/**
 * Chat4 专用样式
 */

/* Chat4 全局变量 */
:root {
  --chat4-primary-color: #4c3c59;
  --chat4-secondary-color: #daff96;
  --chat4-accent-color: #ff004d;
  --chat4-bg-gradient: linear-gradient(180deg, #1f0038 0%, #000 100%);
  --chat4-loading-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --vh: 1vh;
}

/* Chat4 基础容器 */
.chat4-container {
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  position: relative;
  overflow: hidden;
  background: var(--chat4-bg-gradient);
  font-family: 'Work Sans', sans-serif;
}

.chat4-page {
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  overflow: hidden;
}

/* 加载动画 */
.chat4-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: var(--chat4-loading-gradient);
  color: white;
  
  .loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 场景样式 */
.live-scene,
.chat-scene,
.video-call-scene,
.monitor-scene,
.map-scene,
.meetup-scene,
.dancing-scene,
.concert-scene,
.moment-scene,
.diary-scene {
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  position: relative;
  background: var(--chat4-bg-gradient);
}