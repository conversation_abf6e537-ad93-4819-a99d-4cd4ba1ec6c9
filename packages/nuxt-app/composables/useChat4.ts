/**
 * Chat4 Vue Composable
 * 与现有UI组件集成的响应式接口
 * 使用现代化服务架构，零if-else，类型安全
 */

import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import type { Ref, ComputedRef } from 'vue'
import type {
  Chat4Result,
  InitParams,
  MessageContent,
  SceneType,
  ModernFavorabilityState,
  GameState,
  GameService,
  Chat4Config
} from '~/types/chat4'
import {
  createChat4ServiceContainer,
  defaultChat4Config,
  type Chat4ServiceContainer,
  ServiceContainerUtils
} from '~/services/chat4/ServiceFactory'

/**
 * Chat4 Composable 状态接口
 */
interface Chat4ComposableState {
  // 初始化状态
  isInitialized: boolean
  isInitializing: boolean
  initError: string | null
  
  // 游戏状态
  gameState: GameState
  currentScene: SceneType | null
  sessionId: string | null
  
  // 好感度状态
  favorability: ModernFavorabilityState
  
  // 连接状态
  isConnected: boolean
  isReconnecting: boolean
  connectionError: string | null
  
  // 消息状态
  messages: any[]
  isTyping: boolean
  messageError: string | null
  
  // UI状态
  showFriendRequestModal: boolean
  showGiftModal: boolean
  showFavorabilityDrawer: boolean
  showPaymentModal: boolean
  
  // 性能状态
  messageCount: number
  lastActivity: number
}

/**
 * Chat4 Composable 选项
 */
interface UseChat4Options {
  config?: Partial<Chat4Config>
  autoInit?: boolean
  enableDevtools?: boolean
  namespace?: string
}

/**
 * Chat4 Composable 返回值接口
 */
interface UseChat4Return {
  // 状态
  state: Readonly<Chat4ComposableState>
  
  // 计算属性
  isReady: ComputedRef<boolean>
  canSendMessage: ComputedRef<boolean>
  currentSceneName: ComputedRef<string>
  favorabilityProgress: ComputedRef<number>
  connectionStatus: ComputedRef<'connected' | 'disconnected' | 'reconnecting' | 'error'>
  
  // 方法
  initialize: (params: InitParams) => Promise<Chat4Result<void>>
  sendMessage: (content: MessageContent) => Promise<Chat4Result<void>>
  changeScene: (scene: SceneType) => Promise<Chat4Result<void>>
  pauseGame: () => Promise<Chat4Result<void>>
  resumeGame: () => Promise<Chat4Result<void>>
  endGame: (reason?: string) => Promise<Chat4Result<void>>
  continueGame: () => Promise<Chat4Result<void>>
  restartGame: () => Promise<Chat4Result<void>>
  
  // UI操作
  showModal: (modalType: 'friend' | 'gift' | 'favorability' | 'payment') => void
  hideModal: (modalType: 'friend' | 'gift' | 'favorability' | 'payment') => void
  toggleModal: (modalType: 'friend' | 'gift' | 'favorability' | 'payment') => void
  
  // 工具方法
  getGameStats: () => any
  healthCheck: () => Promise<any>
  restart: () => Promise<Chat4Result<void>>
  cleanup: () => Promise<void>
}

/**
 * Chat4 主要 Composable
 * 特性：
 * - 响应式状态管理
 * - 自动服务初始化和清理
 * - 事件驱动的状态更新
 * - 类型安全的API接口
 * - 开发工具集成
 */
export function useChat4(options: UseChat4Options = {}): UseChat4Return {
  // 服务容器
  let serviceContainer: Chat4ServiceContainer | null = null
  let gameService: GameService | null = null
  
  // 响应式状态
  const state = reactive<Chat4ComposableState>({
    // 初始化状态
    isInitialized: false,
    isInitializing: false,
    initError: null,
    
    // 游戏状态
    gameState: 'idle',
    currentScene: null,
    sessionId: null,
    
    // 好感度状态
    favorability: {
      currentValue: 0,
      maxValue: 100,
      level: 1,
      maxLevel: 10,
      progress: 0,
      nextThreshold: 100
    },
    
    // 连接状态
    isConnected: false,
    isReconnecting: false,
    connectionError: null,
    
    // 消息状态
    messages: [],
    isTyping: false,
    messageError: null,
    
    // UI状态
    showFriendRequestModal: false,
    showGiftModal: false,
    showFavorabilityDrawer: false,
    showPaymentModal: false,
    
    // 性能状态
    messageCount: 0,
    lastActivity: Date.now()
  })

  // 事件清理函数
  const eventCleanupFunctions: (() => void)[] = []

  // 计算属性
  const isReady = computed(() => 
    state.isInitialized && !state.isInitializing && !state.initError
  )

  const canSendMessage = computed(() => 
    isReady.value && state.isConnected && ['playing', 'paused'].includes(state.gameState)
  )

  const currentSceneName = computed(() => {
    if (!state.currentScene) return '未知场景'
    
    const sceneNames: Record<string, string> = {
      'Living': '直播间',
      'Phone': '聊天',
      'Video': '视频通话',
      'Monitor': '监控',
      'Map': '地图',
      'Meetup': '约会',
      'MeetupPool': '游泳池约会',
      'MeetupCoffee': '咖啡店约会',
      'MeetupOffice': '办公室约会',
      'MeetupSeaside': '海边约会',
      'Dancing': '舞蹈',
      'Concert': '演唱会',
      'Moment': '朋友圈',
      'Diary': '日记'
    }
    
    return sceneNames[state.currentScene] || state.currentScene
  })

  const favorabilityProgress = computed(() => 
    state.favorability.maxValue > 0 
      ? (state.favorability.currentValue / state.favorability.maxValue) * 100 
      : 0
  )

  const connectionStatus = computed((): 'connected' | 'disconnected' | 'reconnecting' | 'error' => {
    if (state.connectionError) return 'error'
    if (state.isReconnecting) return 'reconnecting'
    if (state.isConnected) return 'connected'
    return 'disconnected'
  })

  /**
   * 初始化游戏
   */
  const initialize = async (params: InitParams): Promise<Chat4Result<void>> => {
    if (state.isInitializing) {
      return { isOk: () => false, isErr: () => true, error: new Error('Already initializing') } as any
    }

    state.isInitializing = true
    state.initError = null

    try {
      // 创建服务容器
      const config = { ...defaultChat4Config, ...options.config }
      serviceContainer = createChat4ServiceContainer({
        config,
        useMockServices: process.env.NODE_ENV === 'development',
        namespace: options.namespace
      })

      // 初始化服务
      const initResult = await serviceContainer.initialize()
      if (initResult.isErr()) {
        state.initError = initResult.error.message
        return initResult
      }

      // 获取游戏服务
      gameService = serviceContainer.getService('gameService')
      if (!gameService) {
        state.initError = 'Game service not available'
        return { isOk: () => false, isErr: () => true, error: new Error('Game service not available') } as any
      }

      // 设置事件监听
      setupEventListeners()

      // 初始化游戏
      const gameInitResult = await gameService.initialize(params)
      if (gameInitResult.isErr()) {
        state.initError = gameInitResult.error.message
        return gameInitResult
      }

      state.isInitialized = true
      state.sessionId = gameService.getGameStats().sessionId
      updateGameState()

      if (process.env.NODE_ENV === 'development') {
        console.log('[useChat4] Initialization completed successfully')
      }

      return { isOk: () => true, isErr: () => false, value: undefined } as any
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      state.initError = errorMessage
      return { isOk: () => false, isErr: () => true, error: new Error(errorMessage) } as any
    } finally {
      state.isInitializing = false
    }
  }

  /**
   * 发送消息
   */
  const sendMessage = async (content: MessageContent): Promise<Chat4Result<void>> => {
    if (!gameService) {
      return { isOk: () => false, isErr: () => true, error: new Error('Game service not initialized') } as any
    }

    state.messageError = null
    state.lastActivity = Date.now()

    try {
      const result = await gameService.sendMessage(content)
      
      if (result.isOk()) {
        state.messageCount++
      } else {
        state.messageError = result.error.message
      }

      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      state.messageError = errorMessage
      return { isOk: () => false, isErr: () => true, error: new Error(errorMessage) } as any
    }
  }

  /**
   * 切换场景
   */
  const changeScene = async (scene: SceneType): Promise<Chat4Result<void>> => {
    if (!gameService) {
      return { isOk: () => false, isErr: () => true, error: new Error('Game service not initialized') } as any
    }

    state.lastActivity = Date.now()
    return gameService.changeScene(scene)
  }

  /**
   * 暂停游戏
   */
  const pauseGame = async (): Promise<Chat4Result<void>> => {
    if (!gameService) {
      return { isOk: () => false, isErr: () => true, error: new Error('Game service not initialized') } as any
    }

    return gameService.pauseGame()
  }

  /**
   * 恢复游戏
   */
  const resumeGame = async (): Promise<Chat4Result<void>> => {
    if (!gameService) {
      return { isOk: () => false, isErr: () => true, error: new Error('Game service not initialized') } as any
    }

    return gameService.resumeGame()
  }

  /**
   * 结束游戏
   */
  const endGame = async (reason = 'user_requested'): Promise<Chat4Result<void>> => {
    if (!gameService) {
      return { isOk: () => false, isErr: () => true, error: new Error('Game service not initialized') } as any
    }

    return gameService.endGame(reason)
  }

  /**
   * 继续游戏（加载历史记录）
   */
  const continueGame = async (): Promise<Chat4Result<void>> => {
    if (!gameService) {
      return { isOk: () => false, isErr: () => true, error: new Error('Game service not initialized') } as any
    }

    state.lastActivity = Date.now()
    return gameService.continueGameFromHistory()
  }

  /**
   * 重新开始游戏（全新游戏）
   */
  const restartGame = async (): Promise<Chat4Result<void>> => {
    if (!gameService) {
      return { isOk: () => false, isErr: () => true, error: new Error('Game service not initialized') } as any
    }

    state.lastActivity = Date.now()
    return gameService.restartGameFromBeginning()
  }

  /**
   * 显示模态框
   */
  const showModal = (modalType: 'friend' | 'gift' | 'favorability' | 'payment'): void => {
    switch (modalType) {
      case 'friend':
        state.showFriendRequestModal = true
        break
      case 'gift':
        state.showGiftModal = true
        break
      case 'favorability':
        state.showFavorabilityDrawer = true
        break
      case 'payment':
        state.showPaymentModal = true
        break
    }
  }

  /**
   * 隐藏模态框
   */
  const hideModal = (modalType: 'friend' | 'gift' | 'favorability' | 'payment'): void => {
    switch (modalType) {
      case 'friend':
        state.showFriendRequestModal = false
        break
      case 'gift':
        state.showGiftModal = false
        break
      case 'favorability':
        state.showFavorabilityDrawer = false
        break
      case 'payment':
        state.showPaymentModal = false
        break
    }
  }

  /**
   * 切换模态框显示状态
   */
  const toggleModal = (modalType: 'friend' | 'gift' | 'favorability' | 'payment'): void => {
    switch (modalType) {
      case 'friend':
        state.showFriendRequestModal = !state.showFriendRequestModal
        break
      case 'gift':
        state.showGiftModal = !state.showGiftModal
        break
      case 'favorability':
        state.showFavorabilityDrawer = !state.showFavorabilityDrawer
        break
      case 'payment':
        state.showPaymentModal = !state.showPaymentModal
        break
    }
  }

  /**
   * 获取游戏统计信息
   */
  const getGameStats = (): any => {
    if (!gameService) return {}
    return gameService.getGameStats()
  }

  /**
   * 健康检查
   */
  const healthCheck = async (): Promise<any> => {
    if (!serviceContainer) return { status: 'no_container' }
    return serviceContainer.healthCheck()
  }

  /**
   * 重启服务
   */
  const restart = async (): Promise<Chat4Result<void>> => {
    if (!serviceContainer) {
      return { isOk: () => false, isErr: () => true, error: new Error('Service container not initialized') } as any
    }

    return serviceContainer.restart()
  }

  /**
   * 清理资源
   */
  const cleanup = async (): Promise<void> => {
    // 清理事件监听
    eventCleanupFunctions.forEach(cleanup => cleanup())
    eventCleanupFunctions.length = 0

    // 清理服务
    if (gameService) {
      await gameService.cleanup()
      gameService = null
    }

    if (serviceContainer) {
      await serviceContainer.cleanup()
      serviceContainer = null
    }

    // 重置状态
    Object.assign(state, {
      isInitialized: false,
      isInitializing: false,
      initError: null,
      gameState: 'idle',
      currentScene: null,
      sessionId: null,
      isConnected: false,
      isReconnecting: false,
      connectionError: null,
      messages: [],
      messageCount: 0
    })

    if (process.env.NODE_ENV === 'development') {
      console.log('[useChat4] Cleanup completed')
    }
  }

  /**
   * 设置事件监听器
   */
  const setupEventListeners = (): void => {
    if (!serviceContainer) return

    const eventBus = serviceContainer.getService('eventBus')
    if (!eventBus) return

    // 游戏状态变化
    const gameStateCleanup = eventBus.subscribe('game:state_changed', (data: any) => {
      state.gameState = data.state
      updateGameState()
    })
    eventCleanupFunctions.push(gameStateCleanup)

    // 场景变化
    const sceneCleanup = eventBus.subscribe('scene:changed', (data: any) => {
      state.currentScene = data.to
      state.lastActivity = Date.now()
    })
    eventCleanupFunctions.push(sceneCleanup)

    // 好感度更新
    const favorabilityCleanup = eventBus.subscribe('favorability:updated', (data: any) => {
      state.favorability = gameService?.getFavorability() || state.favorability
    })
    eventCleanupFunctions.push(favorabilityCleanup)

    // 连接状态
    const connectionCleanup = eventBus.subscribe('connection:established', () => {
      state.isConnected = true
      state.isReconnecting = false
      state.connectionError = null
    })
    eventCleanupFunctions.push(connectionCleanup)

    const disconnectionCleanup = eventBus.subscribe('connection:lost', (data: any) => {
      state.isConnected = false
      state.isReconnecting = data.willReconnect
      state.connectionError = data.reason
    })
    eventCleanupFunctions.push(disconnectionCleanup)

    // 消息相关
    const messageCleanup = eventBus.subscribe('message:received', (data: any) => {
      state.messages.push(data.message)
      state.lastActivity = Date.now()
    })
    eventCleanupFunctions.push(messageCleanup)

    // 支付需求
    const paymentCleanup = eventBus.subscribe('payment:required', () => {
      state.showPaymentModal = true
    })
    eventCleanupFunctions.push(paymentCleanup)

    // 错误处理
    const errorCleanup = eventBus.subscribe('game:error', (data: any) => {
      state.connectionError = data.error
    })
    eventCleanupFunctions.push(errorCleanup)
  }

  /**
   * 更新游戏状态
   */
  const updateGameState = (): void => {
    if (!gameService) return

    const stats = gameService.getGameStats()
    state.currentScene = stats.currentScene
    state.sessionId = stats.sessionId
    state.isConnected = stats.isSSEConnected
    state.messageCount = stats.messageCount
    state.favorability = gameService.getFavorability()
  }

  // 生命周期
  onMounted(() => {
    if (options.autoInit && !state.isInitialized) {
      // 自动初始化逻辑可在这里添加
    }
  })

  onUnmounted(() => {
    cleanup()
  })

  // 返回接口
  return {
    // 状态
    state: readonly(state),
    
    // 计算属性
    isReady,
    canSendMessage,
    currentSceneName,
    favorabilityProgress,
    connectionStatus,
    
    // 方法
    initialize,
    sendMessage,
    changeScene,
    pauseGame,
    resumeGame,
    endGame,
    continueGame,
    restartGame,
    
    // UI操作
    showModal,
    hideModal,
    toggleModal,
    
    // 工具方法
    getGameStats,
    healthCheck,
    restart,
    cleanup
  }
}

/**
 * 轻量级的状态访问 Composable
 * 用于只需要读取状态的组件
 */
export function useChat4State() {
  // 这里可以通过全局状态管理或provide/inject来获取共享状态
  // 暂时返回基础状态结构
  const state = reactive({
    isReady: false,
    gameState: 'idle' as GameState,
    currentScene: null as SceneType | null,
    favorability: {
      currentValue: 0,
      maxValue: 100,
      level: 1,
      maxLevel: 10,
      progress: 0,
      nextThreshold: 100
    } as ModernFavorabilityState,
    isConnected: false
  })

  return {
    state: readonly(state)
  }
}

/**
 * 开发工具 Composable
 * 提供开发和调试功能
 */
export function useChat4DevTools(chat4: ReturnType<typeof useChat4>) {
  const devState = reactive({
    showDevPanel: false,
    logs: [] as any[],
    performance: {
      initTime: 0,
      lastActionTime: 0,
      memoryUsage: 0
    }
  })

  const toggleDevPanel = () => {
    devState.showDevPanel = !devState.showDevPanel
  }

  const addLog = (level: 'info' | 'warn' | 'error', message: string, data?: any) => {
    devState.logs.push({
      level,
      message,
      data,
      timestamp: Date.now()
    })

    // 限制日志数量
    if (devState.logs.length > 100) {
      devState.logs.shift()
    }
  }

  const clearLogs = () => {
    devState.logs.length = 0
  }

  const exportState = () => {
    return {
      chat4State: chat4.state,
      devState: devState,
      gameStats: chat4.getGameStats(),
      timestamp: Date.now()
    }
  }

  // 只在开发环境中启用
  if (process.env.NODE_ENV === 'development') {
    // 监听状态变化并记录日志
    watch(() => chat4.state.gameState, (newState, oldState) => {
      addLog('info', `Game state changed: ${oldState} → ${newState}`)
    })

    watch(() => chat4.state.currentScene, (newScene, oldScene) => {
      addLog('info', `Scene changed: ${oldScene} → ${newScene}`)
    })
  }

  return {
    devState: readonly(devState),
    toggleDevPanel,
    addLog,
    clearLogs,
    exportState
  }
}