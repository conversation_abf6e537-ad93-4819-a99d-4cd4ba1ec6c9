/**
 * Chat4事件管理Composable
 * 处理Chat4的各种事件：用户交互、场景切换、WebSocket事件等
 */

import type { Chat4SceneState, SceneChangeEvent } from '~/types/chat4'
import { useChat4Store } from '~/stores/chat4'

// 定义事件类型
export interface Chat4Events {
  // 场景事件
  'scene:change': SceneChangeEvent
  'scene:transition:start': { scene: Chat4SceneState }
  'scene:transition:end': { scene: Chat4SceneState }
  
  // 消息事件
  'message:sent': { message: any }
  'message:received': { message: any }
  'comment:added': { comment: any }
  
  // 互动事件
  'heart:click': { count: number }
  'gift:sent': { gift: any, quantity: number }
  'favorability:changed': { value: number }
  
  // UI事件
  'modal:open': { modal: string }
  'modal:close': { modal: string }
  'drawer:open': { drawer: string }
  'drawer:close': { drawer: string }
  
  // 连接事件
  'websocket:connected': { timestamp: number }
  'websocket:disconnected': { timestamp: number }
  'websocket:error': { error: string }
  
  // 游戏事件
  'game:initialized': { characterId: string, storyId: string }
  'game:ended': { reason: string }
  'game:paused': { timestamp: number }
  'game:resumed': { timestamp: number }
}

export const useChat4Events = () => {
  const chat4Store = useChat4Store()
  
  // 创建事件总线
  const eventBus = ref<any>(null)
  
  // 初始化事件总线
  const initEventBus = async () => {
    if (process.client && !eventBus.value) {
      const { default: mitt } = await import('mitt')
      eventBus.value = mitt<Chat4Events>()
      
      console.log('🎯 Chat4事件总线初始化完成')
    }
  }
  
  // 事件发射器
  const emit = <T extends keyof Chat4Events>(
    type: T, 
    data: Chat4Events[T]
  ) => {
    if (eventBus.value) {
      eventBus.value.emit(type, data)
    }
    
    // 同时触发原生DOM事件
    if (process.client) {
      const customEvent = new CustomEvent(`chat4:${type}`, {
        detail: data,
        bubbles: true
      })
      window.dispatchEvent(customEvent)
    }
    
    console.log(`🎯 发射事件 ${type}:`, data)
  }
  
  // 事件监听器
  const on = <T extends keyof Chat4Events>(
    type: T,
    handler: (data: Chat4Events[T]) => void
  ) => {
    if (eventBus.value) {
      eventBus.value.on(type, handler)
    }
    
    // 返回取消监听的函数
    return () => {
      if (eventBus.value) {
        eventBus.value.off(type, handler)
      }
    }
  }
  
  // 一次性事件监听器
  const once = <T extends keyof Chat4Events>(
    type: T,
    handler: (data: Chat4Events[T]) => void
  ) => {
    if (eventBus.value) {
      const wrappedHandler = (data: Chat4Events[T]) => {
        handler(data)
        eventBus.value.off(type, wrappedHandler)
      }
      eventBus.value.on(type, wrappedHandler)
      
      return () => eventBus.value.off(type, wrappedHandler)
    }
    return () => {}
  }
  
  // 移除事件监听器
  const off = <T extends keyof Chat4Events>(
    type: T,
    handler?: (data: Chat4Events[T]) => void
  ) => {
    if (eventBus.value) {
      if (handler) {
        eventBus.value.off(type, handler)
      } else {
        eventBus.value.all.clear()
      }
    }
  }
  
  // 场景事件处理器
  const sceneEvents = {
    // 监听场景切换
    onSceneChange: (callback: (event: SceneChangeEvent) => void) => {
      return on('scene:change', callback)
    },
    
    // 触发场景切换事件
    emitSceneChange: (from: Chat4SceneState, to: Chat4SceneState) => {
      const event: SceneChangeEvent = {
        from,
        to,
        timestamp: Date.now()
      }
      emit('scene:change', event)
    },
    
    // 监听场景转场开始
    onTransitionStart: (callback: (data: { scene: Chat4SceneState }) => void) => {
      return on('scene:transition:start', callback)
    },
    
    // 监听场景转场结束
    onTransitionEnd: (callback: (data: { scene: Chat4SceneState }) => void) => {
      return on('scene:transition:end', callback)
    }
  }
  
  // 消息事件处理器
  const messageEvents = {
    // 监听消息发送
    onMessageSent: (callback: (data: { message: any }) => void) => {
      return on('message:sent', callback)
    },
    
    // 监听消息接收
    onMessageReceived: (callback: (data: { message: any }) => void) => {
      return on('message:received', callback)
    },
    
    // 监听评论添加
    onCommentAdded: (callback: (data: { comment: any }) => void) => {
      return on('comment:added', callback)
    },
    
    // 触发消息发送事件
    emitMessageSent: (message: any) => {
      emit('message:sent', { message })
    },
    
    // 触发评论添加事件
    emitCommentAdded: (comment: any) => {
      emit('comment:added', { comment })
    }
  }
  
  // 互动事件处理器
  const interactionEvents = {
    // 监听心形点击
    onHeartClick: (callback: (data: { count: number }) => void) => {
      return on('heart:click', callback)
    },
    
    // 监听礼物发送
    onGiftSent: (callback: (data: { gift: any, quantity: number }) => void) => {
      return on('gift:sent', callback)
    },
    
    // 监听好感度变化
    onFavorabilityChanged: (callback: (data: { value: number }) => void) => {
      return on('favorability:changed', callback)
    },
    
    // 触发心形点击事件
    emitHeartClick: (count: number) => {
      emit('heart:click', { count })
    },
    
    // 触发礼物发送事件
    emitGiftSent: (gift: any, quantity: number) => {
      emit('gift:sent', { gift, quantity })
    }
  }
  
  // UI事件处理器
  const uiEvents = {
    // 监听模态框打开
    onModalOpen: (callback: (data: { modal: string }) => void) => {
      return on('modal:open', callback)
    },
    
    // 监听模态框关闭
    onModalClose: (callback: (data: { modal: string }) => void) => {
      return on('modal:close', callback)
    },
    
    // 触发模态框打开事件
    emitModalOpen: (modal: string) => {
      emit('modal:open', { modal })
    },
    
    // 触发模态框关闭事件
    emitModalClose: (modal: string) => {
      emit('modal:close', { modal })
    }
  }
  
  // WebSocket事件处理器
  const websocketEvents = {
    // 监听连接建立
    onConnected: (callback: (data: { timestamp: number }) => void) => {
      return on('websocket:connected', callback)
    },
    
    // 监听连接断开
    onDisconnected: (callback: (data: { timestamp: number }) => void) => {
      return on('websocket:disconnected', callback)
    },
    
    // 监听连接错误
    onError: (callback: (data: { error: string }) => void) => {
      return on('websocket:error', callback)
    },
    
    // 触发连接事件
    emitConnected: () => {
      emit('websocket:connected', { timestamp: Date.now() })
    },
    
    // 触发断开事件
    emitDisconnected: () => {
      emit('websocket:disconnected', { timestamp: Date.now() })
    },
    
    // 触发错误事件
    emitError: (error: string) => {
      emit('websocket:error', { error })
    }
  }
  
  // 游戏事件处理器
  const gameEvents = {
    // 监听游戏初始化
    onGameInitialized: (callback: (data: { characterId: string, storyId: string }) => void) => {
      return on('game:initialized', callback)
    },
    
    // 监听游戏结束
    onGameEnded: (callback: (data: { reason: string }) => void) => {
      return on('game:ended', callback)
    },
    
    // 触发游戏初始化事件
    emitGameInitialized: (characterId: string, storyId: string) => {
      emit('game:initialized', { characterId, storyId })
    },
    
    // 触发游戏结束事件
    emitGameEnded: (reason: string) => {
      emit('game:ended', { reason })
    }
  }
  
  // 事件历史记录
  const eventHistory = ref<Array<{ type: string, data: any, timestamp: number }>>([])
  
  // 记录事件历史
  const recordEvent = <T extends keyof Chat4Events>(
    type: T,
    data: Chat4Events[T]
  ) => {
    eventHistory.value.push({
      type: type as string,
      data,
      timestamp: Date.now()
    })
    
    // 限制历史记录数量
    if (eventHistory.value.length > 100) {
      eventHistory.value.shift()
    }
  }
  
  // 获取事件历史
  const getEventHistory = (type?: string, limit: number = 50) => {
    let history = eventHistory.value
    
    if (type) {
      history = history.filter(event => event.type === type)
    }
    
    return history.slice(-limit)
  }
  
  // 清空事件历史
  const clearEventHistory = () => {
    eventHistory.value = []
  }
  
  // 自动设置事件监听器（基于当前状态）
  const setupAutoListeners = () => {
    // 监听store状态变化并触发对应事件
    watch(
      () => chat4Store.currentScene,
      (newScene, oldScene) => {
        if (oldScene && newScene !== oldScene) {
          sceneEvents.emitSceneChange(oldScene, newScene)
        }
      }
    )
    
    watch(
      () => chat4Store.isTransitioning,
      (isTransitioning) => {
        const scene = chat4Store.currentScene
        if (isTransitioning) {
          emit('scene:transition:start', { scene })
        } else {
          emit('scene:transition:end', { scene })
        }
      }
    )
    
    watch(
      () => chat4Store.heartClickCount,
      (count) => {
        interactionEvents.emitHeartClick(count)
      }
    )
    
    console.log('🎯 Chat4自动事件监听器设置完成')
  }
  
  // 初始化所有事件系统
  const initialize = async () => {
    await initEventBus()
    setupAutoListeners()
    
    console.log('🎯 Chat4事件系统初始化完成')
  }
  
  // 清理所有事件监听器
  const cleanup = () => {
    if (eventBus.value) {
      eventBus.value.all.clear()
    }
    clearEventHistory()
    
    console.log('🎯 Chat4事件系统清理完成')
  }
  
  return {
    // 初始化和清理
    initialize,
    cleanup,
    
    // 基础事件方法
    emit,
    on,
    once,
    off,
    
    // 分类事件处理器
    sceneEvents,
    messageEvents,
    interactionEvents,
    uiEvents,
    websocketEvents,
    gameEvents,
    
    // 事件历史
    eventHistory: readonly(eventHistory),
    recordEvent,
    getEventHistory,
    clearEventHistory
  }
}