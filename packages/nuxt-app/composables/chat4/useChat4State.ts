/**
 * Chat4状态管理Composable
 * 兼容性包装器 - 委托给现代化的 useChat4 composable
 * @deprecated 建议直接使用 useChat4 composable
 */

import type { Chat4SceneState } from '~/types/chat4'
import { Chat4SceneUtils } from '~/types/chat4'

/**
 * 兼容性包装器
 * 为了保持向后兼容，提供基础的状态访问
 * 实际应用中建议直接使用 useChat4 composable
 */
export const useChat4State = () => {
  console.warn('⚠️ useChat4State 已被废弃，建议直接使用 useChat4 composable')

  // 基础状态 - 静态默认值
  const currentScene = ref<Chat4SceneState>('Living')

  // 返回基础状态结构
  const state = computed(() => ({
    currentScene: currentScene.value,
    isTransitioning: false,
    isLoading: false,
    currentActor: null,
    currentStory: null,
    isConnected: false,
    heartClickCount: 0,
    viewerCount: 128,
  }))

  // 场景状态检查
  const sceneChecks = computed(() => ({
    isLivingScene: Chat4SceneUtils.isLivingScene(currentScene.value),
    isPhoneScene: Chat4SceneUtils.isPhoneScene(currentScene.value),
    isVideoCallScene: Chat4SceneUtils.isVideoCallScene(currentScene.value),
    isMonitorScene: Chat4SceneUtils.isMonitorScene(currentScene.value),
    isMapScene: Chat4SceneUtils.isMapScene(currentScene.value),
    isMeetupScene: Chat4SceneUtils.isMeetupScene(currentScene.value),
    isDancingScene: Chat4SceneUtils.isDancingScene(currentScene.value),
    isConcertScene: Chat4SceneUtils.isConcertScene(currentScene.value),
    isMomentScene: Chat4SceneUtils.isMomentScene(currentScene.value),
    isDiaryScene: Chat4SceneUtils.isDiaryScene(currentScene.value),
  }))

  // UI状态管理 - 基础实现
  const showFriendRequestModal = ref(false)
  const showGiftModal = ref(false)
  const showFavorabilityDrawer = ref(false)
  const showPaymentModal = ref(false)

  const uiState = computed(() => ({
    showFriendRequestModal: showFriendRequestModal.value,
    showGiftModal: showGiftModal.value,
    showFavorabilityDrawer: showFavorabilityDrawer.value,
    showPaymentModal: showPaymentModal.value,
  }))

  // 场景切换方法 - 简化实现
  const sceneNavigation = {
    setScene: (sceneId: Chat4SceneState) => {
      console.log('🔄 场景切换 (兼容模式):', sceneId)
      currentScene.value = sceneId
    },
    goToLive: () => sceneNavigation.setScene('Living'),
    goToChat: () => sceneNavigation.setScene('Phone'),
    goToVideo: () => sceneNavigation.setScene('VideoCall'),
    goToMap: () => sceneNavigation.setScene('Map'),
    goToMeetup: () => sceneNavigation.setScene('Meetup'),
    goToDancing: () => sceneNavigation.setScene('Dancing'),
    goToConcert: () => sceneNavigation.setScene('Concert'),
    goToMoment: () => sceneNavigation.setScene('Moment'),
    goToDiary: () => sceneNavigation.setScene('Diary'),
    goToMonitor: () => sceneNavigation.setScene('Monitor'),
  }

  // 游戏状态管理 - 空实现
  const gameManagement = {
    initGame: async (characterId: string, storyId: string) => {
      console.warn('⚠️ initGame 在兼容模式下不执行，请使用 useChat4.initialize')
      return Promise.resolve()
    },
    resetAll: () => {
      console.warn('⚠️ resetAll 在兼容模式下不执行')
    },
    isGameReady: computed(() => false),
  }

  // UI控制方法 - 基础实现
  const uiControls = {
    showFriendRequest: () => {
      showFriendRequestModal.value = true
    },
    openGiftModal: () => {
      showGiftModal.value = true
    },
    incrementHeart: () => {
      console.log('💖 心形点击 (兼容模式)')
    },
    toggleModal: (modalName: string, value?: boolean) => {
      switch (modalName) {
        case 'friendRequest':
          showFriendRequestModal.value = value ?? !showFriendRequestModal.value
          break
        case 'gift':
          showGiftModal.value = value ?? !showGiftModal.value
          break
        case 'favorability':
          showFavorabilityDrawer.value = value ?? !showFavorabilityDrawer.value
          break
        case 'payment':
          showPaymentModal.value = value ?? !showPaymentModal.value
          break
      }
    },
  }

  // 场景显示名称
  const currentSceneDisplayName = computed(() =>
    Chat4SceneUtils.getSceneDisplayName(currentScene.value),
  )

  // 场景变化监听器
  const watchSceneChange = (
    callback: (newScene: Chat4SceneState, oldScene: Chat4SceneState) => void,
  ) => {
    return watch(
      currentScene,
      (newScene, oldScene) => {
        if (newScene !== oldScene) {
          callback(newScene, oldScene)
        }
      },
      { immediate: false },
    )
  }

  // 连接状态监听器
  const watchConnectionState = (callback: (isConnected: boolean) => void) => {
    // 兼容模式下总是返回 false
    callback(false)
    return () => {} // 返回空的清理函数
  }

  return {
    // 状态数据
    state,
    sceneChecks,
    uiState,
    currentSceneDisplayName,

    // 操作方法
    sceneNavigation,
    gameManagement,
    uiControls,

    // 监听器
    watchSceneChange,
    watchConnectionState,
  }
}
