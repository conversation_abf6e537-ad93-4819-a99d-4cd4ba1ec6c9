/**
 * Chat4状态管理Composable
 * 封装Chat4状态操作的便捷方法
 */

import type { Chat4SceneState } from '~/types/chat4'
import { Chat4SceneUtils } from '~/types/chat4'
import { useChat4Store } from '~/stores/chat4'

export const useChat4State = () => {
  const chat4Store = useChat4Store()
  
  // 状态访问器
  const state = computed(() => ({
    currentScene: chat4Store.currentScene,
    isTransitioning: chat4Store.isTransitioning,
    isLoading: chat4Store.isLoading,
    currentActor: chat4Store.currentActor,
    currentStory: chat4Store.currentStory,
    isConnected: chat4Store.isConnected,
    heartClickCount: chat4Store.heartClickCount,
    viewerCount: chat4Store.viewerCount
  }))
  
  // 场景状态检查
  const sceneChecks = computed(() => ({
    isLivingScene: chat4Store.isLivingScene,
    isPhoneScene: Chat4SceneUtils.isPhoneScene(chat4Store.currentScene),
    isVideoCallScene: Chat4SceneUtils.isVideoCallScene(chat4Store.currentScene),
    isMonitorScene: Chat4SceneUtils.isMonitorScene(chat4Store.currentScene),
    isMapScene: Chat4SceneUtils.isMapScene(chat4Store.currentScene),
    isMeetupScene: Chat4SceneUtils.isMeetupScene(chat4Store.currentScene),
    isDancingScene: Chat4SceneUtils.isDancingScene(chat4Store.currentScene),
    isConcertScene: Chat4SceneUtils.isConcertScene(chat4Store.currentScene),
    isMomentScene: Chat4SceneUtils.isMomentScene(chat4Store.currentScene),
    isDiaryScene: Chat4SceneUtils.isDiaryScene(chat4Store.currentScene)
  }))
  
  // UI状态管理
  const uiState = computed(() => ({
    showFriendRequestModal: chat4Store.showFriendRequestModal,
    showGiftModal: chat4Store.showGiftModal,
    showFavorabilityDrawer: chat4Store.showFavorabilityDrawer,
    showPaymentModal: chat4Store.showPaymentModal
  }))
  
  // 场景切换方法
  const sceneNavigation = {
    setScene: (sceneId: Chat4SceneState) => chat4Store.setCurrentScene(sceneId),
    goToLive: () => chat4Store.goToLive(),
    goToChat: () => chat4Store.goToChat(),
    goToVideo: () => chat4Store.goToVideo(),
    goToMap: () => chat4Store.goToMap(),
    goToMeetup: () => chat4Store.goToMeetup(),
    goToDancing: () => chat4Store.goToDancing(),
    goToConcert: () => chat4Store.goToConcert(),
    goToMoment: () => chat4Store.goToMoment(),
    goToDiary: () => chat4Store.goToDiary(),
    goToMonitor: () => chat4Store.goToMonitor()
  }
  
  // 游戏状态管理
  const gameManagement = {
    initGame: async (characterId: string, storyId: string) => {
      return await chat4Store.initGame(characterId, storyId)
    },
    resetAll: () => chat4Store.resetAllState(),
    isGameReady: computed(() => 
      !!(chat4Store.currentActor && chat4Store.currentStory)
    )
  }
  
  // UI控制方法
  const uiControls = {
    showFriendRequest: () => chat4Store.showFriendRequest(),
    openGiftModal: () => chat4Store.openGiftModal(),
    incrementHeart: () => chat4Store.incrementHeartClick(),
    toggleModal: (modalName: string, value?: boolean) => {
      switch (modalName) {
        case 'friendRequest':
          chat4Store.showFriendRequestModal = value ?? !chat4Store.showFriendRequestModal
          break
        case 'gift':
          chat4Store.showGiftModal = value ?? !chat4Store.showGiftModal
          break
        case 'favorability':
          chat4Store.showFavorabilityDrawer = value ?? !chat4Store.showFavorabilityDrawer
          break
        case 'payment':
          chat4Store.showPaymentModal = value ?? !chat4Store.showPaymentModal
          break
      }
    }
  }
  
  // 场景显示名称
  const currentSceneDisplayName = computed(() => 
    chat4Store.currentSceneDisplayName
  )
  
  // 场景变化监听器
  const watchSceneChange = (callback: (newScene: Chat4SceneState, oldScene: Chat4SceneState) => void) => {
    return watch(
      () => chat4Store.currentScene,
      (newScene, oldScene) => {
        if (newScene !== oldScene) {
          callback(newScene, oldScene)
        }
      },
      { immediate: false }
    )
  }
  
  // 连接状态监听器
  const watchConnectionState = (callback: (isConnected: boolean) => void) => {
    return watch(
      () => chat4Store.isConnected,
      callback,
      { immediate: true }
    )
  }
  
  return {
    // 状态数据
    state,
    sceneChecks,
    uiState,
    currentSceneDisplayName,
    
    // 操作方法
    sceneNavigation,
    gameManagement,
    uiControls,
    
    // 监听器
    watchSceneChange,
    watchConnectionState
  }
}