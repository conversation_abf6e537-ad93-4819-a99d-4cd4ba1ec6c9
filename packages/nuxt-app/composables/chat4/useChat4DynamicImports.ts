/**
 * Chat4动态导入管理Composable
 * 按需加载Chat4所需的第三方库，优化初始加载性能
 */

// 缓存已加载的模块
const moduleCache = new Map<string, any>()

// 加载状态管理
const loadingStates = reactive<Record<string, boolean>>({})

export const useChat4DynamicImports = () => {
  
  // 通用动态导入方法
  const dynamicImport = async <T = any>(
    moduleName: string,
    importFn: () => Promise<any>,
    cacheKey?: string
  ): Promise<T | null> => {
    if (!process.client) {
      console.warn(`⚠️ ${moduleName} 只能在客户端加载`)
      return null
    }
    
    const key = cacheKey || moduleName
    
    // 检查缓存
    if (moduleCache.has(key)) {
      console.log(`📦 从缓存加载 ${moduleName}`)
      return moduleCache.get(key)
    }
    
    // 检查是否正在加载
    if (loadingStates[key]) {
      console.log(`⏳ ${moduleName} 正在加载中...`)
      // 等待加载完成
      while (loadingStates[key] && !moduleCache.has(key)) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      return moduleCache.get(key) || null
    }
    
    try {
      loadingStates[key] = true
      console.log(`📦 开始加载 ${moduleName}`)
      
      const module = await importFn()
      const result = module.default || module
      
      // 缓存结果
      moduleCache.set(key, result)
      console.log(`✅ ${moduleName} 加载成功`)
      
      return result
    } catch (error) {
      console.error(`❌ ${moduleName} 加载失败:`, error)
      return null
    } finally {
      loadingStates[key] = false
    }
  }
  
  // 动画库导入
  const animationLibs = {
    // GSAP动画库（新增）
    gsap: () => dynamicImport(
      'GSAP',
      async () => {
        const module = await import('gsap')
        return {
          gsap: module.gsap || module.default,
          // 可以根据需要添加其他GSAP插件
          // ScrollTrigger: module.ScrollTrigger,
          // TextPlugin: module.TextPlugin
        }
      },
      'gsap'
    ),
    
    // AnimeJS动画库
    anime: () => dynamicImport(
      'AnimeJS',
      () => import('animejs'),
      'anime'
    ),
    
    // Canvas Confetti五彩纸屑效果
    confetti: () => dynamicImport(
      'Canvas Confetti',
      () => import('canvas-confetti'),
      'confetti'
    ),
    
    // Motion动画库
    motion: () => dynamicImport(
      'Motion',
      () => import('motion'),
      'motion'
    ),
    
    // 批量加载所有动画库
    loadAll: async () => {
      const [gsap, anime, confetti, motion] = await Promise.all([
        animationLibs.gsap(),
        animationLibs.anime(),
        animationLibs.confetti(),
        animationLibs.motion()
      ])
      
      return {
        gsap,
        anime,
        confetti,
        motion
      }
    }
  }
  
  // 媒体库导入
  const mediaLibs = {
    // Howler音频库
    howler: () => dynamicImport(
      'Howler',
      async () => {
        const module = await import('howler')
        return {
          Howl: module.Howl,
          Howler: module.Howler
        }
      },
      'howler'
    ),
    
    // 弹幕库
    danmaku: () => dynamicImport(
      'Danmaku',
      () => import('danmaku'),
      'danmaku'
    ),
    
    // 批量加载所有媒体库
    loadAll: async () => {
      const [howler, danmaku] = await Promise.all([
        mediaLibs.howler(),
        mediaLibs.danmaku()
      ])
      
      return {
        howler,
        danmaku
      }
    }
  }
  
  // 工具库导入
  const utilLibs = {
    // DayJS日期库
    dayjs: () => dynamicImport(
      'DayJS',
      () => import('dayjs'),
      'dayjs'
    ),
    
    // NanoID唯一ID生成
    nanoid: () => dynamicImport(
      'NanoID',
      async () => {
        const module = await import('nanoid')
        return {
          nanoid: module.nanoid,
          customAlphabet: module.customAlphabet
        }
      },
      'nanoid'
    ),
    
    // Mitt事件总线
    mitt: () => dynamicImport(
      'Mitt',
      () => import('mitt'),
      'mitt'
    ),
    
    // Lodash-ES工具库
    lodash: () => dynamicImport(
      'Lodash-ES',
      () => import('lodash-es'),
      'lodash'
    ),
    
    // UUID生成库
    uuid: () => dynamicImport(
      'UUID',
      async () => {
        const module = await import('uuid')
        return {
          v4: module.v4,
          v5: module.v5,
          validate: module.validate
        }
      },
      'uuid'
    ),
    
    // 批量加载所有工具库
    loadAll: async () => {
      const [dayjs, nanoid, mitt, lodash, uuid] = await Promise.all([
        utilLibs.dayjs(),
        utilLibs.nanoid(),
        utilLibs.mitt(),
        utilLibs.lodash(),
        utilLibs.uuid()
      ])
      
      return {
        dayjs,
        nanoid,
        mitt,
        lodash,
        uuid
      }
    }
  }
  
  // 场景特定的库加载器
  const sceneLoaders = {
    // 直播场景所需库
    living: async () => {
      console.log('📦 加载直播场景依赖...')
      const [confetti, danmaku, mitt] = await Promise.all([
        animationLibs.confetti(),
        mediaLibs.danmaku(),
        utilLibs.mitt()
      ])
      
      return { confetti, danmaku, mitt }
    },
    
    // 聊天场景所需库
    phone: async () => {
      console.log('📦 加载聊天场景依赖...')
      const [dayjs, uuid] = await Promise.all([
        utilLibs.dayjs(),
        utilLibs.uuid()
      ])
      
      return { dayjs, uuid }
    },
    
    // 视频通话场景所需库
    video: async () => {
      console.log('📦 加载视频通话场景依赖...')
      const howler = await mediaLibs.howler()
      
      return { howler }
    },
    
    // 舞蹈场景所需库
    dancing: async () => {
      console.log('📦 加载舞蹈场景依赖...')
      const [gsap, anime, motion, howler] = await Promise.all([
        animationLibs.gsap(),
        animationLibs.anime(),
        animationLibs.motion(),
        mediaLibs.howler()
      ])
      
      return { gsap, anime, motion, howler }
    },
    
    // 演唱会场景所需库
    concert: async () => {
      console.log('📦 加载演唱会场景依赖...')
      const [gsap, anime, confetti, howler, danmaku] = await Promise.all([
        animationLibs.gsap(),
        animationLibs.anime(),
        animationLibs.confetti(),
        mediaLibs.howler(),
        mediaLibs.danmaku()
      ])
      
      return { gsap, anime, confetti, howler, danmaku }
    },
    
    // 背景组件所需库（新增）
    background: async () => {
      console.log('📦 加载背景组件依赖...')
      const gsap = await animationLibs.gsap()
      
      return { gsap }
    }
  }
  
  // 预加载策略
  const preloader = {
    // 预加载核心库（在空闲时间）
    preloadCore: async () => {
      if (!process.client) return
      
      console.log('🚀 开始预加载核心库...')
      
      // 使用requestIdleCallback在空闲时预加载
      const preloadTask = async () => {
        await Promise.all([
          utilLibs.mitt(),
          utilLibs.dayjs(),
          utilLibs.uuid()
        ])
        console.log('✅ 核心库预加载完成')
      }
      
      if ('requestIdleCallback' in window) {
        window.requestIdleCallback(() => preloadTask())
      } else {
        // 降级到setTimeout
        setTimeout(preloadTask, 1000)
      }
    },
    
    // 根据当前场景预加载相关库
    preloadForScene: async (sceneName: string) => {
      const loader = sceneLoaders[sceneName as keyof typeof sceneLoaders]
      if (loader) {
        console.log(`🎯 预加载场景 ${sceneName} 的依赖...`)
        await loader()
      }
    }
  }
  
  // 获取加载状态
  const getLoadingState = (moduleName: string) => {
    return computed(() => loadingStates[moduleName] || false)
  }
  
  // 获取所有加载状态
  const getAllLoadingStates = () => {
    return computed(() => ({ ...loadingStates }))
  }
  
  // 检查模块是否已加载
  const isLoaded = (cacheKey: string) => {
    return moduleCache.has(cacheKey)
  }
  
  // 获取已加载的模块
  const getLoadedModule = <T = any>(cacheKey: string): T | null => {
    return moduleCache.get(cacheKey) || null
  }
  
  // 清理缓存
  const clearCache = (cacheKey?: string) => {
    if (cacheKey) {
      moduleCache.delete(cacheKey)
      console.log(`🗑️ 清理模块缓存: ${cacheKey}`)
    } else {
      moduleCache.clear()
      console.log('🗑️ 清理所有模块缓存')
    }
  }
  
  // 获取缓存统计
  const getCacheStats = () => {
    return {
      size: moduleCache.size,
      keys: Array.from(moduleCache.keys()),
      loadingCount: Object.values(loadingStates).filter(Boolean).length
    }
  }
  
  return {
    // 分类导入器
    animationLibs,
    mediaLibs,
    utilLibs,
    sceneLoaders,
    
    // 预加载器
    preloader,
    
    // 状态管理
    getLoadingState,
    getAllLoadingStates,
    isLoaded,
    getLoadedModule,
    
    // 缓存管理
    clearCache,
    getCacheStats,
    
    // 通用导入方法
    dynamicImport
  }
}