/**
 * Chat4消息管理Composable
 * 兼容性包装器 - 委托给现代化的 useChat4 composable
 * @deprecated 建议直接使用 useChat4 composable 的消息功能
 */

import type { ChatMessage, LiveComment, GiftItem } from '~/types/chat4'

export const useChat4Messages = () => {
  console.warn('⚠️ useChat4Messages 已被废弃，建议直接使用 useChat4 composable')

  // 基础消息数据 - 空数组
  const messages = ref<ChatMessage[]>([])
  const liveComments = ref<LiveComment[]>([])

  // 消息数据访问器
  const messageData = computed(() => ({
    messages: messages.value,
    liveComments: liveComments.value,
    messageCount: messages.value.length,
    commentCount: liveComments.value.length,
  }))

  // 消息发送方法 - 兼容性实现
  const sendMessage = {
    // 发送文本消息
    text: (content: string, type: ChatMessage['type'] = 'text') => {
      console.warn(
        '⚠️ sendMessage.text 在兼容模式下不执行，请使用 useChat4.sendMessage',
      )
      console.log('💬 发送消息 (兼容模式):', { type, content })
    },

    // 发送系统消息
    system: (content: string) => {
      console.warn('⚠️ sendMessage.system 在兼容模式下不执行')
      console.log('🔧 系统消息 (兼容模式):', content)
    },

    // 发送礼物消息
    gift: (gift: GiftItem, quantity: number = 1) => {
      console.warn('⚠️ sendMessage.gift 在兼容模式下不执行')
      console.log('🎁 礼物消息 (兼容模式):', { gift: gift.title, quantity })
    },

    // 发送图片消息
    image: (imageUrl: string, content?: string) => {
      console.warn('⚠️ sendMessage.image 在兼容模式下不执行')
      console.log('🖼️ 图片消息 (兼容模式):', { imageUrl, content })
    },

    // 发送表情消息
    emoji: (emoji: string) => {
      console.warn('⚠️ sendMessage.emoji 在兼容模式下不执行')
      console.log('😊 表情消息 (兼容模式):', emoji)
    },
  }

  // 直播评论方法 - 兼容性实现
  const liveComment = {
    // 添加普通评论
    add: (content: string, username?: string) => {
      console.warn('⚠️ liveComment.add 在兼容模式下不执行')
      console.log('💬 直播评论 (兼容模式):', { content, username })
    },

    // 添加入场消息
    enter: (username: string) => {
      console.warn('⚠️ liveComment.enter 在兼容模式下不执行')
      console.log('👋 入场消息 (兼容模式):', username)
    },

    // 添加礼物评论
    gift: (username: string, giftName: string, quantity: number) => {
      console.warn('⚠️ liveComment.gift 在兼容模式下不执行')
      console.log('🎁 礼物评论 (兼容模式):', { username, giftName, quantity })
    },

    // 添加关注消息
    follow: (username: string) => {
      console.warn('⚠️ liveComment.follow 在兼容模式下不执行')
      console.log('❤️ 关注消息 (兼容模式):', username)
    },
  }

  // 基础统计 - 兼容性实现
  const messageStats = computed(() => ({
    total: 0,
    text: 0,
    gift: 0,
    system: 0,
    image: 0,
    emoji: 0,
  }))

  const commentStats = computed(() => ({
    total: 0,
    comment: 0,
    gift: 0,
    enter: 0,
    follow: 0,
  }))

  // 简化的工具方法
  const messageFormatter = {
    formatTime: (timestamp: number) => {
      const date = new Date(timestamp)
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
      })
    },
    formatContent: (message: ChatMessage) => message.content,
    formatComment: (comment: LiveComment) =>
      `${comment.username}: ${comment.content}`,
  }

  return {
    // 数据访问
    messageData,
    messageStats,
    commentStats,

    // 发送方法
    sendMessage,
    liveComment,

    // 工具方法
    messageFormatter,
  }
}
