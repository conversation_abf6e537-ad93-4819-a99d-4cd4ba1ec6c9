/**
 * Chat4消息管理Composable
 * 处理聊天消息、直播评论、礼物等消息相关功能
 */

import type { ChatMessage, LiveComment, GiftItem } from '~/types/chat4'
import { useChat4Store } from '~/stores/chat4'

export const useChat4Messages = () => {
  const chat4Store = useChat4Store()
  
  // 消息数据访问器
  const messageData = computed(() => ({
    messages: chat4Store.messages,
    liveComments: chat4Store.liveComments,
    messageCount: chat4Store.messages.length,
    commentCount: chat4Store.liveComments.length
  }))
  
  // 消息发送方法
  const sendMessage = {
    // 发送文本消息
    text: (content: string, type: ChatMessage['type'] = 'text') => {
      chat4Store.addMessage({
        type,
        content,
        sender: 'user'
      })
      
      console.log('💬 发送消息:', { type, content })
    },
    
    // 发送系统消息
    system: (content: string) => {
      chat4Store.addMessage({
        type: 'system',
        content,
        sender: 'system'
      })
    },
    
    // 发送礼物消息
    gift: (gift: GiftItem, quantity: number = 1) => {
      chat4Store.sendGift(gift, quantity)
    },
    
    // 发送图片消息
    image: (imageUrl: string, content?: string) => {
      chat4Store.addMessage({
        type: 'image',
        content: content || '发送了一张图片',
        imageUrl,
        sender: 'user'
      })
    },
    
    // 发送表情消息
    emoji: (emoji: string) => {
      chat4Store.addMessage({
        type: 'emoji',
        content: emoji,
        sender: 'user'
      })
    }
  }
  
  // 直播评论方法
  const liveComment = {
    // 添加普通评论
    add: (content: string, username?: string) => {
      chat4Store.addLiveComment({
        content,
        username: username || '观众',
        type: 'comment'
      })
    },
    
    // 添加入场消息
    enter: (username: string) => {
      chat4Store.addLiveComment({
        content: `${username} 进入了直播间`,
        username,
        type: 'enter'
      })
    },
    
    // 添加礼物评论
    gift: (username: string, giftName: string, quantity: number) => {
      chat4Store.addLiveComment({
        content: `送出了 ${quantity} 个 ${giftName}`,
        username,
        type: 'gift'
      })
    },
    
    // 添加关注消息
    follow: (username: string) => {
      chat4Store.addLiveComment({
        content: `${username} 关注了主播`,
        username,
        type: 'follow'
      })
    }
  }
  
  // 消息过滤器
  const messageFilters = {
    // 按类型过滤消息
    byType: (type: ChatMessage['type']) => {
      return computed(() => 
        chat4Store.messages.filter(msg => msg.type === type)
      )
    },
    
    // 按发送者过滤消息
    bySender: (sender: ChatMessage['sender']) => {
      return computed(() => 
        chat4Store.messages.filter(msg => msg.sender === sender)
      )
    },
    
    // 获取最近的消息
    recent: (count: number = 10) => {
      return computed(() => 
        chat4Store.messages.slice(-count)
      )
    },
    
    // 按时间范围过滤
    byTimeRange: (startTime: number, endTime: number) => {
      return computed(() => 
        chat4Store.messages.filter(msg => 
          msg.timestamp >= startTime && msg.timestamp <= endTime
        )
      )
    }
  }
  
  // 评论过滤器
  const commentFilters = {
    // 按类型过滤评论
    byType: (type: LiveComment['type']) => {
      return computed(() => 
        chat4Store.liveComments.filter(comment => comment.type === type)
      )
    },
    
    // 获取最近的评论
    recent: (count: number = 20) => {
      return computed(() => 
        chat4Store.liveComments.slice(-count)
      )
    }
  }
  
  // 消息统计
  const messageStats = computed(() => {
    const messages = chat4Store.messages
    return {
      total: messages.length,
      text: messages.filter(m => m.type === 'text').length,
      gift: messages.filter(m => m.type === 'gift').length,
      system: messages.filter(m => m.type === 'system').length,
      image: messages.filter(m => m.type === 'image').length,
      emoji: messages.filter(m => m.type === 'emoji').length
    }
  })
  
  // 评论统计
  const commentStats = computed(() => {
    const comments = chat4Store.liveComments
    return {
      total: comments.length,
      comment: comments.filter(c => c.type === 'comment').length,
      gift: comments.filter(c => c.type === 'gift').length,
      enter: comments.filter(c => c.type === 'enter').length,
      follow: comments.filter(c => c.type === 'follow').length
    }
  })
  
  // 消息清理方法
  const messageCleaner = {
    // 清理所有消息
    clearAll: () => {
      // 注意：由于store中的messages是readonly，需要通过store方法清理
      chat4Store.resetAllState()
    },
    
    // 清理指定类型的消息
    clearByType: (type: ChatMessage['type']) => {
      // 这里需要在store中添加相应的清理方法
      console.log('清理消息类型:', type)
    },
    
    // 清理旧消息（保留最近的指定数量）
    clearOld: (keepCount: number = 50) => {
      // 这里需要在store中添加相应的清理方法
      console.log('清理旧消息，保留:', keepCount)
    }
  }
  
  // 消息监听器
  const messageWatchers = {
    // 监听新消息
    onNewMessage: (callback: (message: ChatMessage) => void) => {
      const unwatch = watch(
        () => chat4Store.messages.length,
        (newLength, oldLength) => {
          if (newLength > oldLength) {
            const newMessage = chat4Store.messages[newLength - 1]
            callback(newMessage)
          }
        }
      )
      return unwatch
    },
    
    // 监听新评论
    onNewComment: (callback: (comment: LiveComment) => void) => {
      const unwatch = watch(
        () => chat4Store.liveComments.length,
        (newLength, oldLength) => {
          if (newLength > oldLength) {
            const newComment = chat4Store.liveComments[newLength - 1]
            callback(newComment)
          }
        }
      )
      return unwatch
    }
  }
  
  // 消息格式化工具
  const messageFormatter = {
    // 格式化时间戳
    formatTime: (timestamp: number) => {
      const date = new Date(timestamp)
      return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    },
    
    // 格式化消息内容
    formatContent: (message: ChatMessage) => {
      switch (message.type) {
        case 'gift':
          return `🎁 ${message.content}`
        case 'system':
          return `📢 ${message.content}`
        case 'emoji':
          return message.content
        default:
          return message.content
      }
    },
    
    // 格式化评论显示
    formatComment: (comment: LiveComment) => {
      switch (comment.type) {
        case 'enter':
          return `👋 ${comment.content}`
        case 'gift':
          return `🎁 ${comment.username} ${comment.content}`
        case 'follow':
          return `❤️ ${comment.content}`
        default:
          return `💬 ${comment.username}: ${comment.content}`
      }
    }
  }
  
  return {
    // 数据访问
    messageData,
    messageStats,
    commentStats,
    
    // 发送方法
    sendMessage,
    liveComment,
    
    // 过滤器
    messageFilters,
    commentFilters,
    
    // 工具方法
    messageCleaner,
    messageWatchers,
    messageFormatter
  }
}