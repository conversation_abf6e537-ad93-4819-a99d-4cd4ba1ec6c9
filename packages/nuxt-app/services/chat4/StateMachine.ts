/**
 * Chat4 游戏状态机
 * 使用XState，零if-else，声明式状态管理
 */

import { createMachine, interpret, type InterpreterFrom, type StateFrom } from 'xstate'
import type { StateMachine, GameState, EventHandler, Chat4Result } from '~/types/chat4'
import { GameStateError, ok, err } from '~/types/chat4'
import type { EventBus } from '~/types/chat4'

// 状态机上下文类型
interface Chat4Context {
  sessionId?: string
  actorId?: string
  storyId?: string
  currentScene?: string
  favorability?: number
  error?: string
  retryCount: number
}

// 状态机事件类型
type Chat4StateEvent = 
  | { type: 'INITIALIZE'; data: { actorId: string; storyId: string } }
  | { type: 'LOAD_HISTORY'; shouldRestart?: boolean }
  | { type: 'START_GAME'; sessionId: string }
  | { type: 'SCENE_CHANGE'; scene: string }
  | { type: 'PAUSE' }
  | { type: 'RESUME' }
  | { type: 'END_GAME'; reason: string }
  | { type: 'ERROR'; error: string }
  | { type: 'RETRY' }
  | { type: 'RESET' }

/**
 * Chat4 游戏状态机定义
 * 使用声明式状态转换，完全消除if-else逻辑
 */
const chat4GameMachine = createMachine({
  id: 'chat4Game',
  initial: 'idle',
  context: {
    retryCount: 0
  } as Chat4Context,
  
  states: {
    // 空闲状态
    idle: {
      on: {
        INITIALIZE: {
          target: 'initializing',
          actions: 'setInitData'
        }
      }
    },

    // 初始化状态
    initializing: {
      entry: 'notifyInitializing',
      on: {
        LOAD_HISTORY: [
          {
            target: 'loading_history',
            guard: 'shouldLoadHistory'
          },
          {
            target: 'starting_game'
          }
        ],
        ERROR: {
          target: 'error',
          actions: 'setError'
        }
      }
    },

    // 加载历史记录状态
    loading_history: {
      entry: 'notifyLoadingHistory',
      invoke: {
        src: 'loadHistoryService',
        onDone: {
          target: 'starting_game',
          actions: 'setHistoryData'
        },
        onError: {
          target: 'starting_game', // 加载失败则开始新游戏
          actions: 'clearHistory'
        }
      },
      on: {
        ERROR: {
          target: 'error',
          actions: 'setError'
        }
      }
    },

    // 启动游戏状态
    starting_game: {
      entry: 'notifyStartingGame',
      invoke: {
        src: 'startGameService',
        onDone: {
          target: 'playing',
          actions: 'setGameSession'
        },
        onError: {
          target: 'error',
          actions: 'setError'
        }
      },
      on: {
        ERROR: {
          target: 'error',
          actions: 'setError'
        }
      }
    },

    // 游戏进行状态
    playing: {
      entry: 'notifyPlaying',
      on: {
        SCENE_CHANGE: {
          target: 'scene_transitioning',
          actions: 'setTargetScene'
        },
        PAUSE: {
          target: 'paused'
        },
        END_GAME: {
          target: 'ending',
          actions: 'setEndReason'
        },
        ERROR: {
          target: 'error',
          actions: 'setError'
        }
      }
    },

    // 场景切换状态
    scene_transitioning: {
      entry: 'notifySceneTransitioning',
      invoke: {
        src: 'sceneTransitionService',
        onDone: {
          target: 'playing',
          actions: 'completeSceneTransition'
        },
        onError: {
          target: 'playing', // 切换失败回到游戏状态
          actions: 'revertSceneTransition'
        }
      },
      on: {
        ERROR: {
          target: 'error',
          actions: 'setError'
        }
      }
    },

    // 暂停状态
    paused: {
      entry: 'notifyPaused',
      on: {
        RESUME: {
          target: 'playing'
        },
        END_GAME: {
          target: 'ending',
          actions: 'setEndReason'
        },
        ERROR: {
          target: 'error',
          actions: 'setError'
        }
      }
    },

    // 游戏结束状态
    ending: {
      entry: 'notifyEnding',
      invoke: {
        src: 'endGameService',
        onDone: {
          target: 'idle',
          actions: 'clearGameData'
        },
        onError: {
          target: 'error',
          actions: 'setError'
        }
      }
    },

    // 错误状态
    error: {
      entry: 'notifyError',
      on: {
        RETRY: [
          {
            target: 'initializing',
            guard: 'canRetry',
            actions: 'incrementRetryCount'
          },
          {
            target: 'idle',
            actions: 'maxRetriesReached'
          }
        ],
        RESET: {
          target: 'idle',
          actions: 'resetContext'
        }
      }
    }
  }
}).provide({
  // Actions - 状态机动作
  actions: {
    setInitData: ({ context, event }) => {
      if (event.type === 'INITIALIZE') {
        context.actorId = event.data.actorId
        context.storyId = event.data.storyId
        context.retryCount = 0
      }
    },

    setError: ({ context, event }) => {
      if (event.type === 'ERROR') {
        context.error = event.error
      }
    },

    setGameSession: ({ context, event }) => {
      if (event.type === 'done.invoke.startGameService') {
        context.sessionId = event.data.sessionId
        context.currentScene = event.data.initialScene
        context.favorability = event.data.favorability
      }
    },

    setTargetScene: ({ context, event }) => {
      if (event.type === 'SCENE_CHANGE') {
        context.currentScene = event.scene
      }
    },

    setEndReason: ({ context, event }) => {
      if (event.type === 'END_GAME') {
        context.error = event.reason
      }
    },

    incrementRetryCount: ({ context }) => {
      context.retryCount += 1
    },

    resetContext: ({ context }) => {
      Object.assign(context, {
        sessionId: undefined,
        actorId: undefined,
        storyId: undefined,
        currentScene: undefined,
        favorability: undefined,
        error: undefined,
        retryCount: 0
      })
    },

    clearGameData: ({ context }) => {
      context.sessionId = undefined
      context.currentScene = undefined
      context.favorability = undefined
    },

    // 通知动作（发布事件）
    notifyInitializing: () => {},
    notifyLoadingHistory: () => {},
    notifyStartingGame: () => {},
    notifyPlaying: () => {},
    notifySceneTransitioning: () => {},
    notifyPaused: () => {},
    notifyEnding: () => {},
    notifyError: () => {},
    setHistoryData: () => {},
    clearHistory: () => {},
    completeSceneTransition: () => {},
    revertSceneTransition: () => {},
    maxRetriesReached: () => {}
  },

  // Guards - 状态转换条件
  guards: {
    shouldLoadHistory: ({ context, event }) => {
      return event.type === 'LOAD_HISTORY' && !event.shouldRestart
    },

    canRetry: ({ context }) => {
      return context.retryCount < 3
    }
  },

  // Actors - 异步服务
  actors: {
    loadHistoryService: async (context) => {
      // 这里会调用实际的历史加载服务
      throw new Error('Service not implemented')
    },

    startGameService: async (context) => {
      // 这里会调用实际的游戏启动服务
      throw new Error('Service not implemented')
    },

    sceneTransitionService: async (context) => {
      // 这里会调用实际的场景切换服务
      throw new Error('Service not implemented')
    },

    endGameService: async (context) => {
      // 这里会调用实际的游戏结束服务
      throw new Error('Service not implemented')
    }
  }
})

/**
 * Chat4 状态机实现
 * 封装XState状态机，提供类型安全的接口
 */
export class Chat4StateMachine implements StateMachine {
  private machine: typeof chat4GameMachine
  private service: InterpreterFrom<typeof chat4GameMachine>
  private eventHandlers = new Map<string, Set<EventHandler>>()
  private eventBus: EventBus

  constructor(eventBus: EventBus, actors?: Record<string, any>) {
    this.eventBus = eventBus
    
    // 创建状态机实例，注入服务
    this.machine = chat4GameMachine.provide({
      actors: {
        ...actors
      },
      actions: {
        // 重写通知动作，发布事件
        notifyInitializing: this.createNotifyAction('game:state_changed', 'initializing'),
        notifyLoadingHistory: this.createNotifyAction('game:state_changed', 'loading_history'),
        notifyStartingGame: this.createNotifyAction('game:state_changed', 'starting_game'),
        notifyPlaying: this.createNotifyAction('game:state_changed', 'playing'),
        notifySceneTransitioning: this.createNotifyAction('game:state_changed', 'scene_transitioning'),
        notifyPaused: this.createNotifyAction('game:state_changed', 'paused'),
        notifyEnding: this.createNotifyAction('game:state_changed', 'ending'),
        notifyError: this.createNotifyAction('game:state_changed', 'error')
      }
    })

    // 启动状态机服务
    this.service = interpret(this.machine)
    this.service.start()

    // 监听状态变化
    this.service.subscribe((state) => {
      this.eventBus.publish('game:state_transition', {
        from: state.history?.value || null,
        to: state.value,
        context: state.context
      })
    })
  }

  /**
   * 获取当前状态
   */
  get currentState(): GameState {
    return this.service.getSnapshot().value as GameState
  }

  /**
   * 检查是否可以转换到目标状态
   * @param to 目标状态
   * @returns 是否可以转换
   */
  canTransition(to: GameState): boolean {
    const currentState = this.service.getSnapshot()
    
    // 检查状态机定义中是否有相应的转换
    return Object.keys(currentState.nextEvents).length > 0
  }

  /**
   * 执行状态转换
   * @param to 目标状态
   * @param data 转换数据
   * @returns 转换结果
   */
  async transition(to: GameState, data?: any): Promise<Chat4Result<void>> {
    const currentState = this.currentState

    try {
      // 根据目标状态发送相应事件
      switch (to) {
        case 'initializing':
          if (!data?.actorId || !data?.storyId) {
            return err(new GameStateError('Missing required data for initialization', currentState, to))
          }
          this.service.send({ type: 'INITIALIZE', data })
          break

        case 'loading_history':
          this.service.send({ type: 'LOAD_HISTORY', shouldRestart: data?.shouldRestart })
          break

        case 'starting_game':
          this.service.send({ type: 'START_GAME', sessionId: data?.sessionId })
          break

        case 'scene_transitioning':
          if (!data?.scene) {
            return err(new GameStateError('Missing scene data for transition', currentState, to))
          }
          this.service.send({ type: 'SCENE_CHANGE', scene: data.scene })
          break

        case 'paused':
          this.service.send({ type: 'PAUSE' })
          break

        case 'playing':
          this.service.send({ type: 'RESUME' })
          break

        case 'ending':
          this.service.send({ type: 'END_GAME', reason: data?.reason || 'normal' })
          break

        case 'error':
          this.service.send({ type: 'ERROR', error: data?.error || 'Unknown error' })
          break

        default:
          return err(new GameStateError(`Unknown target state: ${to}`, currentState, to))
      }

      return ok(undefined)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      return err(new GameStateError(`Transition failed: ${errorMessage}`, currentState, to))
    }
  }

  /**
   * 注册事件处理器
   * @param event 事件名
   * @param handler 处理器
   * @returns 取消注册函数
   */
  on(event: string, handler: EventHandler): () => void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, new Set())
    }

    this.eventHandlers.get(event)!.add(handler)

    return () => {
      this.eventHandlers.get(event)?.delete(handler)
    }
  }

  /**
   * 获取当前上下文
   */
  getContext(): Chat4Context {
    return this.service.getSnapshot().context
  }

  /**
   * 获取状态历史
   */
  getStateHistory(): GameState[] {
    // XState 5.x 的历史记录获取方式可能需要调整
    return [] // 暂时返回空数组
  }

  /**
   * 重试当前操作
   */
  retry(): Chat4Result<void> {
    try {
      this.service.send({ type: 'RETRY' })
      return ok(undefined)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      return err(new GameStateError(`Retry failed: ${errorMessage}`, this.currentState, this.currentState))
    }
  }

  /**
   * 重置状态机
   */
  reset(): Chat4Result<void> {
    try {
      this.service.send({ type: 'RESET' })
      return ok(undefined)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      return err(new GameStateError(`Reset failed: ${errorMessage}`, this.currentState, 'idle'))
    }
  }

  /**
   * 销毁状态机
   */
  destroy(): void {
    this.service.stop()
    this.eventHandlers.clear()
  }

  /**
   * 创建通知动作
   * @param eventName 事件名
   * @param state 状态
   * @returns 动作函数
   */
  private createNotifyAction(eventName: string, state: string) {
    return ({ context }: { context: Chat4Context }) => {
      this.eventBus.publish(eventName, {
        state,
        context: { ...context },
        timestamp: Date.now()
      })
    }
  }
}

/**
 * 状态机工厂函数
 * @param eventBus 事件总线
 * @param services 自定义服务
 * @returns 状态机实例
 */
export function createChat4StateMachine(
  eventBus: EventBus,
  actors?: {
    loadHistory?: (context: Chat4Context) => Promise<any>
    startGame?: (context: Chat4Context) => Promise<any>
    sceneTransition?: (context: Chat4Context) => Promise<any>
    endGame?: (context: Chat4Context) => Promise<any>
  }
): Chat4StateMachine {
  const xstateActors = actors ? {
    loadHistoryService: actors.loadHistory,
    startGameService: actors.startGame,
    sceneTransitionService: actors.sceneTransition,
    endGameService: actors.endGame
  } : undefined

  return new Chat4StateMachine(eventBus, xstateActors)
}

/**
 * 状态机工具函数
 */
export const StateMachineUtils = {
  /**
   * 检查状态是否为最终状态
   * @param state 状态
   * @returns 是否为最终状态
   */
  isFinalState(state: GameState): boolean {
    return state === 'idle' || state === 'error'
  },

  /**
   * 检查状态是否为加载状态
   * @param state 状态
   * @returns 是否为加载状态
   */
  isLoadingState(state: GameState): boolean {
    return ['initializing', 'loading_history', 'starting_game', 'scene_transitioning'].includes(state)
  },

  /**
   * 检查状态是否为活跃游戏状态
   * @param state 状态
   * @returns 是否为活跃状态
   */
  isActiveState(state: GameState): boolean {
    return ['playing', 'paused'].includes(state)
  },

  /**
   * 获取状态的显示名称
   * @param state 状态
   * @returns 显示名称
   */
  getStateDisplayName(state: GameState): string {
    const stateNames: Record<GameState, string> = {
      idle: '空闲',
      initializing: '初始化中',
      loading_history: '加载历史记录',
      starting_game: '启动游戏',
      playing: '游戏中',
      scene_transitioning: '场景切换中',
      paused: '已暂停',
      ending: '游戏结束',
      error: '错误'
    }

    return stateNames[state] || '未知状态'
  }
}