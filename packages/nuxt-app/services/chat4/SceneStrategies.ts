/**
 * Chat4 场景策略处理器
 * 使用策略模式，零if-else，可扩展的场景处理
 */

import type {
  SceneStrategy,
  GameEvent,
  Chat4Result,
  SceneType,
  EventBus,
  Storage
} from '~/types/chat4'
import { ok, err, SceneError } from '~/types/chat4'

/**
 * 场景策略基类
 * 提供通用的场景处理功能
 */
abstract class BaseSceneStrategy implements SceneStrategy {
  constructor(
    protected eventBus: EventBus,
    protected storage: Storage
  ) {}

  abstract readonly sceneType: SceneType
  abstract canHandle(event: GameEvent): boolean
  abstract process(event: GameEvent): Promise<Chat4Result<void>>

  /**
   * 清理场景资源（可选实现）
   */
  async cleanup?(): Promise<void> {
    // 默认空实现
  }

  /**
   * 发布场景事件
   * @protected
   */
  protected publishSceneEvent(eventName: string, data: any): void {
    this.eventBus.publish(`scene:${this.sceneType}:${eventName}`, {
      scene: this.sceneType,
      ...data,
      timestamp: Date.now()
    })
  }

  /**
   * 保存场景状态
   * @protected
   */
  protected async saveSceneState(state: any): Promise<void> {
    await this.storage.set(`scene:${this.sceneType}:state`, {
      ...state,
      lastUpdated: Date.now()
    })
  }

  /**
   * 获取场景状态
   * @protected
   */
  protected async getSceneState<T>(): Promise<T | null> {
    return this.storage.get<T>(`scene:${this.sceneType}:state`)
  }
}

/**
 * 直播场景策略
 * 处理直播间相关的事件和逻辑
 */
export class LivingSceneStrategy extends BaseSceneStrategy {
  readonly sceneType: SceneType = 'Living'

  canHandle(event: GameEvent): boolean {
    return event.sceneType === 'Living' || 
           (event.type === 'scene_change' && event.data.scene === 'Living')
  }

  async process(event: GameEvent): Promise<Chat4Result<void>> {
    try {
      switch (event.type) {
        case 'message':
          return this.handleMessage(event)
        
        case 'heart_value':
          return this.handleHeartValue(event)
        
        case 'scene_change':
          return this.handleSceneEnter(event)
        
        case 'show_image':
          return this.handleShowImage(event)
        
        case 'play_video':
          return this.handlePlayVideo(event)
        
        default:
          console.warn(`[LivingScene] Unhandled event type: ${event.type}`)
          return ok(undefined)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      return err(new SceneError(`Living scene processing failed: ${errorMessage}`, 'Living'))
    }
  }

  /**
   * 处理消息事件
   * @private
   */
  private async handleMessage(event: GameEvent): Promise<Chat4Result<void>> {
    const message = event.data

    // 发布直播消息事件
    this.publishSceneEvent('message', {
      message,
      isLiveComment: true
    })

    // 如果是礼物消息，处理礼物逻辑
    if (message.type === 'gift') {
      this.publishSceneEvent('gift_received', {
        gift: message.gift,
        sender: message.sender
      })
    }

    return ok(undefined)
  }

  /**
   * 处理心值更新事件
   * @private
   */
  private async handleHeartValue(event: GameEvent): Promise<Chat4Result<void>> {
    const { value, change } = event.data

    // 发布心值更新事件
    this.publishSceneEvent('heart_value_updated', {
      currentValue: value,
      change,
      isPositive: change > 0
    })

    // 更新场景状态
    await this.saveSceneState({
      heartValue: value,
      lastHeartChange: change
    })

    return ok(undefined)
  }

  /**
   * 处理场景进入事件
   * @private
   */
  private async handleSceneEnter(event: GameEvent): Promise<Chat4Result<void>> {
    // 初始化直播场景状态
    await this.saveSceneState({
      viewerCount: 1,
      heartValue: 0,
      isLive: true,
      startTime: Date.now()
    })

    // 发布场景激活事件
    this.publishSceneEvent('activated', {
      reason: event.data.reason || 'scene_change'
    })

    return ok(undefined)
  }

  /**
   * 处理显示图片事件
   * @private
   */
  private async handleShowImage(event: GameEvent): Promise<Chat4Result<void>> {
    this.publishSceneEvent('image_shown', {
      imageUrl: event.data.url,
      duration: event.data.duration
    })

    return ok(undefined)
  }

  /**
   * 处理播放视频事件
   * @private
   */
  private async handlePlayVideo(event: GameEvent): Promise<Chat4Result<void>> {
    this.publishSceneEvent('video_played', {
      videoUrl: event.data.url,
      autoplay: event.data.autoplay
    })

    return ok(undefined)
  }

  async cleanup(): Promise<void> {
    // 清理直播场景状态
    await this.storage.remove(`scene:${this.sceneType}:state`)
    
    this.publishSceneEvent('deactivated', {
      reason: 'cleanup'
    })
  }
}

/**
 * 聊天场景策略
 * 处理一对一聊天相关的事件和逻辑
 */
export class PhoneSceneStrategy extends BaseSceneStrategy {
  readonly sceneType: SceneType = 'Phone'

  canHandle(event: GameEvent): boolean {
    return event.sceneType === 'Phone' || 
           (event.type === 'scene_change' && event.data.scene === 'Phone')
  }

  async process(event: GameEvent): Promise<Chat4Result<void>> {
    try {
      switch (event.type) {
        case 'message':
          return this.handleMessage(event)
        
        case 'scene_change':
          return this.handleSceneEnter(event)
        
        case 'favorability_update':
          return this.handleFavorabilityUpdate(event)
        
        default:
          console.warn(`[PhoneScene] Unhandled event type: ${event.type}`)
          return ok(undefined)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      return err(new SceneError(`Phone scene processing failed: ${errorMessage}`, 'Phone'))
    }
  }

  private async handleMessage(event: GameEvent): Promise<Chat4Result<void>> {
    const message = event.data

    // 发布聊天消息事件
    this.publishSceneEvent('message', {
      message,
      isPrivateChat: true
    })

    // 更新消息计数
    const state = await this.getSceneState<{ messageCount: number }>()
    await this.saveSceneState({
      ...state,
      messageCount: (state?.messageCount || 0) + 1
    })

    return ok(undefined)
  }

  private async handleSceneEnter(event: GameEvent): Promise<Chat4Result<void>> {
    await this.saveSceneState({
      messageCount: 0,
      isTyping: false,
      startTime: Date.now()
    })

    this.publishSceneEvent('activated', {
      reason: event.data.reason || 'scene_change'
    })

    return ok(undefined)
  }

  private async handleFavorabilityUpdate(event: GameEvent): Promise<Chat4Result<void>> {
    this.publishSceneEvent('favorability_updated', {
      favorability: event.data
    })

    return ok(undefined)
  }
}

/**
 * 视频通话场景策略
 * 处理视频通话相关的事件和逻辑
 */
export class VideoSceneStrategy extends BaseSceneStrategy {
  readonly sceneType: SceneType = 'Video'

  canHandle(event: GameEvent): boolean {
    return event.sceneType === 'Video' || 
           (event.type === 'scene_change' && event.data.scene === 'Video')
  }

  async process(event: GameEvent): Promise<Chat4Result<void>> {
    try {
      switch (event.type) {
        case 'scene_change':
          return this.handleSceneEnter(event)
        
        case 'play_video':
          return this.handleVideoPlay(event)
        
        case 'payment_required':
          return this.handlePaymentRequired(event)
        
        default:
          console.warn(`[VideoScene] Unhandled event type: ${event.type}`)
          return ok(undefined)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      return err(new SceneError(`Video scene processing failed: ${errorMessage}`, 'Video'))
    }
  }

  private async handleSceneEnter(event: GameEvent): Promise<Chat4Result<void>> {
    await this.saveSceneState({
      isConnected: true,
      quality: 'HD',
      duration: 0,
      startTime: Date.now()
    })

    this.publishSceneEvent('call_started', {
      reason: event.data.reason || 'scene_change'
    })

    return ok(undefined)
  }

  private async handleVideoPlay(event: GameEvent): Promise<Chat4Result<void>> {
    this.publishSceneEvent('video_playing', {
      videoUrl: event.data.url,
      quality: event.data.quality || 'HD'
    })

    return ok(undefined)
  }

  private async handlePaymentRequired(event: GameEvent): Promise<Chat4Result<void>> {
    this.publishSceneEvent('payment_requested', {
      amount: event.data.amount,
      reason: event.data.reason
    })

    return ok(undefined)
  }
}

/**
 * 约会场景策略
 * 处理各种约会场景的事件和逻辑
 */
export class MeetupSceneStrategy extends BaseSceneStrategy {
  readonly sceneType: SceneType = 'Meetup'

  canHandle(event: GameEvent): boolean {
    return event.sceneType?.startsWith('Meetup') === true ||
           (event.type === 'scene_change' && event.data.scene?.startsWith('Meetup'))
  }

  async process(event: GameEvent): Promise<Chat4Result<void>> {
    try {
      switch (event.type) {
        case 'scene_change':
          return this.handleSceneEnter(event)
        
        case 'show_image':
          return this.handleShowImage(event)
        
        case 'favorability_update':
          return this.handleFavorabilityUpdate(event)
        
        default:
          console.warn(`[MeetupScene] Unhandled event type: ${event.type}`)
          return ok(undefined)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      return err(new SceneError(`Meetup scene processing failed: ${errorMessage}`, 'Meetup'))
    }
  }

  private async handleSceneEnter(event: GameEvent): Promise<Chat4Result<void>> {
    const meetupType = this.getMeetupType(event.data.scene)
    
    await this.saveSceneState({
      meetupType,
      location: this.getMeetupLocation(meetupType),
      mood: 'excited',
      startTime: Date.now()
    })

    this.publishSceneEvent('meetup_started', {
      meetupType,
      location: this.getMeetupLocation(meetupType)
    })

    return ok(undefined)
  }

  private async handleShowImage(event: GameEvent): Promise<Chat4Result<void>> {
    this.publishSceneEvent('environment_shown', {
      imageUrl: event.data.url,
      description: event.data.description
    })

    return ok(undefined)
  }

  private async handleFavorabilityUpdate(event: GameEvent): Promise<Chat4Result<void>> {
    this.publishSceneEvent('interaction_result', {
      favorability: event.data,
      interactionType: 'meetup_activity'
    })

    return ok(undefined)
  }

  private getMeetupType(sceneType: string): string {
    if (sceneType.includes('Pool')) return 'pool'
    if (sceneType.includes('Coffee')) return 'coffee'
    if (sceneType.includes('Office')) return 'office'
    if (sceneType.includes('Seaside')) return 'seaside'
    return 'general'
  }

  private getMeetupLocation(meetupType: string): string {
    const locations = {
      pool: '游泳池',
      coffee: '咖啡店',
      office: '办公室',
      seaside: '海边',
      general: '约会地点'
    }
    return locations[meetupType as keyof typeof locations] || '未知地点'
  }
}

/**
 * 场景策略管理器
 * 使用策略模式管理所有场景策略，零if-else
 */
export class SceneStrategyManager {
  private strategies = new Map<SceneType, SceneStrategy>()
  private activeStrategy: SceneStrategy | null = null

  constructor(
    private eventBus: EventBus,
    private storage: Storage
  ) {
    this.registerDefaultStrategies()
  }

  /**
   * 注册场景策略
   * @param strategy 场景策略实例
   */
  registerStrategy(strategy: SceneStrategy): void {
    this.strategies.set(strategy.sceneType, strategy)
  }

  /**
   * 处理游戏事件
   * @param event 游戏事件
   * @returns 处理结果
   */
  async processEvent(event: GameEvent): Promise<Chat4Result<void>> {
    // 查找能处理此事件的策略
    const strategy = this.findStrategyForEvent(event)
    
    if (!strategy) {
      console.warn(`[SceneManager] No strategy found for event:`, event)
      return ok(undefined)
    }

    // 如果切换了策略，清理之前的策略
    if (this.activeStrategy && this.activeStrategy !== strategy) {
      await this.cleanupActiveStrategy()
    }

    this.activeStrategy = strategy

    // 执行策略处理
    return strategy.process(event)
  }

  /**
   * 获取当前活跃策略
   * @returns 当前策略或null
   */
  getActiveStrategy(): SceneStrategy | null {
    return this.activeStrategy
  }

  /**
   * 获取所有注册的策略
   * @returns 策略类型数组
   */
  getRegisteredStrategies(): SceneType[] {
    return Array.from(this.strategies.keys())
  }

  /**
   * 清理所有策略
   */
  async cleanup(): Promise<void> {
    await this.cleanupActiveStrategy()
    
    // 清理所有策略
    for (const strategy of this.strategies.values()) {
      if (strategy.cleanup) {
        await strategy.cleanup()
      }
    }
    
    this.strategies.clear()
    this.activeStrategy = null
  }

  /**
   * 查找能处理事件的策略
   * @private
   */
  private findStrategyForEvent(event: GameEvent): SceneStrategy | null {
    // 优先查找完全匹配的策略
    for (const strategy of this.strategies.values()) {
      if (strategy.canHandle(event)) {
        return strategy
      }
    }

    // 如果没有找到，尝试通过场景类型匹配
    if (event.sceneType) {
      return this.strategies.get(event.sceneType) || null
    }

    return null
  }

  /**
   * 清理当前活跃策略
   * @private
   */
  private async cleanupActiveStrategy(): Promise<void> {
    if (this.activeStrategy && this.activeStrategy.cleanup) {
      await this.activeStrategy.cleanup()
    }
  }

  /**
   * 注册默认策略
   * @private
   */
  private registerDefaultStrategies(): void {
    this.registerStrategy(new LivingSceneStrategy(this.eventBus, this.storage))
    this.registerStrategy(new PhoneSceneStrategy(this.eventBus, this.storage))
    this.registerStrategy(new VideoSceneStrategy(this.eventBus, this.storage))
    this.registerStrategy(new MeetupSceneStrategy(this.eventBus, this.storage))
  }
}

/**
 * 场景策略工厂函数
 * @param eventBus 事件总线
 * @param storage 存储服务
 * @returns 场景策略管理器
 */
export function createSceneStrategyManager(
  eventBus: EventBus,
  storage: Storage
): SceneStrategyManager {
  return new SceneStrategyManager(eventBus, storage)
}

/**
 * 场景工具函数
 */
export const SceneUtils = {
  /**
   * 检查是否为约会场景
   * @param sceneType 场景类型
   * @returns 是否为约会场景
   */
  isMeetupScene(sceneType: string): boolean {
    return sceneType.startsWith('Meetup') || sceneType.startsWith('map-')
  },

  /**
   * 获取场景显示名称
   * @param sceneType 场景类型
   * @returns 显示名称
   */
  getSceneDisplayName(sceneType: SceneType): string {
    const names: Record<string, string> = {
      'Living': '直播间',
      'Phone': '聊天',
      'Video': '视频通话',
      'Monitor': '监控',
      'Map': '地图',
      'Meetup': '约会',
      'MeetupPool': '游泳池约会',
      'MeetupCoffee': '咖啡店约会',
      'MeetupOffice': '办公室约会',
      'MeetupSeaside': '海边约会',
      'Dancing': '舞蹈',
      'Concert': '演唱会',
      'Moment': '朋友圈',
      'Diary': '日记'
    }

    return names[sceneType] || sceneType
  },

  /**
   * 验证场景类型
   * @param sceneType 场景类型
   * @returns 是否有效
   */
  isValidSceneType(sceneType: string): sceneType is SceneType {
    const validTypes = [
      'Living', 'Phone', 'Video', 'Monitor', 'Map', 'Meetup',
      'MeetupPool', 'MeetupCoffee', 'MeetupOffice', 'MeetupSeaside',
      'Dancing', 'Concert', 'Moment', 'Diary'
    ]

    return validTypes.includes(sceneType) || sceneType.startsWith('map-')
  }
}