/**
 * Chat4 服务工厂和依赖注入容器
 * 统一管理所有服务实例，提供依赖注入和生命周期管理
 */

import type {
  EventBus,
  StateMachine,
  Chat4ApiAdapter,
  Storage,
  GameService,
  Chat4Config,
  Chat4Result
} from '~/types/chat4'
import { ok, err, Chat4Error, Chat4ConfigSchema, validateSchema } from '~/types/chat4'

// 导入所有服务实现
import { Chat4EventBus, createNamespacedEventBus } from './EventBus'
import { createChat4StateMachine } from './StateMachine'
import { createChat4ApiAdapter } from './ApiAdapter'
import { createChat4Storage } from './Storage'
import { createChat4GameService } from './GameService'
import { createSceneStrategyManager, type SceneStrategyManager } from './SceneStrategies'

/**
 * 服务容器错误类
 */
export class ServiceContainerError extends Chat4Error {
  constructor(message: string, public serviceName: string) {
    super(message, 'SERVICE_CONTAINER_ERROR', { serviceName })
    this.name = 'ServiceContainerError'
  }
}

/**
 * 服务实例集合
 */
interface ServiceInstances {
  eventBus: EventBus
  stateMachine: StateMachine
  apiAdapter: Chat4ApiAdapter
  storage: Storage
  sceneManager: SceneStrategyManager
  gameService: GameService
}

/**
 * 服务配置选项
 */
interface ServiceFactoryOptions {
  config: Chat4Config
  useMockServices?: boolean
  useMemoryStorage?: boolean
  namespace?: string
  customServices?: Partial<ServiceInstances>
}

/**
 * 服务生命周期状态
 */
type ServiceLifecycleState = 'uninitialized' | 'initializing' | 'ready' | 'error' | 'destroyed'

/**
 * Chat4 服务容器
 * 特性：
 * - 依赖注入管理
 * - 服务生命周期管理
 * - 自动服务发现和注入
 * - 配置验证和管理
 * - 优雅的错误处理和清理
 */
export class Chat4ServiceContainer {
  private services: Partial<ServiceInstances> = {}
  private config: Chat4Config
  private options: ServiceFactoryOptions
  private lifecycleState: ServiceLifecycleState = 'uninitialized'
  private cleanupTasks: (() => Promise<void>)[] = []

  constructor(options: ServiceFactoryOptions) {
    this.options = options
    this.config = options.config
  }

  /**
   * 初始化所有服务
   * @returns 初始化结果
   */
  async initialize(): Promise<Chat4Result<ServiceInstances>> {
    if (this.lifecycleState === 'ready') {
      return ok(this.services as ServiceInstances)
    }

    if (this.lifecycleState === 'initializing') {
      return err(new ServiceContainerError('Container is already initializing', 'container'))
    }

    this.lifecycleState = 'initializing'

    try {
      // 验证配置
      const configValidation = validateSchema(Chat4ConfigSchema, this.config)
      if (configValidation.isErr()) {
        this.lifecycleState = 'error'
        return err(new ServiceContainerError(`Invalid configuration: ${configValidation.error.message}`, 'config'))
      }

      // 创建服务实例
      await this.createServices()

      // 注入依赖关系
      await this.injectDependencies()

      // 初始化服务
      await this.initializeServices()

      this.lifecycleState = 'ready'

      if (process.env.NODE_ENV === 'development') {
        console.log('[ServiceContainer] All services initialized successfully')
      }

      return ok(this.services as ServiceInstances)
    } catch (error) {
      this.lifecycleState = 'error'
      const errorMessage = error instanceof Error ? error.message : String(error)
      return err(new ServiceContainerError(`Service initialization failed: ${errorMessage}`, 'container'))
    }
  }

  /**
   * 获取服务实例
   * @param serviceName 服务名称
   * @returns 服务实例
   */
  getService<K extends keyof ServiceInstances>(serviceName: K): ServiceInstances[K] | null {
    if (this.lifecycleState !== 'ready') {
      console.warn(`[ServiceContainer] Service ${serviceName} requested before container is ready`)
      return null
    }

    return this.services[serviceName] || null
  }

  /**
   * 获取所有服务实例
   * @returns 服务实例集合
   */
  getAllServices(): Partial<ServiceInstances> {
    return { ...this.services }
  }

  /**
   * 获取容器状态
   * @returns 生命周期状态
   */
  getState(): ServiceLifecycleState {
    return this.lifecycleState
  }

  /**
   * 获取配置
   * @returns 当前配置
   */
  getConfig(): Chat4Config {
    return { ...this.config }
  }

  /**
   * 更新配置
   * @param newConfig 新配置
   * @returns 更新结果
   */
  async updateConfig(newConfig: Partial<Chat4Config>): Promise<Chat4Result<void>> {
    try {
      const mergedConfig = { ...this.config, ...newConfig }
      
      const validation = validateSchema(Chat4ConfigSchema, mergedConfig)
      if (validation.isErr()) {
        return err(new ServiceContainerError(`Invalid configuration: ${validation.error.message}`, 'config'))
      }

      this.config = mergedConfig
      this.options.config = mergedConfig

      // 通知配置更新
      if (this.services.eventBus) {
        this.services.eventBus.publish('config:updated', {
          oldConfig: this.config,
          newConfig: mergedConfig,
          timestamp: Date.now()
        })
      }

      if (process.env.NODE_ENV === 'development') {
        console.log('[ServiceContainer] Configuration updated')
      }

      return ok(undefined)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      return err(new ServiceContainerError(`Config update failed: ${errorMessage}`, 'config'))
    }
  }

  /**
   * 检查服务健康状态
   * @returns 健康状态报告
   */
  async healthCheck(): Promise<{
    overall: 'healthy' | 'degraded' | 'unhealthy'
    services: Record<keyof ServiceInstances, 'ok' | 'error' | 'missing'>
    details: Record<string, any>
  }> {
    const serviceHealth: Record<keyof ServiceInstances, 'ok' | 'error' | 'missing'> = {
      eventBus: 'missing',
      stateMachine: 'missing',
      apiAdapter: 'missing',
      storage: 'missing',
      sceneManager: 'missing',
      gameService: 'missing'
    }

    const details: Record<string, any> = {}

    // 检查各个服务状态
    for (const [serviceName, service] of Object.entries(this.services)) {
      try {
        if (!service) {
          serviceHealth[serviceName as keyof ServiceInstances] = 'missing'
          details[serviceName] = 'Service not initialized'
          continue
        }

        // 服务特定的健康检查
        switch (serviceName) {
          case 'stateMachine':
            details[serviceName] = {
              currentState: (service as StateMachine).currentState
            }
            break
          
          case 'apiAdapter':
            details[serviceName] = {
              isConnected: (service as any).isConnected?.() || false
            }
            break
          
          case 'storage':
            // 简单的存储测试
            const testKey = 'health_check'
            const testValue = Date.now()
            await (service as Storage).set(testKey, testValue)
            const retrieved = await (service as Storage).get(testKey)
            await (service as Storage).remove(testKey)
            
            details[serviceName] = {
              canWrite: retrieved === testValue
            }
            break
          
          case 'gameService':
            details[serviceName] = (service as any).getGameStats?.() || {}
            break
          
          default:
            details[serviceName] = { status: 'active' }
        }

        serviceHealth[serviceName as keyof ServiceInstances] = 'ok'
      } catch (error) {
        serviceHealth[serviceName as keyof ServiceInstances] = 'error'
        details[serviceName] = {
          error: error instanceof Error ? error.message : String(error)
        }
      }
    }

    // 计算整体健康状态
    const healthValues = Object.values(serviceHealth)
    const errorCount = healthValues.filter(status => status === 'error').length
    const missingCount = healthValues.filter(status => status === 'missing').length

    let overall: 'healthy' | 'degraded' | 'unhealthy'
    if (errorCount === 0 && missingCount === 0) {
      overall = 'healthy'
    } else if (errorCount === 0 && missingCount <= 1) {
      overall = 'degraded'
    } else {
      overall = 'unhealthy'
    }

    return {
      overall,
      services: serviceHealth,
      details
    }
  }

  /**
   * 重启服务
   * @param serviceName 服务名称，不指定则重启所有服务
   * @returns 重启结果
   */
  async restart(serviceName?: keyof ServiceInstances): Promise<Chat4Result<void>> {
    try {
      if (serviceName) {
        // 重启单个服务
        return this.restartSingleService(serviceName)
      } else {
        // 重启所有服务
        await this.cleanup()
        const initResult = await this.initialize()
        return initResult.isOk() ? ok(undefined) : err(initResult.error)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      return err(new ServiceContainerError(`Restart failed: ${errorMessage}`, serviceName || 'all'))
    }
  }

  /**
   * 清理所有资源
   */
  async cleanup(): Promise<void> {
    this.lifecycleState = 'destroyed'

    // 执行清理任务
    await Promise.all(this.cleanupTasks.map(task => 
      task().catch(error => console.error('[ServiceContainer] Cleanup task failed:', error))
    ))

    // 清理各个服务
    for (const [serviceName, service] of Object.entries(this.services)) {
      try {
        if (service && typeof (service as any).cleanup === 'function') {
          await (service as any).cleanup()
        }
      } catch (error) {
        console.error(`[ServiceContainer] Failed to cleanup ${serviceName}:`, error)
      }
    }

    // 清空服务引用
    this.services = {}
    this.cleanupTasks = []

    if (process.env.NODE_ENV === 'development') {
      console.log('[ServiceContainer] Cleanup completed')
    }
  }

  /**
   * 创建服务实例
   * @private
   */
  private async createServices(): Promise<void> {
    const { useMockServices, useMemoryStorage, namespace, customServices } = this.options

    // 创建事件总线
    this.services.eventBus = customServices?.eventBus || 
      (namespace ? createNamespacedEventBus(namespace) : new Chat4EventBus())

    // 创建存储服务
    this.services.storage = customServices?.storage || 
      createChat4Storage(this.config, useMemoryStorage)

    // 创建API适配器
    this.services.apiAdapter = customServices?.apiAdapter || 
      createChat4ApiAdapter(this.config, useMockServices)

    // 创建状态机
    this.services.stateMachine = customServices?.stateMachine || 
      createChat4StateMachine(this.services.eventBus!, {
        loadHistory: async (context) => {
          const historyResult = await this.services.apiAdapter!.getHistory({
            actorId: context.actorId!,
            storyId: context.storyId!
          })
          if (historyResult.isErr()) throw new Error(historyResult.error.message)
          return historyResult.value
        },
        startGame: async (context) => {
          const gameResult = await this.services.apiAdapter!.startGame({
            actorId: context.actorId!,
            storyId: context.storyId!
          })
          if (gameResult.isErr()) throw new Error(gameResult.error.message)
          return gameResult.value
        }
      })

    // 创建场景管理器
    this.services.sceneManager = customServices?.sceneManager || 
      createSceneStrategyManager(this.services.eventBus!, this.services.storage!)

    // 创建游戏服务
    this.services.gameService = customServices?.gameService || 
      createChat4GameService({
        eventBus: this.services.eventBus!,
        stateMachine: this.services.stateMachine!,
        apiAdapter: this.services.apiAdapter!,
        storage: this.services.storage!,
        config: this.config
      })
  }

  /**
   * 注入依赖关系
   * @private
   */
  private async injectDependencies(): Promise<void> {
    // 设置事件总线订阅
    if (this.services.eventBus && this.services.sceneManager) {
      this.services.eventBus.subscribe('game:event_received', async (event) => {
        await this.services.sceneManager!.processEvent(event)
      })
    }

    // 添加清理任务
    this.cleanupTasks.push(async () => {
      if (this.services.eventBus) {
        this.services.eventBus.clear()
      }
    })
  }

  /**
   * 初始化服务
   * @private
   */
  private async initializeServices(): Promise<void> {
    // 初始化存储服务
    if (this.services.storage && typeof (this.services.storage as any).initialize === 'function') {
      const initResult = await (this.services.storage as any).initialize()
      if (initResult?.isErr?.()) {
        throw new Error(`Storage initialization failed: ${initResult.error.message}`)
      }
    }

    // 发布服务就绪事件
    if (this.services.eventBus) {
      this.services.eventBus.publish('services:ready', {
        timestamp: Date.now(),
        services: Object.keys(this.services)
      })
    }
  }

  /**
   * 重启单个服务
   * @private
   */
  private async restartSingleService(serviceName: keyof ServiceInstances): Promise<Chat4Result<void>> {
    try {
      // 清理旧服务
      const oldService = this.services[serviceName]
      if (oldService && typeof (oldService as any).cleanup === 'function') {
        await (oldService as any).cleanup()
      }

      // 重新创建服务（简化实现，实际应该根据服务类型具体处理）
      await this.createServices()
      await this.injectDependencies()

      return ok(undefined)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      return err(new ServiceContainerError(`Failed to restart ${serviceName}: ${errorMessage}`, serviceName))
    }
  }
}

/**
 * 默认配置
 */
export const defaultChat4Config: Chat4Config = {
  api: {
    baseUrl: 'http://localhost:3001/api/v1',
    timeout: 30000,
    retryAttempts: 3
  },
  sse: {
    reconnectInterval: 5000,
    maxReconnectAttempts: 10
  },
  storage: {
    prefix: 'chat4:',
    ttl: 24 * 60 * 60 * 1000 // 24小时
  },
  features: {
    enableSound: true,
    enableHistory: true,
    enableOffline: false
  }
}

/**
 * 创建Chat4服务容器
 * @param options 配置选项
 * @returns 服务容器实例
 */
export function createChat4ServiceContainer(options?: Partial<ServiceFactoryOptions>): Chat4ServiceContainer {
  const mergedOptions: ServiceFactoryOptions = {
    config: defaultChat4Config,
    useMockServices: process.env.NODE_ENV === 'development',
    useMemoryStorage: false,
    namespace: undefined,
    customServices: {},
    ...options
  }

  return new Chat4ServiceContainer(mergedOptions)
}

/**
 * 便捷的服务初始化函数
 * @param config 配置对象
 * @param options 额外选项
 * @returns 初始化结果和服务容器
 */
export async function initializeChat4Services(
  config?: Partial<Chat4Config>,
  options?: Partial<ServiceFactoryOptions>
): Promise<{
  container: Chat4ServiceContainer
  services: ServiceInstances | null
  error: Chat4Error | null
}> {
  try {
    const mergedConfig = { ...defaultChat4Config, ...config }
    const container = createChat4ServiceContainer({
      ...options,
      config: mergedConfig
    })

    const initResult = await container.initialize()
    
    if (initResult.isOk()) {
      return {
        container,
        services: initResult.value,
        error: null
      }
    } else {
      return {
        container,
        services: null,
        error: initResult.error
      }
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    return {
      container: null as any,
      services: null,
      error: new ServiceContainerError(`Initialization failed: ${errorMessage}`, 'init')
    }
  }
}

/**
 * 服务容器工具函数
 */
export const ServiceContainerUtils = {
  /**
   * 检查服务是否已初始化
   * @param container 服务容器
   * @returns 是否已初始化
   */
  isReady(container: Chat4ServiceContainer): boolean {
    return container.getState() === 'ready'
  },

  /**
   * 等待服务就绪
   * @param container 服务容器
   * @param timeout 超时时间（毫秒）
   * @returns Promise，在服务就绪或超时时resolve
   */
  async waitForReady(container: Chat4ServiceContainer, timeout = 10000): Promise<boolean> {
    if (this.isReady(container)) {
      return true
    }

    const startTime = Date.now()
    
    while (Date.now() - startTime < timeout) {
      if (this.isReady(container)) {
        return true
      }
      
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    
    return false
  },

  /**
   * 创建开发环境容器
   * @param config 配置
   * @returns 开发环境容器
   */
  createDevContainer(config?: Partial<Chat4Config>): Chat4ServiceContainer {
    return createChat4ServiceContainer({
      config: { ...defaultChat4Config, ...config },
      useMockServices: true,
      useMemoryStorage: true,
      namespace: 'dev'
    })
  },

  /**
   * 创建测试环境容器
   * @param config 配置
   * @returns 测试环境容器
   */
  createTestContainer(config?: Partial<Chat4Config>): Chat4ServiceContainer {
    return createChat4ServiceContainer({
      config: { ...defaultChat4Config, ...config },
      useMockServices: true,
      useMemoryStorage: true,
      namespace: 'test'
    })
  }
}