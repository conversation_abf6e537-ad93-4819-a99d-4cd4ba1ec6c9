/**
 * Chat4 现代化事件总线
 * 使用发布订阅模式，零if-else，类型安全
 */

import mitt from 'mitt'
import type { EventBus, EventHandler } from '~/types/chat4'

// 定义Chat4专用事件类型
export type Chat4Events = {
  // 游戏生命周期事件
  'game:initialized': { sessionId: string; actorId: string; storyId: string }
  'game:started': { scene: string; favorability: number }
  'game:ended': { reason: string; duration: number }
  'game:error': { error: string; code: string }
  
  // 场景事件
  'scene:changed': { from: string | null; to: string; timestamp: number }
  'scene:transition_start': { to: string }
  'scene:transition_end': { scene: string }
  
  // 消息事件
  'message:received': { message: any; scene: string }
  'message:sent': { message: any; scene: string }
  'message:typing': { isTyping: boolean; scene: string }
  
  // 好感度事件
  'favorability:updated': { current: number; previous: number; level: number }
  'favorability:level_up': { newLevel: number; rewards: any[] }
  
  // 支付事件
  'payment:required': { amount: number; scene: string; reason: string }
  'payment:completed': { amount: number; scene: string; success: boolean }
  
  // UI事件
  'ui:modal_open': { modal: string; data?: any }
  'ui:modal_close': { modal: string }
  'ui:toast_show': { message: string; type: 'info' | 'success' | 'error' | 'warning' }
  
  // 连接事件
  'connection:established': { sessionId: string }
  'connection:lost': { reason: string; willReconnect: boolean }
  'connection:reconnected': { sessionId: string; attempts: number }
  
  // 自定义事件
  [key: string]: any
}

/**
 * Chat4事件总线实现
 * 特性：
 * - 类型安全的事件订阅和发布
 * - 自动去重和防抖
 * - 事件历史记录
 * - 错误边界处理
 * - 内存泄漏防护
 */
export class Chat4EventBus implements EventBus {
  private emitter = mitt<Chat4Events>()
  private subscriptions = new Map<string, Set<() => void>>()
  private eventHistory = new Map<string, any[]>()
  private maxHistorySize = 100
  private isDestroyed = false

  /**
   * 订阅事件
   * @param event 事件名
   * @param handler 处理函数
   * @returns 取消订阅函数
   */
  subscribe<T>(event: string, handler: EventHandler<T>): () => void {
    if (this.isDestroyed) {
      console.warn('EventBus has been destroyed, subscription ignored')
      return () => {}
    }

    // 创建包装的处理函数，添加错误边界
    const wrappedHandler = this.createErrorBoundaryHandler(event, handler)
    
    // 订阅事件
    this.emitter.on(event as keyof Chat4Events, wrappedHandler)
    
    // 记录订阅
    if (!this.subscriptions.has(event)) {
      this.subscriptions.set(event, new Set())
    }
    
    const unsubscribe = () => {
      this.emitter.off(event as keyof Chat4Events, wrappedHandler)
      this.subscriptions.get(event)?.delete(unsubscribe)
    }
    
    this.subscriptions.get(event)!.add(unsubscribe)
    
    return unsubscribe
  }

  /**
   * 发布事件
   * @param event 事件名
   * @param data 事件数据
   */
  publish<T>(event: string, data: T): void {
    if (this.isDestroyed) {
      console.warn('EventBus has been destroyed, publish ignored')
      return
    }

    // 记录事件历史
    this.recordEventHistory(event, data)
    
    // 发布事件
    this.emitter.emit(event as keyof Chat4Events, data)
    
    // 调试日志
    if (process.env.NODE_ENV === 'development') {
      console.log(`[EventBus] Published: ${event}`, data)
    }
  }

  /**
   * 批量订阅事件
   * @param events 事件配置数组
   * @returns 批量取消订阅函数
   */
  subscribeMultiple(events: Array<{ event: string; handler: EventHandler }>): () => void {
    const unsubscribers = events.map(({ event, handler }) => 
      this.subscribe(event, handler)
    )
    
    return () => {
      unsubscribers.forEach(unsubscribe => unsubscribe())
    }
  }

  /**
   * 一次性事件订阅
   * @param event 事件名
   * @param handler 处理函数
   * @returns 取消订阅函数
   */
  once<T>(event: string, handler: EventHandler<T>): () => void {
    let unsubscribe: (() => void) | null = null
    
    unsubscribe = this.subscribe<T>(event, async (data) => {
      if (unsubscribe) {
        unsubscribe()
        unsubscribe = null
      }
      await handler(data)
    })
    
    return () => {
      if (unsubscribe) {
        unsubscribe()
        unsubscribe = null
      }
    }
  }

  /**
   * 条件订阅
   * @param event 事件名
   * @param condition 条件函数
   * @param handler 处理函数
   * @returns 取消订阅函数
   */
  subscribeIf<T>(
    event: string, 
    condition: (data: T) => boolean,
    handler: EventHandler<T>
  ): () => void {
    return this.subscribe<T>(event, async (data) => {
      if (condition(data)) {
        await handler(data)
      }
    })
  }

  /**
   * 获取事件历史
   * @param event 事件名
   * @param limit 限制数量
   * @returns 事件历史数组
   */
  getEventHistory(event: string, limit = 10): any[] {
    const history = this.eventHistory.get(event) || []
    return history.slice(-limit)
  }

  /**
   * 获取所有活跃订阅
   * @returns 订阅统计
   */
  getSubscriptionStats(): Record<string, number> {
    const stats: Record<string, number> = {}
    
    for (const [event, subscriptions] of this.subscriptions) {
      stats[event] = subscriptions.size
    }
    
    return stats
  }

  /**
   * 清除所有订阅和历史
   */
  clear(): void {
    // 取消所有订阅
    for (const subscriptions of this.subscriptions.values()) {
      subscriptions.forEach(unsubscribe => unsubscribe())
    }
    
    // 清空数据
    this.subscriptions.clear()
    this.eventHistory.clear()
    this.emitter.all.clear()
    
    if (process.env.NODE_ENV === 'development') {
      console.log('[EventBus] All subscriptions and history cleared')
    }
  }

  /**
   * 销毁事件总线
   */
  destroy(): void {
    this.clear()
    this.isDestroyed = true
    
    if (process.env.NODE_ENV === 'development') {
      console.log('[EventBus] EventBus destroyed')
    }
  }

  /**
   * 创建错误边界处理函数
   * @param event 事件名
   * @param handler 原始处理函数
   * @returns 包装后的处理函数
   */
  private createErrorBoundaryHandler<T>(event: string, handler: EventHandler<T>) {
    return async (data: T) => {
      try {
        await handler(data)
      } catch (error) {
        console.error(`[EventBus] Error in handler for event '${event}':`, error)
        
        // 发布错误事件
        this.publish('system:handler_error', {
          event,
          error: error instanceof Error ? error.message : String(error),
          data
        })
      }
    }
  }

  /**
   * 记录事件历史
   * @param event 事件名
   * @param data 事件数据
   */
  private recordEventHistory(event: string, data: any): void {
    if (!this.eventHistory.has(event)) {
      this.eventHistory.set(event, [])
    }
    
    const history = this.eventHistory.get(event)!
    
    // 添加时间戳
    const eventRecord = {
      timestamp: Date.now(),
      data
    }
    
    history.push(eventRecord)
    
    // 限制历史大小
    if (history.length > this.maxHistorySize) {
      history.shift()
    }
  }
}

/**
 * 全局事件总线实例
 */
export const globalEventBus = new Chat4EventBus()

/**
 * 创建命名空间事件总线
 * @param namespace 命名空间
 * @returns 命名空间事件总线
 */
export function createNamespacedEventBus(namespace: string): EventBus {
  const namespacedBus: EventBus = {
    subscribe<T>(event: string, handler: EventHandler<T>) {
      return globalEventBus.subscribe(`${namespace}:${event}`, handler)
    },
    
    publish<T>(event: string, data: T) {
      globalEventBus.publish(`${namespace}:${event}`, data)
    },
    
    clear() {
      // 只清理当前命名空间的事件
      const stats = globalEventBus.getSubscriptionStats()
      Object.keys(stats).forEach(event => {
        if (event.startsWith(`${namespace}:`)) {
          // 这里需要实现命名空间特定的清理逻辑
          // 目前使用全局清理，后续可以优化
        }
      })
    }
  }
  
  return namespacedBus
}

/**
 * 事件总线工具函数
 */
export const EventBusUtils = {
  /**
   * 创建防抖的事件处理函数
   * @param handler 原始处理函数
   * @param delay 防抖延迟（毫秒）
   * @returns 防抖处理函数
   */
  debounce<T>(handler: EventHandler<T>, delay = 300): EventHandler<T> {
    let timeoutId: NodeJS.Timeout | null = null
    
    return async (data: T) => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      
      timeoutId = setTimeout(async () => {
        await handler(data)
        timeoutId = null
      }, delay)
    }
  },

  /**
   * 创建节流的事件处理函数
   * @param handler 原始处理函数
   * @param interval 节流间隔（毫秒）
   * @returns 节流处理函数
   */
  throttle<T>(handler: EventHandler<T>, interval = 300): EventHandler<T> {
    let lastCall = 0
    
    return async (data: T) => {
      const now = Date.now()
      
      if (now - lastCall >= interval) {
        lastCall = now
        await handler(data)
      }
    }
  },

  /**
   * 创建带重试的事件处理函数
   * @param handler 原始处理函数
   * @param maxRetries 最大重试次数
   * @param retryDelay 重试延迟（毫秒）
   * @returns 带重试的处理函数
   */
  withRetry<T>(
    handler: EventHandler<T>, 
    maxRetries = 3, 
    retryDelay = 1000
  ): EventHandler<T> {
    return async (data: T) => {
      let attempts = 0
      
      while (attempts <= maxRetries) {
        try {
          await handler(data)
          return
        } catch (error) {
          attempts++
          
          if (attempts > maxRetries) {
            throw error
          }
          
          await new Promise(resolve => setTimeout(resolve, retryDelay * attempts))
        }
      }
    }
  }
}