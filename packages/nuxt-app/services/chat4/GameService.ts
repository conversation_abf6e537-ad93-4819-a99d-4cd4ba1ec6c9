/**
 * Chat4 游戏核心服务
 * 组合所有服务，提供统一的游戏业务逻辑接口
 * 使用依赖注入模式，零if-else，事件驱动
 */

import type {
  GameService,
  Chat4Result,
  InitParams,
  MessageContent,
  SceneType,
  ModernFavorabilityState,
  GameState,
  EventBus,
  StateMachine,
  Chat4ApiAdapter,
  Storage,
  Chat4Config,
  GameEvent,
  ModernMessage
} from '~/types/chat4'
import {
  ok,
  err,
  Chat4Error,
  createMessage,
  createEvent,
  validateSchema,
  InitParamsSchema
} from '~/types/chat4'

/**
 * 游戏服务状态
 */
interface GameServiceState {
  isInitialized: boolean
  currentSessionId: string | null
  actorId: string | null
  storyId: string | null
  currentScene: SceneType | null
  favorability: ModernFavorabilityState | null
  isSSEConnected: boolean
  messageCount: number
}

/**
 * 游戏服务错误类
 */
export class GameServiceError extends Chat4Error {
  constructor(message: string, public operation: string) {
    super(message, 'GAME_SERVICE_ERROR', { operation })
    this.name = 'GameServiceError'
  }
}

/**
 * Chat4 游戏核心服务实现
 * 特性：
 * - 依赖注入架构，易于测试
 * - 事件驱动，完全解耦
 * - Result模式错误处理
 * - 状态机驱动的生命周期管理
 * - 自动重连和恢复
 */
export class Chat4GameService implements GameService {
  private state: GameServiceState = {
    isInitialized: false,
    currentSessionId: null,
    actorId: null,
    storyId: null,
    currentScene: null,
    favorability: null,
    isSSEConnected: false,
    messageCount: 0
  }
  
  private sseStream: ReadableStream<GameEvent> | null = null
  private sseReader: ReadableStreamDefaultReader<GameEvent> | null = null
  private cleanupTasks: (() => Promise<void>)[] = []

  constructor(
    private eventBus: EventBus,
    private stateMachine: StateMachine,
    private apiAdapter: Chat4ApiAdapter,
    private storage: Storage,
    private config: Chat4Config
  ) {
    this.setupEventHandlers()
  }

  /**
   * 初始化游戏
   * @param params 初始化参数
   * @returns 初始化结果
   */
  async initialize(params: InitParams): Promise<Chat4Result<void>> {
    // 参数验证
    const validation = validateSchema(InitParamsSchema, params)
    if (validation.isErr()) {
      return err(validation.error)
    }

    // 防止重复初始化
    if (this.state.isInitialized) {
      return err(new GameServiceError('Game already initialized', 'initialize'))
    }

    try {
      // 更新状态
      this.state.actorId = params.actorId
      this.state.storyId = params.storyId

      // 发布初始化开始事件
      this.eventBus.publish('game:initialization_started', {
        actorId: params.actorId,
        storyId: params.storyId,
        config: params.config
      })

      // 状态机转换到初始化状态
      const initTransition = await this.stateMachine.transition('initializing', {
        actorId: params.actorId,
        storyId: params.storyId
      })

      if (initTransition.isErr()) {
        return err(new GameServiceError(`State transition failed: ${initTransition.error.message}`, 'initialize'))
      }

      // 根据配置决定是继续游戏还是重新开始
      if (params.config?.loadHistory === true) {
        console.log('🔄 [GameService] Continue模式：尝试加载历史记录并恢复游戏状态')
        // Continue模式：先加载历史记录，再恢复游戏状态
        const continueResult = await this.continueGame()
        if (continueResult.isErr()) {
          console.warn('[GameService] Continue failed, starting new game:', continueResult.error)
          // 如果继续游戏失败，降级为新游戏
          const restartResult = await this.restartGame()
          if (restartResult.isErr()) {
            return err(new GameServiceError(`Failed to start game: ${restartResult.error.message}`, 'initialize'))
          }
        }
      } else {
        console.log('🆕 [GameService] Restart模式：直接调用game.start开始新游戏')
        // Restart模式：直接开始新游戏
        const restartResult = await this.restartGame()
        if (restartResult.isErr()) {
          return err(new GameServiceError(`Failed to start new game: ${restartResult.error.message}`, 'initialize'))
        }
      }

      // 标记为已初始化
      this.state.isInitialized = true

      // 发布初始化完成事件
      this.eventBus.publish('game:initialized', {
        sessionId: this.state.currentSessionId!,
        actorId: params.actorId,
        storyId: params.storyId,
        scene: this.state.currentScene,
        favorability: this.state.favorability
      })

      return ok(undefined)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      return err(new GameServiceError(`Initialization failed: ${errorMessage}`, 'initialize'))
    }
  }

  /**
   * 发送消息
   * @param content 消息内容
   * @returns 发送结果
   */
  async sendMessage(content: MessageContent): Promise<Chat4Result<void>> {
    if (!this.state.isInitialized) {
      return err(new GameServiceError('Game not initialized', 'sendMessage'))
    }

    try {
      // 创建消息对象
      const message = createMessage(
        this.getMessageType(content),
        content,
        {
          id: 'user',
          name: 'User',
          avatar: undefined
        },
        this.state.currentScene || undefined
      )

      // 发布消息发送前事件
      this.eventBus.publish('message:sending', {
        message,
        scene: this.state.currentScene
      })

      // 发送到API
      const sendResult = await this.apiAdapter.sendMessage(message)
      if (sendResult.isErr()) {
        this.eventBus.publish('message:send_failed', {
          error: sendResult.error.message,
          message
        })
        return err(new GameServiceError(`Failed to send message: ${sendResult.error.message}`, 'sendMessage'))
      }

      // 保存到本地存储
      const saveResult = await this.storage.set(`message:${message.id}`, message)
      if (saveResult instanceof Error) {
        console.warn('[GameService] Failed to save message locally:', saveResult)
      }

      // 更新计数器
      this.state.messageCount++

      // 发布消息发送成功事件
      this.eventBus.publish('message:sent', {
        message,
        scene: this.state.currentScene
      })

      return ok(undefined)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      return err(new GameServiceError(`Send message failed: ${errorMessage}`, 'sendMessage'))
    }
  }

  /**
   * 切换场景
   * @param sceneType 场景类型
   * @returns 切换结果
   */
  async changeScene(sceneType: SceneType): Promise<Chat4Result<void>> {
    if (!this.state.isInitialized) {
      return err(new GameServiceError('Game not initialized', 'changeScene'))
    }

    const previousScene = this.state.currentScene

    try {
      // 发布场景切换开始事件
      this.eventBus.publish('scene:transition_start', {
        from: previousScene,
        to: sceneType,
        timestamp: Date.now()
      })

      // 状态机转换到场景切换状态
      const transitionResult = await this.stateMachine.transition('scene_transitioning', {
        scene: sceneType
      })

      if (transitionResult.isErr()) {
        return err(new GameServiceError(`Scene transition failed: ${transitionResult.error.message}`, 'changeScene'))
      }

      // 更新当前场景
      this.state.currentScene = sceneType

      // 保存场景状态
      await this.saveGameState()

      // 发布场景切换完成事件
      this.eventBus.publish('scene:changed', {
        from: previousScene,
        to: sceneType,
        timestamp: Date.now()
      })

      // 状态机转换回游戏进行状态
      await this.stateMachine.transition('playing')

      return ok(undefined)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      return err(new GameServiceError(`Scene change failed: ${errorMessage}`, 'changeScene'))
    }
  }

  /**
   * 获取好感度状态
   * @returns 好感度状态
   */
  getFavorability(): ModernFavorabilityState {
    return this.state.favorability || {
      currentValue: 0,
      maxValue: 100,
      level: 1,
      maxLevel: 10,
      progress: 0,
      nextThreshold: 100
    }
  }

  /**
   * 获取当前场景
   * @returns 当前场景类型
   */
  getCurrentScene(): SceneType | null {
    return this.state.currentScene
  }

  /**
   * 获取游戏状态
   * @returns 游戏状态
   */
  getGameState(): GameState {
    return this.stateMachine.currentState
  }

  /**
   * 获取游戏统计信息
   * @returns 统计信息
   */
  getGameStats(): {
    isInitialized: boolean
    sessionId: string | null
    currentScene: SceneType | null
    messageCount: number
    isSSEConnected: boolean
    gameState: GameState
  } {
    return {
      isInitialized: this.state.isInitialized,
      sessionId: this.state.currentSessionId,
      currentScene: this.state.currentScene,
      messageCount: this.state.messageCount,
      isSSEConnected: this.state.isSSEConnected,
      gameState: this.getGameState()
    }
  }

  /**
   * 暂停游戏
   * @returns 暂停结果
   */
  async pauseGame(): Promise<Chat4Result<void>> {
    const result = await this.stateMachine.transition('paused')
    if (result.isOk()) {
      this.eventBus.publish('game:paused', {
        sessionId: this.state.currentSessionId,
        timestamp: Date.now()
      })
    }
    return result
  }

  /**
   * 恢复游戏
   * @returns 恢复结果
   */
  async resumeGame(): Promise<Chat4Result<void>> {
    const result = await this.stateMachine.transition('playing')
    if (result.isOk()) {
      this.eventBus.publish('game:resumed', {
        sessionId: this.state.currentSessionId,
        timestamp: Date.now()
      })
    }
    return result
  }

  /**
   * 结束游戏
   * @param reason 结束原因
   * @returns 结束结果
   */
  async endGame(reason = 'user_requested'): Promise<Chat4Result<void>> {
    const result = await this.stateMachine.transition('ending', { reason })
    if (result.isOk()) {
      this.eventBus.publish('game:ended', {
        sessionId: this.state.currentSessionId,
        reason,
        timestamp: Date.now(),
        stats: this.getGameStats()
      })
    }
    return result
  }

  /**
   * 继续游戏 (公共方法)
   * 加载历史记录并恢复游戏状态
   * @returns 继续结果
   */
  async continueGameFromHistory(): Promise<Chat4Result<void>> {
    if (!this.state.isInitialized) {
      return err(new GameServiceError('Game not initialized', 'continueGameFromHistory'))
    }

    return this.continueGame()
  }

  /**
   * 重新开始游戏 (公共方法)
   * 调用game.start开始全新游戏
   * @returns 重启结果
   */
  async restartGameFromBeginning(): Promise<Chat4Result<void>> {
    if (!this.state.isInitialized) {
      return err(new GameServiceError('Game not initialized', 'restartGameFromBeginning'))
    }

    // 清除当前游戏状态
    this.state.currentSessionId = null
    this.state.currentScene = null
    this.state.favorability = null
    this.state.messageCount = 0

    // 断开现有连接
    await this.disconnectSSE()

    return this.restartGame()
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    // 执行所有清理任务
    await Promise.all(this.cleanupTasks.map(task => task().catch(console.error)))

    // 关闭SSE连接
    await this.disconnectSSE()

    // 断开API连接
    await this.apiAdapter.disconnect()

    // 重置状态
    this.state = {
      isInitialized: false,
      currentSessionId: null,
      actorId: null,
      storyId: null,
      currentScene: null,
      favorability: null,
      isSSEConnected: false,
      messageCount: 0
    }

    // 发布清理完成事件
    this.eventBus.publish('game:cleanup_completed', {
      timestamp: Date.now()
    })

    if (process.env.NODE_ENV === 'development') {
      console.log('[GameService] Cleanup completed')
    }
  }

  /**
   * 继续游戏 (Continue模式)
   * 先加载历史记录，再恢复游戏状态
   * @private
   */
  private async continueGame(): Promise<Chat4Result<void>> {
    console.log('[GameService] 继续模式：加载历史记录并恢复游戏状态')
    
    // 1. 加载历史记录
    const historyResult = await this.loadGameHistory()
    if (historyResult.isErr()) {
      return err(new GameServiceError(`Failed to load history: ${historyResult.error.message}`, 'continueGame'))
    }

    // 2. 如果有历史记录，恢复游戏状态（不调用game.start）
    if (this.state.currentScene && this.state.favorability) {
      // 已有游戏状态，直接连接SSE恢复实时通信
      console.log('[GameService] 恢复已有游戏状态，建立SSE连接')
      
      // 生成或恢复sessionId
      const sessionId = this.state.currentSessionId || `continue_${Date.now()}`
      this.state.currentSessionId = sessionId
      
      // 连接SSE事件流
      const sseResult = await this.connectSSE(sessionId)
      if (sseResult.isErr()) {
        console.warn('[GameService] SSE connection failed:', sseResult.error)
      }

      // 状态机转换到游戏进行状态
      await this.stateMachine.transition('playing')

      // 发布游戏恢复事件
      this.eventBus.publish('game:continued', {
        sessionId: this.state.currentSessionId,
        scene: this.state.currentScene,
        favorability: this.state.favorability,
        messageCount: this.state.messageCount
      })

      return ok(undefined)
    } else {
      // 没有历史记录，降级为新游戏
      return err(new GameServiceError('No game history found, need to start new game', 'continueGame'))
    }
  }

  /**
   * 重新开始游戏 (Restart模式)
   * 直接调用game.start开始新游戏
   * @private
   */
  private async restartGame(): Promise<Chat4Result<void>> {
    console.log('[GameService] 重新开始模式：调用game.start开始新游戏')
    
    // 直接启动新游戏会话
    return this.startGameSession()
  }

  /**
   * 加载游戏历史记录
   * @private
   */
  private async loadGameHistory(): Promise<Chat4Result<void>> {
    if (!this.state.actorId || !this.state.storyId) {
      return err(new GameServiceError('Actor ID and Story ID required', 'loadGameHistory'))
    }

    // 状态机转换到加载历史状态
    const transition = await this.stateMachine.transition('loading_history')
    if (transition.isErr()) {
      return err(transition.error)
    }

    // 从API获取历史记录
    const historyResult = await this.apiAdapter.getHistory({
      actorId: this.state.actorId,
      storyId: this.state.storyId
    })

    if (historyResult.isErr()) {
      return err(new GameServiceError(`Failed to load history: ${historyResult.error.message}`, 'loadGameHistory'))
    }

    const historyData = historyResult.value.data
    if (historyData) {
      // 更新好感度状态
      this.state.favorability = historyData.favorability
      this.state.currentScene = historyData.scene

      // 保存消息到本地存储
      for (const message of historyData.messages) {
        await this.storage.set(`message:${message.id}`, message)
      }

      // 发布历史加载完成事件
      this.eventBus.publish('game:history_loaded', {
        messageCount: historyData.messages.length,
        favorability: historyData.favorability,
        scene: historyData.scene
      })
    }

    return ok(undefined)
  }

  /**
   * 启动新游戏会话 (调用game.start)
   * @private
   */
  private async startGameSession(): Promise<Chat4Result<void>> {
    if (!this.state.actorId || !this.state.storyId) {
      return err(new GameServiceError('Actor ID and Story ID required', 'startGameSession'))
    }

    console.log('🚀 [GameService] startGameSession: 准备调用API启动新游戏', {
      actorId: this.state.actorId,
      storyId: this.state.storyId
    })

    // 状态机转换到启动游戏状态
    const transition = await this.stateMachine.transition('starting_game')
    if (transition.isErr()) {
      return err(transition.error)
    }

    console.log('📡 [GameService] 调用apiAdapter.startGame')
    
    // 调用API启动游戏
    const startResult = await this.apiAdapter.startGame({
      actorId: this.state.actorId,
      storyId: this.state.storyId,
      config: {
        autoStart: true,
        loadHistory: false,
        enableSound: this.config.features.enableSound
      }
    })

    if (startResult.isErr()) {
      console.error('❌ [GameService] API调用失败:', startResult.error.message)
      return err(new GameServiceError(`Failed to start game: ${startResult.error.message}`, 'startGameSession'))
    }

    console.log('✅ [GameService] API调用成功，收到游戏会话数据')

    const sessionData = startResult.value.data
    if (sessionData) {
      // 更新会话状态
      this.state.currentSessionId = sessionData.sessionId
      this.state.currentScene = sessionData.initialScene
      this.state.favorability = sessionData.favorability

      // 保存游戏状态
      await this.saveGameState()

      // 连接SSE事件流
      const sseResult = await this.connectSSE(sessionData.sessionId)
      if (sseResult.isErr()) {
        console.warn('[GameService] SSE connection failed:', sseResult.error)
      }

      // 状态机转换到游戏进行状态
      await this.stateMachine.transition('playing')

      console.log('🎉 [GameService] 新游戏启动完成', {
        sessionId: sessionData.sessionId,
        scene: sessionData.initialScene
      })
      
      // 发布游戏启动完成事件
      this.eventBus.publish('game:started', {
        sessionId: sessionData.sessionId,
        scene: sessionData.initialScene,
        favorability: sessionData.favorability
      })
    }

    return ok(undefined)
  }

  /**
   * 连接SSE事件流
   * @private
   */
  private async connectSSE(sessionId: string): Promise<Chat4Result<void>> {
    const sseResult = await this.apiAdapter.connectSSE(sessionId)
    if (sseResult.isErr()) {
      return err(sseResult.error)
    }

    this.sseStream = sseResult.value
    this.sseReader = this.sseStream.getReader()
    this.state.isSSEConnected = true

    // 启动事件处理循环
    this.startSSEProcessing()

    this.eventBus.publish('connection:established', {
      sessionId,
      timestamp: Date.now()
    })

    return ok(undefined)
  }

  /**
   * 启动SSE事件处理
   * @private
   */
  private async startSSEProcessing(): Promise<void> {
    if (!this.sseReader) return

    try {
      while (this.state.isSSEConnected) {
        const { done, value } = await this.sseReader.read()
        
        if (done) {
          break
        }

        if (value) {
          await this.handleGameEvent(value)
        }
      }
    } catch (error) {
      console.error('[GameService] SSE processing error:', error)
      this.state.isSSEConnected = false
      
      this.eventBus.publish('connection:lost', {
        reason: error instanceof Error ? error.message : 'Unknown error',
        willReconnect: true
      })
    }
  }

  /**
   * 处理游戏事件
   * @private
   */
  private async handleGameEvent(event: GameEvent): Promise<void> {
    try {
      // 根据事件类型分发处理
      switch (event.type) {
        case 'message':
          await this.handleMessageEvent(event)
          break
        case 'scene_change':
          await this.handleSceneChangeEvent(event)
          break
        case 'favorability_update':
          await this.handleFavorabilityUpdateEvent(event)
          break
        case 'heart_value':
          await this.handleHeartValueEvent(event)
          break
        case 'payment_required':
          await this.handlePaymentRequiredEvent(event)
          break
        case 'game_end':
          await this.handleGameEndEvent(event)
          break
        case 'error':
          await this.handleErrorEvent(event)
          break
        default:
          console.warn('[GameService] Unknown event type:', event.type)
      }

      // 发布通用事件接收事件
      this.eventBus.publish('game:event_received', event)
    } catch (error) {
      console.error('[GameService] Event handling error:', error)
    }
  }

  /**
   * 处理消息事件
   * @private
   */
  private async handleMessageEvent(event: GameEvent): Promise<void> {
    const message = event.data as ModernMessage
    
    // 保存消息到本地存储
    await this.storage.set(`message:${message.id}`, message)
    
    // 发布消息接收事件
    this.eventBus.publish('message:received', {
      message,
      scene: this.state.currentScene
    })
  }

  /**
   * 处理场景变化事件
   * @private
   */
  private async handleSceneChangeEvent(event: GameEvent): Promise<void> {
    const previousScene = this.state.currentScene
    const newScene = event.data.scene as SceneType
    
    this.state.currentScene = newScene
    await this.saveGameState()
    
    this.eventBus.publish('scene:changed', {
      from: previousScene,
      to: newScene,
      timestamp: Date.now()
    })
  }

  /**
   * 处理好感度更新事件
   * @private
   */
  private async handleFavorabilityUpdateEvent(event: GameEvent): Promise<void> {
    const previous = this.state.favorability
    this.state.favorability = event.data as ModernFavorabilityState
    
    await this.saveGameState()
    
    this.eventBus.publish('favorability:updated', {
      current: this.state.favorability.currentValue,
      previous: previous?.currentValue || 0,
      level: this.state.favorability.level
    })
  }

  /**
   * 处理心值事件
   * @private
   */
  private async handleHeartValueEvent(event: GameEvent): Promise<void> {
    this.eventBus.publish('favorability:heart_value_updated', {
      value: event.data.value,
      scene: this.state.currentScene,
      timestamp: Date.now()
    })
  }

  /**
   * 处理支付需求事件
   * @private
   */
  private async handlePaymentRequiredEvent(event: GameEvent): Promise<void> {
    this.eventBus.publish('payment:required', {
      amount: event.data.amount,
      scene: this.state.currentScene,
      reason: event.data.reason
    })
  }

  /**
   * 处理游戏结束事件
   * @private
   */
  private async handleGameEndEvent(event: GameEvent): Promise<void> {
    await this.stateMachine.transition('ending', { reason: event.data.reason })
    
    this.eventBus.publish('game:ended', {
      sessionId: this.state.currentSessionId,
      reason: event.data.reason,
      timestamp: Date.now()
    })
  }

  /**
   * 处理错误事件
   * @private
   */
  private async handleErrorEvent(event: GameEvent): Promise<void> {
    await this.stateMachine.transition('error', { error: event.data.error })
    
    this.eventBus.publish('game:error', {
      error: event.data.error,
      code: event.data.code,
      timestamp: Date.now()
    })
  }

  /**
   * 断开SSE连接
   * @private
   */
  private async disconnectSSE(): Promise<void> {
    if (this.sseReader) {
      await this.sseReader.cancel()
      this.sseReader = null
    }
    
    this.sseStream = null
    this.state.isSSEConnected = false
  }

  /**
   * 保存游戏状态
   * @private
   */
  private async saveGameState(): Promise<void> {
    if (!this.state.actorId || !this.state.storyId) return

    const gameState = {
      sessionId: this.state.currentSessionId,
      currentScene: this.state.currentScene,
      favorability: this.state.favorability,
      messageCount: this.state.messageCount,
      lastUpdated: Date.now()
    }

    await this.storage.set(
      `gameState:${this.state.actorId}:${this.state.storyId}`,
      gameState
    )
  }

  /**
   * 设置事件处理器
   * @private
   */
  private setupEventHandlers(): void {
    // 监听状态机事件
    this.stateMachine.on('game:state_changed', (data) => {
      this.eventBus.publish('game:state_changed', data)
    })

    // 添加清理任务
    this.cleanupTasks.push(async () => {
      this.eventBus.clear()
    })
  }

  /**
   * 获取消息类型
   * @private
   */
  private getMessageType(content: MessageContent): 'text' | 'image' | 'audio' | 'video' | 'emoji' {
    if (content.text) return 'text'
    if (content.imageUrl) return 'image'
    if (content.audioUrl) return 'audio'
    if (content.videoUrl) return 'video'
    if (content.emoji) return 'emoji'
    return 'text'
  }
}

/**
 * 游戏服务工厂函数
 * @param dependencies 依赖注入对象
 * @returns 游戏服务实例
 */
export function createChat4GameService(dependencies: {
  eventBus: EventBus
  stateMachine: StateMachine
  apiAdapter: Chat4ApiAdapter
  storage: Storage
  config: Chat4Config
}): GameService {
  return new Chat4GameService(
    dependencies.eventBus,
    dependencies.stateMachine,
    dependencies.apiAdapter,
    dependencies.storage,
    dependencies.config
  )
}