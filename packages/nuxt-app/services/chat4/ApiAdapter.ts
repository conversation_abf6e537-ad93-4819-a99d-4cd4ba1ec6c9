/**
 * Chat4 API 适配器
 * 使用ky HTTP客户端，Result模式，零try-catch
 */

import ky, { type KyInstance } from 'ky'
import type { 
  Chat4ApiAdapter, 
  Chat4Result, 
  InitParams, 
  ChatHistoryResponse, 
  GameSessionResponse,
  ModernMessage,
  GameEvent,
  Chat4Config
} from '~/types/chat4'
import { 
  ok, 
  err, 
  validateSchema,
  InitParamsSchema,
  ChatHistoryResponseSchema,
  GameSessionResponseSchema,
  NetworkError 
} from '~/types/chat4'

/**
 * SSE 连接状态
 */
interface SSEConnectionState {
  eventSource: EventSource | null
  isConnected: boolean
  reconnectAttempts: number
  sessionId: string
}

/**
 * HTTP Chat4 API 适配器实现
 * 特性：
 * - Result模式错误处理，零try-catch
 * - 自动重试机制
 * - 请求/响应验证
 * - SSE连接管理
 * - 连接池管理
 */
export class HttpChat4ApiAdapter implements Chat4ApiAdapter {
  private client: KyInstance
  private sseState: SSEConnectionState = {
    eventSource: null,
    isConnected: false,
    reconnectAttempts: 0,
    sessionId: ''
  }
  private config: Chat4Config
  private eventController: AbortController | null = null

  constructor(config: Chat4Config) {
    this.config = config
    
    // 创建配置化的HTTP客户端
    this.client = ky.create({
      prefixUrl: config.api.baseUrl,
      timeout: config.api.timeout,
      retry: {
        limit: config.api.retryAttempts,
        methods: ['get', 'post'],
        statusCodes: [408, 413, 429, 500, 502, 503, 504]
      },
      hooks: {
        beforeRequest: [
          (request) => {
            // 添加通用请求头
            request.headers.set('Content-Type', 'application/json')
            request.headers.set('Accept', 'application/json')
            
            // 开发环境日志
            if (process.env.NODE_ENV === 'development') {
              console.log(`[ApiAdapter] ${request.method} ${request.url}`)
            }
          }
        ],
        beforeError: [
          (error) => {
            // 统一错误日志
            console.error(`[ApiAdapter] Request failed:`, error)
            return error
          }
        ]
      }
    })
  }

  /**
   * 获取聊天历史记录
   * @param params 查询参数
   * @returns 聊天历史结果
   */
  async getHistory(params: { storyId: string; actorId: string }): Promise<Chat4Result<ChatHistoryResponse>> {
    console.log('📚 [ApiAdapter] getHistory: 准备调用chat/history API', {
      actorId: params.actorId,
      storyId: params.storyId
    })
    
    // 参数验证
    const paramsValidation = validateSchema(
      InitParamsSchema.pick({ storyId: true, actorId: true }),
      params
    )
    
    if (paramsValidation.isErr()) {
      console.error('❌ [ApiAdapter] 参数验证失败:', paramsValidation.error)
      return err(paramsValidation.error)
    }

    console.log('📡 [ApiAdapter] 发送GET请求到 chat/history', {
      url: `${this.client.options.prefixUrl}chat/history`,
      params: { story_id: params.storyId, actor_id: params.actorId }
    })

    // 执行API请求
    const result = await this.safeApiCall(
      () => this.client.get('chat/history', {
        searchParams: {
          story_id: params.storyId,
          actor_id: params.actorId
        }
      }).json(),
      ChatHistoryResponseSchema,
      'Failed to fetch chat history'
    )
    
    if (result.isOk()) {
      console.log('✅ [ApiAdapter] chat/history API调用成功')
    } else {
      console.error('❌ [ApiAdapter] chat/history API调用失败:', result.error)
    }
    
    return result
  }

  /**
   * 启动游戏会话
   * @param params 初始化参数
   * @returns 游戏会话结果
   */
  async startGame(params: InitParams): Promise<Chat4Result<GameSessionResponse>> {
    console.log('🚀 [ApiAdapter] startGame: 准备调用game/start API', {
      actorId: params.actorId,
      storyId: params.storyId,
      config: params.config
    })
    
    // 参数验证
    const paramsValidation = validateSchema(InitParamsSchema, params)
    
    if (paramsValidation.isErr()) {
      console.error('❌ [ApiAdapter] 参数验证失败:', paramsValidation.error)
      return err(paramsValidation.error)
    }

    // 构造请求体
    const requestBody = {
      actor_id: params.actorId,
      story_id: params.storyId,
      user_id: params.userId,
      config: {
        auto_start: params.config?.autoStart ?? true,
        load_history: params.config?.loadHistory ?? true,
        enable_sound: params.config?.enableSound ?? true
      }
    }

    console.log('📡 [ApiAdapter] 发送POST请求到 game/start', {
      url: `${this.client.options.prefixUrl}game/start`,
      body: requestBody
    })

    // 执行API请求
    const result = await this.safeApiCall(
      () => this.client.post('game/start', {
        json: requestBody
      }).json(),
      GameSessionResponseSchema,
      'Failed to start game session'
    )
    
    if (result.isOk()) {
      console.log('✅ [ApiAdapter] game/start API调用成功')
    } else {
      console.error('❌ [ApiAdapter] game/start API调用失败:', result.error)
    }
    
    return result
  }

  /**
   * 发送消息
   * @param message 消息对象
   * @returns 发送结果
   */
  async sendMessage(message: Omit<ModernMessage, 'id' | 'timestamp'>): Promise<Chat4Result<void>> {
    // 构造请求体
    const requestBody = {
      type: message.type,
      content: message.content,
      sender: message.sender,
      scene: message.scene
    }

    // 执行API请求
    const result = await this.safeApiCall(
      () => this.client.post('chat/message', {
        json: requestBody
      }).json(),
      ChatHistoryResponseSchema, // 复用验证Schema
      'Failed to send message'
    )

    // 转换为void结果
    return result.isOk() ? ok(undefined) : err(result.error)
  }

  /**
   * 连接SSE事件流
   * @param sessionId 会话ID
   * @returns 事件流
   */
  async connectSSE(sessionId: string): Promise<Chat4Result<ReadableStream<GameEvent>>> {
    if (!sessionId) {
      return err(new NetworkError('Session ID is required for SSE connection'))
    }

    // 关闭已有连接
    await this.disconnect()

    // 创建新的abort controller
    this.eventController = new AbortController()

    try {
      // 构造SSE URL
      const sseUrl = new URL('game/events', this.config.api.baseUrl)
      sseUrl.searchParams.set('session_id', sessionId)

      // 创建EventSource
      const eventSource = new EventSource(sseUrl.toString())
      
      // 更新连接状态
      this.sseState = {
        eventSource,
        isConnected: false,
        reconnectAttempts: 0,
        sessionId
      }

      // 创建可读流
      const stream = new ReadableStream<GameEvent>({
        start: (controller) => {
          // 连接成功处理
          eventSource.onopen = () => {
            this.sseState.isConnected = true
            this.sseState.reconnectAttempts = 0
            
            if (process.env.NODE_ENV === 'development') {
              console.log('[ApiAdapter] SSE connection established')
            }
          }

          // 消息处理
          eventSource.onmessage = (event) => {
            try {
              const gameEvent = JSON.parse(event.data) as GameEvent
              controller.enqueue(gameEvent)
            } catch (error) {
              console.error('[ApiAdapter] Failed to parse SSE event:', error)
            }
          }

          // 错误处理
          eventSource.onerror = (error) => {
            console.error('[ApiAdapter] SSE connection error:', error)
            this.sseState.isConnected = false
            
            // 自动重连逻辑
            if (this.sseState.reconnectAttempts < this.config.sse.maxReconnectAttempts) {
              this.sseState.reconnectAttempts++
              
              setTimeout(() => {
                if (!this.sseState.isConnected) {
                  this.reconnectSSE(sessionId)
                }
              }, this.config.sse.reconnectInterval)
            } else {
              controller.error(new NetworkError('SSE connection failed after max retries'))
            }
          }

          // 监听abort信号
          this.eventController?.signal.addEventListener('abort', () => {
            eventSource.close()
            controller.close()
          })
        },

        cancel: () => {
          eventSource.close()
          this.sseState.isConnected = false
        }
      })

      return ok(stream)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      return err(new NetworkError(`Failed to establish SSE connection: ${errorMessage}`))
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    // 关闭SSE连接
    if (this.sseState.eventSource) {
      this.sseState.eventSource.close()
      this.sseState.eventSource = null
    }

    // 取消所有请求
    if (this.eventController) {
      this.eventController.abort()
      this.eventController = null
    }

    // 重置状态
    this.sseState = {
      eventSource: null,
      isConnected: false,
      reconnectAttempts: 0,
      sessionId: ''
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('[ApiAdapter] All connections closed')
    }
  }

  /**
   * 检查连接状态
   */
  isConnected(): boolean {
    return this.sseState.isConnected
  }

  /**
   * 获取当前会话ID
   */
  getCurrentSessionId(): string {
    return this.sseState.sessionId
  }

  /**
   * 安全的API调用包装器
   * @param apiCall API调用函数
   * @param schema 响应验证Schema
   * @param errorMessage 错误消息
   * @returns 类型安全的结果
   */
  private async safeApiCall<T>(
    apiCall: () => Promise<unknown>,
    schema: any,
    errorMessage: string
  ): Promise<Chat4Result<T>> {
    try {
      const response = await apiCall()
      
      // 验证响应数据
      const validation = validateSchema(schema, response)
      
      if (validation.isOk()) {
        return ok(validation.value as T)
      } else {
        return err(new NetworkError(`${errorMessage}: Invalid response format`))
      }
    } catch (error) {
      // 网络错误处理
      if (error instanceof TypeError && error.message.includes('fetch')) {
        return err(new NetworkError(`${errorMessage}: Network connection failed`))
      }
      
      // HTTP状态错误
      const status = (error as any)?.response?.status
      if (status) {
        return err(new NetworkError(`${errorMessage}: HTTP ${status}`, status))
      }
      
      // 其他错误
      const errorMessage2 = error instanceof Error ? error.message : String(error)
      return err(new NetworkError(`${errorMessage}: ${errorMessage2}`))
    }
  }

  /**
   * SSE重连逻辑
   * @param sessionId 会话ID
   */
  private async reconnectSSE(sessionId: string): Promise<void> {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[ApiAdapter] Attempting SSE reconnection (${this.sseState.reconnectAttempts}/${this.config.sse.maxReconnectAttempts})`)
    }

    const result = await this.connectSSE(sessionId)
    
    if (result.isErr()) {
      console.error('[ApiAdapter] SSE reconnection failed:', result.error)
    }
  }
}

/**
 * Mock API 适配器（用于开发和测试）
 */
export class MockChat4ApiAdapter implements Chat4ApiAdapter {
  private mockSessionId = 'mock-session-' + Date.now()
  private isSSEConnected = false

  async getHistory(): Promise<Chat4Result<ChatHistoryResponse>> {
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    const mockResponse: ChatHistoryResponse = {
      success: true,
      data: {
        messages: [
          {
            id: '1',
            type: 'system',
            content: { text: '欢迎回到游戏！' },
            sender: { id: 'system', name: '系统' },
            timestamp: new Date().toISOString(),
            scene: 'Living'
          }
        ],
        favorability: {
          currentValue: 50,
          maxValue: 100,
          level: 1,
          maxLevel: 10,
          progress: 50,
          nextThreshold: 100
        },
        scene: 'Living'
      },
      metadata: {
        timestamp: new Date().toISOString(),
        requestId: 'mock-req-' + Date.now(),
        version: '1.0.0'
      }
    }

    return ok(mockResponse)
  }

  async startGame(): Promise<Chat4Result<GameSessionResponse>> {
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 800))

    const mockResponse: GameSessionResponse = {
      success: true,
      data: {
        sessionId: this.mockSessionId,
        events: [
          {
            id: '1',
            type: 'scene_change',
            timestamp: new Date().toISOString(),
            sceneType: 'Living',
            data: { reason: 'game_start' }
          }
        ],
        initialScene: 'Living',
        favorability: {
          currentValue: 50,
          maxValue: 100,
          level: 1,
          maxLevel: 10,
          progress: 50,
          nextThreshold: 100
        }
      },
      metadata: {
        timestamp: new Date().toISOString(),
        requestId: 'mock-req-' + Date.now(),
        version: '1.0.0'
      }
    }

    return ok(mockResponse)
  }

  async sendMessage(): Promise<Chat4Result<void>> {
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 200))
    return ok(undefined)
  }

  async connectSSE(): Promise<Chat4Result<ReadableStream<GameEvent>>> {
    // 模拟SSE流
    const stream = new ReadableStream<GameEvent>({
      start: (controller) => {
        this.isSSEConnected = true
        
        // 模拟定期事件
        const interval = setInterval(() => {
          if (!this.isSSEConnected) {
            clearInterval(interval)
            return
          }

          const mockEvent: GameEvent = {
            id: 'mock-' + Date.now(),
            type: 'heart_value',
            timestamp: new Date().toISOString(),
            sceneType: 'Living',
            data: { value: Math.floor(Math.random() * 100) }
          }

          controller.enqueue(mockEvent)
        }, 2000)
      },

      cancel: () => {
        this.isSSEConnected = false
      }
    })

    return ok(stream)
  }

  async disconnect(): Promise<void> {
    this.isSSEConnected = false
  }
}

/**
 * API适配器工厂函数
 * @param config 配置对象
 * @param useMock 是否使用Mock实现
 * @returns API适配器实例
 */
export function createChat4ApiAdapter(config: Chat4Config, useMock = false): Chat4ApiAdapter {
  if (useMock || process.env.NODE_ENV === 'development') {
    return new MockChat4ApiAdapter()
  }
  
  return new HttpChat4ApiAdapter(config)
}

/**
 * API适配器工具函数
 */
export const ApiAdapterUtils = {
  /**
   * 检查网络连接状态
   * @returns 是否在线
   */
  isOnline(): boolean {
    return navigator.onLine
  },

  /**
   * 创建重试装饰器
   * @param maxRetries 最大重试次数
   * @param delay 重试延迟
   * @returns 装饰器函数
   */
  withRetry<T extends any[], R>(
    fn: (...args: T) => Promise<Chat4Result<R>>,
    maxRetries = 3,
    delay = 1000
  ) {
    return async (...args: T): Promise<Chat4Result<R>> => {
      let attempts = 0
      
      while (attempts <= maxRetries) {
        const result = await fn(...args)
        
        if (result.isOk() || attempts === maxRetries) {
          return result
        }
        
        attempts++
        await new Promise(resolve => setTimeout(resolve, delay * attempts))
      }
      
      return err(new NetworkError('Maximum retry attempts exceeded'))
    }
  },

  /**
   * 创建超时装饰器
   * @param timeout 超时时间（毫秒）
   * @returns 装饰器函数
   */
  withTimeout<T extends any[], R>(
    fn: (...args: T) => Promise<Chat4Result<R>>,
    timeout = 30000
  ) {
    return async (...args: T): Promise<Chat4Result<R>> => {
      const timeoutPromise = new Promise<Chat4Result<R>>((_, reject) => {
        setTimeout(() => reject(new NetworkError('Request timeout')), timeout)
      })
      
      try {
        return await Promise.race([fn(...args), timeoutPromise])
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error)
        return err(new NetworkError(`Request failed: ${errorMessage}`))
      }
    }
  }
}