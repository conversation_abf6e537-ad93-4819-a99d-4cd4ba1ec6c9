/**
 * Chat4 现代化存储服务
 * 使用IndexedDB + idb，Result模式，类型安全
 */

import { openDB, type DBSchema, type IDBPDatabase } from 'idb'
import type { Storage, Chat4Result, Chat4Config } from '~/types/chat4'
import { ok, err, Chat4Error } from '~/types/chat4'

/**
 * Chat4 数据库Schema定义
 */
interface Chat4DBSchema extends DBSchema {
  // 游戏状态存储
  gameState: {
    key: string
    value: {
      id: string
      actorId: string
      storyId: string
      sessionId?: string
      currentScene?: string
      favorability?: any
      lastUpdated: number
      version: string
    }
  }
  
  // 聊天历史存储
  messages: {
    key: string
    value: {
      id: string
      sessionId: string
      type: string
      content: any
      sender: any
      timestamp: string
      scene?: string
      metadata?: Record<string, any>
    }
    indexes: {
      'by-session': string
      'by-timestamp': string
    }
  }
  
  // 用户配置存储
  userConfig: {
    key: string
    value: {
      id: string
      enableSound: boolean
      enableNotifications: boolean
      theme: string
      language: string
      lastUpdated: number
    }
  }
  
  // 缓存数据存储
  cache: {
    key: string
    value: {
      id: string
      data: any
      ttl: number
      createdAt: number
    }
    indexes: {
      'by-ttl': number
    }
  }
}

/**
 * 存储错误类
 */
export class StorageError extends Chat4Error {
  constructor(message: string, public operation: string) {
    super(message, 'STORAGE_ERROR', { operation })
    this.name = 'StorageError'
  }
}

/**
 * IndexedDB Chat4 存储实现
 * 特性：
 * - 类型安全的存储操作
 * - 自动过期清理
 * - 事务管理
 * - 错误边界处理
 * - 版本管理
 */
export class IndexedDBChat4Storage implements Storage {
  private db: IDBPDatabase<Chat4DBSchema> | null = null
  private config: Chat4Config
  private isInitialized = false
  private cleanupInterval: NodeJS.Timeout | null = null

  constructor(config: Chat4Config) {
    this.config = config
  }

  /**
   * 初始化数据库
   */
  async initialize(): Promise<Chat4Result<void>> {
    if (this.isInitialized) {
      return ok(undefined)
    }

    try {
      this.db = await openDB<Chat4DBSchema>('chat4-db', 1, {
        upgrade: (db) => {
          // 游戏状态表
          if (!db.objectStoreNames.contains('gameState')) {
            db.createObjectStore('gameState', { keyPath: 'id' })
          }

          // 消息表
          if (!db.objectStoreNames.contains('messages')) {
            const messageStore = db.createObjectStore('messages', { keyPath: 'id' })
            messageStore.createIndex('by-session', 'sessionId', { unique: false })
            messageStore.createIndex('by-timestamp', 'timestamp', { unique: false })
          }

          // 用户配置表
          if (!db.objectStoreNames.contains('userConfig')) {
            db.createObjectStore('userConfig', { keyPath: 'id' })
          }

          // 缓存表
          if (!db.objectStoreNames.contains('cache')) {
            const cacheStore = db.createObjectStore('cache', { keyPath: 'id' })
            cacheStore.createIndex('by-ttl', 'ttl', { unique: false })
          }
        }
      })

      this.isInitialized = true
      
      // 启动定期清理
      this.startCleanupTask()

      if (process.env.NODE_ENV === 'development') {
        console.log('[Storage] IndexedDB initialized successfully')
      }

      return ok(undefined)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      return err(new StorageError(`Failed to initialize database: ${errorMessage}`, 'initialize'))
    }
  }

  /**
   * 获取数据
   * @param key 键
   * @returns 数据值或null
   */
  async get<T>(key: string): Promise<T | null> {
    if (!this.db) {
      const initResult = await this.initialize()
      if (initResult.isErr()) {
        return null
      }
    }

    try {
      // 尝试从不同的存储中获取数据
      const stores: (keyof Chat4DBSchema)[] = ['gameState', 'messages', 'userConfig', 'cache']
      
      for (const storeName of stores) {
        const tx = this.db!.transaction(storeName, 'readonly')
        const store = tx.objectStore(storeName)
        const result = await store.get(key)
        
        if (result) {
          // 检查缓存是否过期
          if (storeName === 'cache' && this.isCacheExpired(result)) {
            await this.remove(key)
            return null
          }
          
          return result as T
        }
      }

      return null
    } catch (error) {
      console.error('[Storage] Failed to get data:', error)
      return null
    }
  }

  /**
   * 设置数据
   * @param key 键
   * @param value 值
   */
  async set<T>(key: string, value: T): Promise<void> {
    if (!this.db) {
      const initResult = await this.initialize()
      if (initResult.isErr()) {
        throw new StorageError('Database not initialized', 'set')
      }
    }

    try {
      // 根据键的前缀确定存储类型
      const storeName = this.getStoreNameByKey(key)
      const tx = this.db!.transaction(storeName, 'readwrite')
      const store = tx.objectStore(storeName)
      
      // 准备存储数据
      const storeData = this.prepareStoreData(key, value, storeName)
      
      await store.put(storeData)
      await tx.complete

      if (process.env.NODE_ENV === 'development') {
        console.log(`[Storage] Data saved to ${storeName}:`, key)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      throw new StorageError(`Failed to set data: ${errorMessage}`, 'set')
    }
  }

  /**
   * 删除数据
   * @param key 键
   */
  async remove(key: string): Promise<void> {
    if (!this.db) {
      return
    }

    try {
      const stores: (keyof Chat4DBSchema)[] = ['gameState', 'messages', 'userConfig', 'cache']
      
      for (const storeName of stores) {
        const tx = this.db.transaction(storeName, 'readwrite')
        const store = tx.objectStore(storeName)
        await store.delete(key)
        await tx.complete
      }

      if (process.env.NODE_ENV === 'development') {
        console.log('[Storage] Data removed:', key)
      }
    } catch (error) {
      console.error('[Storage] Failed to remove data:', error)
    }
  }

  /**
   * 清空所有数据
   */
  async clear(): Promise<void> {
    if (!this.db) {
      return
    }

    try {
      const stores: (keyof Chat4DBSchema)[] = ['gameState', 'messages', 'userConfig', 'cache']
      
      for (const storeName of stores) {
        const tx = this.db.transaction(storeName, 'readwrite')
        const store = tx.objectStore(storeName)
        await store.clear()
        await tx.complete
      }

      if (process.env.NODE_ENV === 'development') {
        console.log('[Storage] All data cleared')
      }
    } catch (error) {
      console.error('[Storage] Failed to clear data:', error)
    }
  }

  /**
   * 获取消息历史
   * @param sessionId 会话ID
   * @param limit 限制数量
   * @returns 消息数组
   */
  async getMessageHistory(sessionId: string, limit = 100): Promise<any[]> {
    if (!this.db) {
      return []
    }

    try {
      const tx = this.db.transaction('messages', 'readonly')
      const store = tx.objectStore('messages')
      const index = store.index('by-session')
      
      const messages = await index.getAll(sessionId)
      
      // 按时间戳排序并限制数量
      return messages
        .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
        .slice(-limit)
    } catch (error) {
      console.error('[Storage] Failed to get message history:', error)
      return []
    }
  }

  /**
   * 保存消息
   * @param message 消息对象
   */
  async saveMessage(message: any): Promise<Chat4Result<void>> {
    try {
      const key = `message:${message.id}`
      await this.set(key, message)
      return ok(undefined)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      return err(new StorageError(`Failed to save message: ${errorMessage}`, 'saveMessage'))
    }
  }

  /**
   * 获取游戏状态
   * @param actorId 角色ID
   * @param storyId 故事ID
   * @returns 游戏状态或null
   */
  async getGameState(actorId: string, storyId: string): Promise<any | null> {
    const key = `gameState:${actorId}:${storyId}`
    return this.get(key)
  }

  /**
   * 保存游戏状态
   * @param actorId 角色ID
   * @param storyId 故事ID
   * @param state 游戏状态
   */
  async saveGameState(actorId: string, storyId: string, state: any): Promise<Chat4Result<void>> {
    try {
      const key = `gameState:${actorId}:${storyId}`
      await this.set(key, {
        ...state,
        actorId,
        storyId,
        lastUpdated: Date.now(),
        version: '1.0.0'
      })
      return ok(undefined)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      return err(new StorageError(`Failed to save game state: ${errorMessage}`, 'saveGameState'))
    }
  }

  /**
   * 设置缓存数据
   * @param key 键
   * @param data 数据
   * @param ttl 过期时间（毫秒）
   */
  async setCache<T>(key: string, data: T, ttl = this.config.storage.ttl): Promise<void> {
    const cacheKey = `cache:${key}`
    await this.set(cacheKey, {
      id: cacheKey,
      data,
      ttl,
      createdAt: Date.now()
    })
  }

  /**
   * 获取缓存数据
   * @param key 键
   * @returns 缓存数据或null
   */
  async getCache<T>(key: string): Promise<T | null> {
    const cacheKey = `cache:${key}`
    const cached = await this.get<{ data: T; ttl: number; createdAt: number }>(cacheKey)
    
    if (!cached) {
      return null
    }

    // 检查是否过期
    if (this.isCacheExpired(cached)) {
      await this.remove(cacheKey)
      return null
    }

    return cached.data
  }

  /**
   * 获取存储统计信息
   * @returns 统计信息
   */
  async getStorageStats(): Promise<{
    gameStates: number
    messages: number
    userConfigs: number
    cacheItems: number
    totalSize: number
  }> {
    if (!this.db) {
      return { gameStates: 0, messages: 0, userConfigs: 0, cacheItems: 0, totalSize: 0 }
    }

    try {
      const [gameStates, messages, userConfigs, cacheItems] = await Promise.all([
        this.db.count('gameState'),
        this.db.count('messages'),
        this.db.count('userConfig'),
        this.db.count('cache')
      ])

      // 估算总大小（简化计算）
      const totalSize = (gameStates + messages + userConfigs + cacheItems) * 1024 // 估算每条记录1KB

      return {
        gameStates,
        messages,
        userConfigs,
        cacheItems,
        totalSize
      }
    } catch (error) {
      console.error('[Storage] Failed to get stats:', error)
      return { gameStates: 0, messages: 0, userConfigs: 0, cacheItems: 0, totalSize: 0 }
    }
  }

  /**
   * 关闭数据库连接
   */
  async close(): Promise<void> {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }

    if (this.db) {
      this.db.close()
      this.db = null
      this.isInitialized = false
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('[Storage] Database connection closed')
    }
  }

  /**
   * 根据键确定存储类型
   * @param key 键
   * @returns 存储名称
   */
  private getStoreNameByKey(key: string): keyof Chat4DBSchema {
    if (key.startsWith('gameState:')) return 'gameState'
    if (key.startsWith('message:')) return 'messages'
    if (key.startsWith('userConfig:')) return 'userConfig'
    if (key.startsWith('cache:')) return 'cache'
    
    // 默认使用cache存储
    return 'cache'
  }

  /**
   * 准备存储数据
   * @param key 键
   * @param value 值
   * @param storeName 存储名称
   * @returns 格式化的存储数据
   */
  private prepareStoreData(key: string, value: any, storeName: keyof Chat4DBSchema): any {
    const baseData = {
      id: key,
      ...value
    }

    switch (storeName) {
      case 'cache':
        return {
          ...baseData,
          ttl: value.ttl || this.config.storage.ttl,
          createdAt: Date.now()
        }
      
      case 'messages':
        return {
          ...baseData,
          timestamp: value.timestamp || new Date().toISOString()
        }
      
      default:
        return baseData
    }
  }

  /**
   * 检查缓存是否过期
   * @param cached 缓存对象
   * @returns 是否过期
   */
  private isCacheExpired(cached: { ttl: number; createdAt: number }): boolean {
    return Date.now() - cached.createdAt > cached.ttl
  }

  /**
   * 启动清理任务
   */
  private startCleanupTask(): void {
    // 每小时清理一次过期数据
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredCache()
    }, 60 * 60 * 1000)
  }

  /**
   * 清理过期缓存
   */
  private async cleanupExpiredCache(): Promise<void> {
    if (!this.db) return

    try {
      const tx = this.db.transaction('cache', 'readwrite')
      const store = tx.objectStore('cache')
      const index = store.index('by-ttl')
      
      const expiredItems = await index.getAll()
      const now = Date.now()
      
      for (const item of expiredItems) {
        if (this.isCacheExpired(item)) {
          await store.delete(item.id)
        }
      }
      
      await tx.complete

      if (process.env.NODE_ENV === 'development') {
        console.log('[Storage] Expired cache cleanup completed')
      }
    } catch (error) {
      console.error('[Storage] Cache cleanup failed:', error)
    }
  }
}

/**
 * 内存存储实现（用于测试和开发）
 */
export class MemoryChat4Storage implements Storage {
  private data = new Map<string, any>()
  private timestamps = new Map<string, number>()

  async get<T>(key: string): Promise<T | null> {
    return this.data.get(key) || null
  }

  async set<T>(key: string, value: T): Promise<void> {
    this.data.set(key, value)
    this.timestamps.set(key, Date.now())
  }

  async remove(key: string): Promise<void> {
    this.data.delete(key)
    this.timestamps.delete(key)
  }

  async clear(): Promise<void> {
    this.data.clear()
    this.timestamps.clear()
  }

  // 获取所有键
  getAllKeys(): string[] {
    return Array.from(this.data.keys())
  }

  // 获取数据大小
  size(): number {
    return this.data.size
  }
}

/**
 * 存储工厂函数
 * @param config 配置对象
 * @param useMemory 是否使用内存存储
 * @returns 存储实例
 */
export function createChat4Storage(config: Chat4Config, useMemory = false): Storage {
  if (useMemory || typeof window === 'undefined') {
    return new MemoryChat4Storage()
  }
  
  return new IndexedDBChat4Storage(config)
}

/**
 * 存储工具函数
 */
export const StorageUtils = {
  /**
   * 生成存储键
   * @param prefix 前缀
   * @param parts 键组成部分
   * @returns 完整的键
   */
  generateKey(prefix: string, ...parts: string[]): string {
    return `${prefix}:${parts.join(':')}`
  },

  /**
   * 解析存储键
   * @param key 键
   * @returns 解析后的部分
   */
  parseKey(key: string): { prefix: string; parts: string[] } {
    const [prefix, ...parts] = key.split(':')
    return { prefix, parts }
  },

  /**
   * 压缩数据
   * @param data 原始数据
   * @returns 压缩后的数据
   */
  compressData(data: any): string {
    try {
      return JSON.stringify(data)
    } catch {
      return String(data)
    }
  },

  /**
   * 解压数据
   * @param compressed 压缩的数据
   * @returns 原始数据
   */
  decompressData<T>(compressed: string): T | null {
    try {
      return JSON.parse(compressed) as T
    } catch {
      return null
    }
  }
}