/**
 * Chat4客户端插件
 * 仅在客户端执行的Chat4初始化逻辑
 */

export default defineNuxtPlugin(() => {
  // 确保仅在客户端执行
  if (!process.client) return
  
  console.log('🎮 Chat4客户端插件开始初始化')
  
  // 设置视口高度CSS变量
  const setViewportHeight = () => {
    const vh = window.innerHeight * 0.01
    document.documentElement.style.setProperty('--vh', `${vh}px`)
    console.log('📐 视口高度已更新:', `${vh}px`)
  }
  
  // 设置Chat4专用CSS变量
  const setCSSVariables = () => {
    const root = document.documentElement
    
    // 检测设备类型
    const isMobile = window.innerWidth <= 768
    const isTablet = window.innerWidth > 768 && window.innerWidth <= 1024
    
    // 根据设备类型设置变量
    if (isMobile) {
      root.style.setProperty('--chat4-font-size-base', '14px')
      root.style.setProperty('--chat4-padding-base', '12px')
      root.style.setProperty('--chat4-button-size', '44px')
    } else if (isTablet) {
      root.style.setProperty('--chat4-font-size-base', '16px')
      root.style.setProperty('--chat4-padding-base', '16px')
      root.style.setProperty('--chat4-button-size', '48px')
    } else {
      root.style.setProperty('--chat4-font-size-base', '18px')
      root.style.setProperty('--chat4-padding-base', '20px')
      root.style.setProperty('--chat4-button-size', '52px')
    }
    
    console.log('🎨 Chat4 CSS变量已设置')
  }
  
  // 设置全局事件监听器
  const setupGlobalEventListeners = () => {
    // 监听Chat4场景变化事件
    window.addEventListener('chat4:scene:change', (event: any) => {
      console.log('🎬 全局场景变化事件:', event.detail)
      
      // 可以在这里添加全局场景切换逻辑
      // 例如：更新页面标题、发送统计数据等
      if (event.detail?.to) {
        document.title = `Chat4 - ${event.detail.to}`
      }
    })
    
    // 监听Chat4消息事件
    window.addEventListener('chat4:message:sent', (event: any) => {
      console.log('💬 全局消息发送事件:', event.detail)
      
      // 可以在这里添加消息发送的全局处理
      // 例如：统计、日志等
    })
    
    // 监听Chat4连接状态事件
    window.addEventListener('chat4:websocket:connected', (event: any) => {
      console.log('🔌 WebSocket连接建立:', event.detail)
    })
    
    window.addEventListener('chat4:websocket:disconnected', (event: any) => {
      console.log('🔌 WebSocket连接断开:', event.detail)
    })
    
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        console.log('👁️ Chat4页面隐藏')
        // 页面隐藏时可以暂停某些功能
        window.dispatchEvent(new CustomEvent('chat4:page:hidden'))
      } else {
        console.log('👁️ Chat4页面显示')
        // 页面显示时恢复功能
        window.dispatchEvent(new CustomEvent('chat4:page:visible'))
      }
    })
    
    console.log('👂 全局事件监听器已设置')
  }
  
  // 设置性能监控
  const setupPerformanceMonitoring = () => {
    // 监控内存使用
    if ('memory' in performance) {
      const checkMemory = () => {
        const memory = (performance as any).memory
        if (memory) {
          const memoryInfo = {
            used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
            total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
            limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
          }
          
          // 如果内存使用超过80%，发出警告
          if (memoryInfo.used / memoryInfo.limit > 0.8) {
            console.warn('⚠️ Chat4内存使用过高:', memoryInfo)
            window.dispatchEvent(new CustomEvent('chat4:memory:warning', {
              detail: memoryInfo
            }))
          }
        }
      }
      
      // 每30秒检查一次内存使用
      setInterval(checkMemory, 30000)
    }
    
    // 监控页面性能
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry) => {
        if (entry.entryType === 'navigation') {
          console.log('📊 Chat4页面加载性能:', {
            loadTime: entry.loadEventEnd - entry.loadEventStart,
            domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
            firstPaint: entry.responseEnd - entry.requestStart
          })
        }
      })
    })
    
    try {
      observer.observe({ entryTypes: ['navigation'] })
    } catch (error) {
      console.warn('⚠️ 性能监控初始化失败:', error)
    }
    
    console.log('📊 性能监控已启用')
  }
  
  // 设置错误处理
  const setupErrorHandling = () => {
    // 全局错误处理
    window.addEventListener('error', (event) => {
      if (event.filename && event.filename.includes('chat4')) {
        console.error('❌ Chat4全局错误:', {
          message: event.message,
          filename: event.filename,
          line: event.lineno,
          column: event.colno,
          error: event.error
        })
        
        // 发送错误事件
        window.dispatchEvent(new CustomEvent('chat4:error', {
          detail: {
            type: 'javascript',
            message: event.message,
            filename: event.filename,
            line: event.lineno,
            column: event.colno
          }
        }))
      }
    })
    
    // Promise错误处理
    window.addEventListener('unhandledrejection', (event) => {
      if (event.reason && event.reason.stack && event.reason.stack.includes('chat4')) {
        console.error('❌ Chat4 Promise错误:', event.reason)
        
        window.dispatchEvent(new CustomEvent('chat4:error', {
          detail: {
            type: 'promise',
            error: event.reason
          }
        }))
      }
    })
    
    console.log('🛡️ 错误处理已设置')
  }
  
  // 设置开发工具
  const setupDevTools = () => {
    if (process.dev) {
      // 开发环境下提供全局Chat4调试工具
      ;(window as any).__CHAT4_DEBUG__ = {
        version: '1.0.0',
        getStore: () => {
          const { $pinia } = useNuxtApp()
          return $pinia._s.get('chat4')
        },
        getCache: () => {
          const { getCacheStats } = useChat4DynamicImports()
          return getCacheStats()
        },
        clearCache: () => {
          const { clearCache } = useChat4DynamicImports()
          clearCache()
        },
        triggerEvent: (type: string, data: any) => {
          window.dispatchEvent(new CustomEvent(`chat4:${type}`, { detail: data }))
        }
      }
      
      console.log('🔧 Chat4开发工具已启用 - 使用 __CHAT4_DEBUG__ 访问')
    }
  }
  
  // 初始化序列
  const initialize = () => {
    console.log('🚀 开始初始化Chat4客户端环境')
    
    try {
      // 设置CSS变量
      setViewportHeight()
      setCSSVariables()
      
      // 设置事件监听器
      setupGlobalEventListeners()
      
      // 设置性能监控
      setupPerformanceMonitoring()
      
      // 设置错误处理
      setupErrorHandling()
      
      // 设置开发工具
      setupDevTools()
      
      // 监听窗口变化
      let resizeTimer: NodeJS.Timeout
      window.addEventListener('resize', () => {
        clearTimeout(resizeTimer)
        resizeTimer = setTimeout(() => {
          setViewportHeight()
          setCSSVariables()
        }, 100)
      })
      
      // 监听方向变化（移动设备）
      window.addEventListener('orientationchange', () => {
        setTimeout(() => {
          setViewportHeight()
          setCSSVariables()
        }, 100)
      })
      
      console.log('✅ Chat4客户端环境初始化完成')
      
      // 发送初始化完成事件
      window.dispatchEvent(new CustomEvent('chat4:client:ready', {
        detail: {
          timestamp: Date.now(),
          userAgent: navigator.userAgent,
          viewport: {
            width: window.innerWidth,
            height: window.innerHeight
          }
        }
      }))
      
    } catch (error) {
      console.error('❌ Chat4客户端初始化失败:', error)
    }
  }
  
  // 页面加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initialize)
  } else {
    initialize()
  }
  
  // 插件返回值 - 提供给其他地方使用的工具
  return {
    provide: {
      chat4Client: {
        setViewportHeight,
        setCSSVariables,
        version: '1.0.0'
      }
    }
  }
})