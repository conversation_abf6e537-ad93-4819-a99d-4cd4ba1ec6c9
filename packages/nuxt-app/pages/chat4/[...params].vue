<template>
  <div class="chat4-page">
    <ClientOnly fallback-tag="div" fallback="Loading Chat4...">
      <Chat4Container 
        :character-id="characterId"
        :story-id="storyId"
      />
      
      <template #fallback>
        <div class="chat4-loading">
          <div class="loading-spinner" />
          <p>Loading interactive chat...</p>
        </div>
      </template>
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
// 确保页面级CSR
definePageMeta({
  ssr: false,      // 页面级CSR
  mode: 'spa'      // SPA模式
})

// 路由参数解析
const route = useRoute()
const params = computed(() => (route.params.params as string[]) || [])
const storyId = computed(() => params.value[0])
const characterId = computed(() => params.value[1])

// SEO设置 (在CSR模式下仍然重要)
useSeoMeta({
  title: 'Interactive Chat Experience',
  description: 'Immersive AI chat with multiple scenarios',
  robots: 'noindex, nofollow' // Chat页面不需要SEO
})
</script>

<style scoped>
.chat4-page {
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  overflow: hidden;
}

.chat4-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>