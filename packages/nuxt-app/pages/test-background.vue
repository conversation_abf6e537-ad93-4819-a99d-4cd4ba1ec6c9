<template>
  <div class="test-background-page">
    <div class="demo-container">
      <EnhancedBackground
        ref="backgroundRef"
        :video-url="currentVideoUrl"
        :image-url="currentImageUrl"
        :animated-images="currentAnimatedImages"
        :transition-mode="transitionMode"
        :transition-duration="800"
        @resource-loaded="handleResourceLoaded"
        @transition-start="handleTransitionStart"
        @transition-end="handleTransitionEnd"
      />

      <!-- 控制面板 -->
      <div class="control-panel">
        <h2>EnhancedBackground 测试</h2>

        <div class="control-group">
          <h3>背景类型</h3>
          <button
            v-for="demo in demos"
            :key="demo.id"
            @click="switchDemo(demo)"
            :class="{ active: currentDemo?.id === demo.id }"
            class="demo-button"
          >
            {{ demo.name }}
          </button>
        </div>

        <div class="control-group">
          <h3>过渡效果</h3>
          <select v-model="transitionMode" class="transition-select">
            <option value="crossfade">交叉淡入淡出</option>
            <option value="fade">淡入淡出</option>
            <option value="slide">滑动</option>
            <option value="scale">缩放</option>
            <option value="zoom">放大</option>
            <option value="blur">模糊</option>
          </select>
        </div>

        <div class="status-info">
          <p><strong>状态:</strong> {{ status }}</p>
          <p><strong>当前类型:</strong> {{ currentType }}</p>
          <p><strong>过渡中:</strong> {{ isTransitioning ? '是' : '否' }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 显式导入EnhancedBackground组件
import EnhancedBackground from '~/components/common/EnhancedBackground.vue'

definePageMeta({
  layout: false,
  ssr: false,
})

interface Demo {
  id: string
  name: string
  type: 'video' | 'image' | 'animated-images'
  url: string | string[]
}

// 测试数据
const demos: Demo[] = [
  {
    id: 'video1',
    name: '视频背景1',
    type: 'video',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
  },
  {
    id: 'video2',
    name: '视频背景2',
    type: 'video',
    url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
  },
  {
    id: 'video3',
    name: '视频背景3',
    type: 'video',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
  },
  {
    id: 'image1',
    name: '静态图片1',
    type: 'image',
    url: 'https://picsum.photos/1920/1080?random=1',
  },
  {
    id: 'image2',
    name: '静态图片2',
    type: 'image',
    url: 'https://picsum.photos/1920/1080?random=2',
  },
  {
    id: 'animated1',
    name: '动画序列',
    type: 'animated-images',
    url: [
      'https://picsum.photos/1920/1080?random=10',
      'https://picsum.photos/1920/1080?random=11',
      'https://picsum.photos/1920/1080?random=12',
      'https://picsum.photos/1920/1080?random=13',
    ],
  },
]

// 响应式状态
const backgroundRef = ref()
const currentDemo = ref<Demo>()
const transitionMode = ref<
  'crossfade' | 'fade' | 'slide' | 'scale' | 'zoom' | 'blur'
>('crossfade')
const status = ref('准备就绪')
const isTransitioning = ref(false)

// 背景属性
const currentVideoUrl = ref('')
const currentImageUrl = ref('')
const currentAnimatedImages = ref<string[]>([])
const currentType = ref('无')

// 切换演示
const switchDemo = async (demo: Demo) => {
  if (currentDemo.value?.id === demo.id) return

  currentDemo.value = demo
  status.value = '切换中...'

  // 重置所有URL
  currentVideoUrl.value = ''
  currentImageUrl.value = ''
  currentAnimatedImages.value = []

  // 等待一帧确保重置生效
  await nextTick()

  // 设置新的背景
  switch (demo.type) {
    case 'video':
      currentVideoUrl.value = demo.url as string
      currentType.value = '视频'
      break
    case 'image':
      currentImageUrl.value = demo.url as string
      currentType.value = '图片'
      break
    case 'animated-images':
      currentAnimatedImages.value = demo.url as string[]
      currentType.value = '动画序列'
      break
  }
}

// 事件处理
const handleResourceLoaded = (type: string) => {
  status.value = `${type} 加载完成`
  console.log('✅ Resource loaded:', type)
}

const handleTransitionStart = () => {
  isTransitioning.value = true
  status.value = '过渡中...'
  console.log('🎬 Transition started')
}

const handleTransitionEnd = () => {
  isTransitioning.value = false
  status.value = '就绪'
  console.log('✅ Transition completed')
}

// 初始化
onMounted(() => {
  // 默认加载第一个演示
  setTimeout(() => {
    switchDemo(demos[0])
  }, 1000)
})
</script>

<style scoped>
.test-background-page {
  position: relative;
  width: 100vw;
  height: 100vh;
  background: #000;
  overflow: hidden;
}

.demo-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.control-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 320px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px;
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  z-index: 1000;
}

.control-panel h2 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
}

.control-panel h3 {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 500;
  color: #ccc;
}

.control-group {
  margin-bottom: 20px;
}

.demo-button {
  display: block;
  width: 100%;
  margin-bottom: 8px;
  padding: 10px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.demo-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.demo-button.active {
  background: rgba(59, 130, 246, 0.5);
  border-color: rgba(59, 130, 246, 0.8);
}

.transition-select {
  width: 100%;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: white;
  font-size: 14px;
}

.transition-select option {
  background: #333;
  color: white;
}

.status-info {
  font-size: 12px;
  line-height: 1.5;
  color: #ccc;
}

.status-info p {
  margin: 4px 0;
}

.status-info strong {
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .control-panel {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 20px;
    top: auto;
    width: auto;
    max-height: 50vh;
    overflow-y: auto;
  }
}
</style>
