/**
 * Chat4 现代化类型系统
 * 使用 Zod + Result 模式，零 if-else，完全类型安全
 */

import type { Result } from 'neverthrow'
import { z } from 'zod'

/**
 * Chat4场景ID枚举
 */
export enum Chat4SceneId {
  /** 直播场景 - 显示好友请求弹窗 */
  LIVING = 'Living',
  /** 聊天场景 */
  PHONE = 'Phone',
  /** 视频通话场景 */
  VIDEO_CALL = 'Video',
  /** 监控场景 */
  MONITOR = 'Monitor',
  /** 地图场景 - 选择约会地点 */
  MAP = 'Map',
  /** 见面场景 */
  MEETUP = 'Meetup',
  /** 游泳池约会场景 */
  MEETUP_POOL = 'MeetupPool',
  /** 咖啡店约会场景 */
  MEETUP_COFFEE = 'MeetupCoffee',
  /** 办公室约会场景 */
  MEETUP_OFFICE = 'MeetupOffice',
  /** 海边约会场景 */
  MEETUP_SEASIDE = 'MeetupSeaside',
  /** 舞蹈场景 */
  DANCING = 'Dancing',
  /** 演唱会场景 */
  CONCERT = 'Concert',
  /** 打赏场景 */
  TIP = 'Tip',
  /** 游戏场景 */
  GAME = 'Game',
  /** 设置场景 */
  SETTINGS = 'Settings',
  /** 朋友圈场景 */
  MOMENT = 'Moment',
  /** 日记场景 */
  DIARY = 'Diary',
}

/**
 * Chat4场景状态类型
 */
export type Chat4SceneState =
  | Chat4SceneId.LIVING
  | Chat4SceneId.PHONE
  | Chat4SceneId.VIDEO_CALL
  | Chat4SceneId.MONITOR
  | Chat4SceneId.MAP
  | Chat4SceneId.MEETUP
  | Chat4SceneId.MEETUP_POOL
  | Chat4SceneId.MEETUP_COFFEE
  | Chat4SceneId.MEETUP_OFFICE
  | Chat4SceneId.MEETUP_SEASIDE
  | Chat4SceneId.DANCING
  | Chat4SceneId.CONCERT
  | Chat4SceneId.TIP
  | Chat4SceneId.GAME
  | Chat4SceneId.SETTINGS
  | Chat4SceneId.MOMENT
  | Chat4SceneId.DIARY
  | string // 支持自定义场景ID（如 map-xxx 格式）

/**
 * Chat4 Store状态接口
 */
export interface Chat4State {
  // 场景状态
  currentScene: Chat4SceneState
  isTransitioning: boolean
  isLoading: boolean
  
  // 游戏数据
  currentActor: any | null
  currentStory: any | null
  
  // 消息状态
  messages: ChatMessage[]
  liveComments: LiveComment[]
  
  // UI状态
  showFriendRequestModal: boolean
  showGiftModal: boolean
  showFavorabilityDrawer: boolean
  showPaymentModal: boolean
  heartClickCount: number
  viewerCount: number
}

/**
 * 聊天消息接口
 */
export interface ChatMessage {
  id: string
  type: 'user' | 'actor' | 'system' | 'gift'
  content: string
  timestamp: number
  avatar?: string
  characterName?: string
  gift?: GiftItem
}

/**
 * 直播评论接口
 */
export interface LiveComment {
  id: string
  content: string
  username: string
  avatar?: string
  timestamp: number
  isHighlighted?: boolean
}

/**
 * 礼物道具接口
 */
export interface GiftItem {
  id: string
  title: string
  image: string
  price: number
  animation?: string
}

/**
 * 场景变化事件数据
 */
export interface SceneChangeEvent {
  from: Chat4SceneState
  to: Chat4SceneState
  timestamp: number
  data?: Record<string, any>
}

/**
 * Chat4场景工具类
 */
export class Chat4SceneUtils {
  /**
   * 判断是否为直播场景
   */
  static isLivingScene(sceneId: string | null): boolean {
    return sceneId === Chat4SceneId.LIVING
  }

  /**
   * 判断是否为聊天场景
   */
  static isPhoneScene(sceneId: string | null): boolean {
    return sceneId === Chat4SceneId.PHONE
  }

  /**
   * 判断是否为视频通话场景
   */
  static isVideoCallScene(sceneId: string | null): boolean {
    return sceneId === Chat4SceneId.VIDEO_CALL
  }

  /**
   * 判断是否为监控场景
   */
  static isMonitorScene(sceneId: string | null): boolean {
    return sceneId === Chat4SceneId.MONITOR
  }

  /**
   * 判断是否为地图场景
   */
  static isMapScene(sceneId: string | null): boolean {
    return sceneId === Chat4SceneId.MAP
  }

  /**
   * 判断是否为约会场景（包括所有约会子场景）
   */
  static isMeetupScene(sceneId: string | null): boolean {
    if (!sceneId) return false

    // 检查标准meetup场景
    if (
      sceneId === Chat4SceneId.MEETUP ||
      sceneId === Chat4SceneId.MEETUP_POOL ||
      sceneId === Chat4SceneId.MEETUP_COFFEE ||
      sceneId === Chat4SceneId.MEETUP_OFFICE ||
      sceneId === Chat4SceneId.MEETUP_SEASIDE
    ) {
      return true
    }

    // 检查自定义meetup子场景（map-xxx格式）
    if (typeof sceneId === 'string' && sceneId.startsWith('map-')) {
      return true
    }

    return false
  }

  /**
   * 判断是否为舞蹈场景
   */
  static isDancingScene(sceneId: string | null): boolean {
    return sceneId === Chat4SceneId.DANCING
  }

  /**
   * 判断是否为演唱会场景
   */
  static isConcertScene(sceneId: string | null): boolean {
    return sceneId === Chat4SceneId.CONCERT
  }

  /**
   * 判断是否为朋友圈场景
   */
  static isMomentScene(sceneId: string | null): boolean {
    return sceneId === Chat4SceneId.MOMENT
  }

  /**
   * 判断是否为日记场景
   */
  static isDiaryScene(sceneId: string | null): boolean {
    return sceneId === Chat4SceneId.DIARY
  }

  /**
   * 获取场景的显示名称
   */
  static getSceneDisplayName(sceneId: string | null): string {
    switch (sceneId) {
      case Chat4SceneId.LIVING:
        return '直播间'
      case Chat4SceneId.PHONE:
        return '聊天室'
      case Chat4SceneId.VIDEO_CALL:
        return '视频通话'
      case Chat4SceneId.MONITOR:
        return '监控'
      case Chat4SceneId.MAP:
        return '地图'
      case Chat4SceneId.MEETUP:
        return '见面'
      case Chat4SceneId.MEETUP_POOL:
        return '游泳池约会'
      case Chat4SceneId.MEETUP_COFFEE:
        return '咖啡店约会'
      case Chat4SceneId.MEETUP_OFFICE:
        return '办公室约会'
      case Chat4SceneId.MEETUP_SEASIDE:
        return '海边约会'
      case Chat4SceneId.DANCING:
        return '舞蹈'
      case Chat4SceneId.CONCERT:
        return '演唱会'
      case Chat4SceneId.MOMENT:
        return '朋友圈'
      case Chat4SceneId.DIARY:
        return '日记'
      case Chat4SceneId.TIP:
        return '打赏'
      case Chat4SceneId.GAME:
        return '游戏'
      case Chat4SceneId.SETTINGS:
        return '设置'
      default:
        return '未知场景'
    }
  }

  /**
   * 验证是否为有效的Chat4场景ID
   */
  static isValidChat4Scene(sceneId: string | null): sceneId is Chat4SceneState {
    if (!sceneId) return false

    // 检查标准场景ID
    if (Object.values(Chat4SceneId).includes(sceneId as Chat4SceneId)) {
      return true
    }

    // 检查自定义meetup子场景（map-xxx格式）
    if (typeof sceneId === 'string' && sceneId.startsWith('map-')) {
      return true
    }

    return false
  }
}

/**
 * WebSocket消息类型
 */
export interface WebSocketMessage {
  type: string
  data: any
  timestamp: number
}

/**
 * 好感度状态接口
 */
export interface FavorabilityState {
  currentLevel: number
  currentHeartValue: number
  maxHeartValue: number
  isUnlocked: boolean
}

/**
 * 支付相关接口
 */
export interface PaymentData {
  amount: number
  currency: string
  description: string
  sceneId?: string
}

// ==================== 现代化类型系统扩展 ====================

// 游戏状态枚举 (使用 Zod)
export const GameStateSchema = z.enum([
  'idle',
  'initializing', 
  'loading_history',
  'starting_game',
  'playing',
  'scene_transitioning',
  'paused',
  'ending',
  'error'
])

export type GameState = z.infer<typeof GameStateSchema>

// 场景类型 Schema
export const SceneTypeSchema = z.enum([
  'Living',
  'Phone', 
  'Video',
  'Monitor',
  'Map',
  'Meetup',
  'MeetupPool',
  'MeetupCoffee',
  'MeetupOffice',
  'MeetupSeaside',
  'Dancing',
  'Concert',
  'Moment',
  'Diary'
])

export type SceneType = z.infer<typeof SceneTypeSchema>

// 消息类型
export const MessageTypeSchema = z.enum([
  'text',
  'image', 
  'audio',
  'video',
  'emoji',
  'gift',
  'system'
])

export type MessageType = z.infer<typeof MessageTypeSchema>

// 事件类型
export const EventTypeSchema = z.enum([
  'message',
  'scene_change',
  'play_video',
  'show_image',
  'heart_value',
  'favorability_update',
  'payment_required',
  'game_end',
  'error'
])

export type EventType = z.infer<typeof EventTypeSchema>

// ==================== 现代化数据模型 ====================

// 用户信息 Schema
export const UserSchema = z.object({
  id: z.string(),
  name: z.string(),
  avatar: z.string().url().optional(),
  coins: z.number().min(0),
  level: z.number().min(1)
})

export type User = z.infer<typeof UserSchema>

// 角色信息 Schema
export const ActorSchema = z.object({
  id: z.string(),
  name: z.string(),
  avatar: z.string().url().optional(),
  preview: z.string().url().optional(),
  version: z.string()
})

export type Actor = z.infer<typeof ActorSchema>

// 故事信息 Schema
export const StorySchema = z.object({
  id: z.string(),
  title: z.string(),
  version: z.string(),
  actors: z.array(ActorSchema)
})

export type Story = z.infer<typeof StorySchema>

// 消息内容 Schema
export const MessageContentSchema = z.object({
  text: z.string().optional(),
  imageUrl: z.string().url().optional(),
  audioUrl: z.string().url().optional(),
  videoUrl: z.string().url().optional(),
  emoji: z.string().optional(),
  metadata: z.record(z.any()).optional()
})

export type MessageContent = z.infer<typeof MessageContentSchema>

// 现代化消息 Schema
export const ModernMessageSchema = z.object({
  id: z.string(),
  type: MessageTypeSchema,
  content: MessageContentSchema,
  sender: z.object({
    id: z.string(),
    name: z.string(),
    avatar: z.string().url().optional()
  }),
  timestamp: z.string().datetime(),
  scene: SceneTypeSchema.optional()
})

export type ModernMessage = z.infer<typeof ModernMessageSchema>

// 现代化好感度状态 Schema
export const ModernFavorabilityStateSchema = z.object({
  currentValue: z.number().min(0),
  maxValue: z.number().min(1),
  level: z.number().min(1),
  maxLevel: z.number().min(1),
  progress: z.number().min(0).max(100),
  nextThreshold: z.number().min(0)
})

export type ModernFavorabilityState = z.infer<typeof ModernFavorabilityStateSchema>

// 游戏事件 Schema
export const GameEventSchema = z.object({
  id: z.string(),
  type: EventTypeSchema,
  timestamp: z.string().datetime(),
  sceneType: SceneTypeSchema.optional(),
  data: z.record(z.any()),
  metadata: z.object({
    priority: z.enum(['low', 'medium', 'high', 'critical']).default('medium'),
    retryable: z.boolean().default(true),
    ttl: z.number().positive().optional()
  }).optional()
})

export type GameEvent = z.infer<typeof GameEventSchema>

// 场景状态 Schema
export const SceneStateSchema = z.object({
  type: SceneTypeSchema,
  isActive: z.boolean(),
  data: z.record(z.any()),
  lastUpdated: z.string().datetime()
})

export type SceneState = z.infer<typeof SceneStateSchema>

// ==================== 服务接口 ====================

// 初始化参数 Schema
export const InitParamsSchema = z.object({
  actorId: z.string(),
  storyId: z.string(),
  userId: z.string().optional(),
  config: z.object({
    autoStart: z.boolean().default(true),
    loadHistory: z.boolean().default(true),
    enableSound: z.boolean().default(true)
  }).optional()
})

export type InitParams = z.infer<typeof InitParamsSchema>

// API 响应 Schema
export const ApiResponseSchema = <T>(dataSchema: z.ZodSchema<T>) => z.object({
  success: z.boolean(),
  data: dataSchema.optional(),
  error: z.object({
    code: z.string(),
    message: z.string(),
    details: z.record(z.any()).optional()
  }).optional(),
  metadata: z.object({
    timestamp: z.string().datetime(),
    requestId: z.string(),
    version: z.string()
  }).optional()
})

export type ApiResponse<T> = z.infer<ReturnType<typeof ApiResponseSchema<T>>>

// 聊天历史响应 Schema
export const ChatHistoryResponseSchema = ApiResponseSchema(z.object({
  messages: z.array(ModernMessageSchema),
  favorability: ModernFavorabilityStateSchema,
  scene: SceneTypeSchema
}))

export type ChatHistoryResponse = z.infer<typeof ChatHistoryResponseSchema>

// 游戏会话响应 Schema
export const GameSessionResponseSchema = ApiResponseSchema(z.object({
  sessionId: z.string(),
  events: z.array(GameEventSchema),
  initialScene: SceneTypeSchema,
  favorability: ModernFavorabilityStateSchema
}))

export type GameSessionResponse = z.infer<typeof GameSessionResponseSchema>

// ==================== 错误类型 ====================

export class Chat4Error extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: Record<string, any>
  ) {
    super(message)
    this.name = 'Chat4Error'
  }
}

export class ValidationError extends Chat4Error {
  constructor(message: string, public issues: z.ZodIssue[]) {
    super(message, 'VALIDATION_ERROR', { issues })
    this.name = 'ValidationError'
  }
}

export class NetworkError extends Chat4Error {
  constructor(message: string, public status?: number) {
    super(message, 'NETWORK_ERROR', { status })
    this.name = 'NetworkError'
  }
}

export class GameStateError extends Chat4Error {
  constructor(message: string, public currentState: GameState, public targetState: GameState) {
    super(message, 'GAME_STATE_ERROR', { currentState, targetState })
    this.name = 'GameStateError'
  }
}

export class SceneError extends Chat4Error {
  constructor(message: string, public sceneType: SceneType) {
    super(message, 'SCENE_ERROR', { sceneType })
    this.name = 'SceneError'
  }
}

// ==================== 工具类型 ====================

// Result 类型别名
export type Chat4Result<T> = Result<T, Chat4Error>

// 事件处理器类型
export type EventHandler<T = any> = (event: T) => Promise<void> | void

// 状态转换器类型
export type StateTransition = {
  from: GameState
  to: GameState
  condition?: () => boolean | Promise<boolean>
  action?: () => Promise<void> | void
}

// 场景策略接口
export interface SceneStrategy {
  readonly sceneType: SceneType
  canHandle(event: GameEvent): boolean
  process(event: GameEvent): Promise<Chat4Result<void>>
  cleanup?(): Promise<void>
}

// API 适配器接口  
export interface Chat4ApiAdapter {
  getHistory(params: { storyId: string; actorId: string }): Promise<Chat4Result<ChatHistoryResponse>>
  startGame(params: InitParams): Promise<Chat4Result<GameSessionResponse>>
  sendMessage(message: Omit<ModernMessage, 'id' | 'timestamp'>): Promise<Chat4Result<void>>
  connectSSE(sessionId: string): Promise<Chat4Result<ReadableStream<GameEvent>>>
  disconnect(): Promise<void>
}

// 事件总线接口
export interface EventBus {
  subscribe<T>(event: string, handler: EventHandler<T>): () => void
  publish<T>(event: string, data: T): void
  clear(): void
}

// 存储接口
export interface Storage {
  get<T>(key: string): Promise<T | null>
  set<T>(key: string, value: T): Promise<void>
  remove(key: string): Promise<void>
  clear(): Promise<void>
}

// 状态机接口
export interface StateMachine {
  readonly currentState: GameState
  canTransition(to: GameState): boolean
  transition(to: GameState, data?: any): Promise<Chat4Result<void>>
  on(event: string, handler: EventHandler): () => void
}

// 游戏服务接口
export interface GameService {
  initialize(params: InitParams): Promise<Chat4Result<void>>
  sendMessage(content: MessageContent): Promise<Chat4Result<void>>
  changeScene(sceneType: SceneType): Promise<Chat4Result<void>>
  getFavorability(): ModernFavorabilityState
  getCurrentScene(): SceneType | null
  getGameState(): GameState
  continueGameFromHistory(): Promise<Chat4Result<void>>
  restartGameFromBeginning(): Promise<Chat4Result<void>>
  cleanup(): Promise<void>
}

// ==================== 配置类型 ====================

export const Chat4ConfigSchema = z.object({
  api: z.object({
    baseUrl: z.string().url(),
    timeout: z.number().positive().default(30000),
    retryAttempts: z.number().min(0).max(5).default(3)
  }),
  sse: z.object({
    reconnectInterval: z.number().positive().default(5000),
    maxReconnectAttempts: z.number().min(0).default(10)
  }),
  storage: z.object({
    prefix: z.string().default('chat4:'),
    ttl: z.number().positive().default(24 * 60 * 60 * 1000) // 24小时
  }),
  features: z.object({
    enableSound: z.boolean().default(true),
    enableHistory: z.boolean().default(true),
    enableOffline: z.boolean().default(false)
  })
})

export type Chat4Config = z.infer<typeof Chat4ConfigSchema>

// ==================== 导出工具函数 ====================

// 类型安全的验证函数
export function validateSchema<T>(schema: z.ZodSchema<T>, data: unknown): Chat4Result<T> {
  const result = schema.safeParse(data)
  
  if (result.success) {
    return { isOk: () => true, isErr: () => false, value: result.data } as Chat4Result<T>
  } else {
    return { 
      isOk: () => false, 
      isErr: () => true, 
      error: new ValidationError('数据验证失败', result.error.issues) 
    } as Chat4Result<T>
  }
}

// 创建类型安全的事件
export function createEvent<T extends EventType>(
  type: T,
  data: Record<string, any>,
  sceneType?: SceneType
): GameEvent {
  return {
    id: crypto.randomUUID(),
    type,
    timestamp: new Date().toISOString(),
    sceneType,
    data
  }
}

// 创建消息
export function createMessage(
  type: MessageType,
  content: MessageContent,
  sender: { id: string; name: string; avatar?: string },
  scene?: SceneType
): ModernMessage {
  return {
    id: crypto.randomUUID(),
    type,
    content,
    sender,
    timestamp: new Date().toISOString(),
    scene
  }
}

// Result 工具函数导出
export { Result, ok, err } from 'neverthrow'