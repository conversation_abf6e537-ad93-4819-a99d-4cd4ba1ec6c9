<template>
  <div class="enhanced-background" :class="{ 'is-transitioning': isTransitioning }">
    <!-- 双视频层用于无缝切换 -->
    <div class="video-layers">
      <video
        ref="video1Ref"
        class="background-layer video-layer"
        :class="{ 'is-blurred': isBlurred }"
        muted
        :loop="shouldLoop"
        autoplay
        playsinline
        webkit-playsinline
        x5-playsinline
        preload="metadata"
        style="opacity: 1"
        @loadeddata="handleVideoLoaded"
        @error="handleVideoError"
        @canplay="handleVideoCanPlay"
      />
      <video
        ref="video2Ref"
        class="background-layer video-layer"
        :class="{ 'is-blurred': isBlurred }"
        muted
        :loop="shouldLoop"
        autoplay
        playsinline
        webkit-playsinline
        x5-playsinline
        preload="metadata"
        style="opacity: 0"
        @loadeddata="handleVideoLoaded"
        @error="handleVideoError"
        @canplay="handleVideoCanPlay"
      />
    </div>

    <!-- 动画图片序列层 -->
    <div
      v-show="currentBackgroundType === 'animated-images'"
      ref="animatedContainerRef"
      class="background-layer animated-layer"
      :class="{ 'is-blurred': isBlurred }"
      @click="handleAnimatedImageClick"
    >
      <!-- 点击继续提示 -->
      <div
        v-if="showTapToContinue && !isLastAnimatedImage"
        class="tap-to-continue"
        :class="{ 'fade-out': isLastAnimatedImage }"
      >
        <div class="tap-icon">👆</div>
        <span>点击继续</span>
      </div>

      <!-- 动画图片容器 -->
      <div class="animated-images-wrapper">
        <img
          v-for="(imageUrl, index) in currentAnimatedImages"
          :key="`animated-${index}-${imageUrl}`"
          :src="imageUrl"
          :alt="`Animation frame ${index + 1}`"
          class="animated-image"
          :class="{
            'is-active': index === currentAnimatedIndex,
            'is-previous': index === previousAnimatedIndex,
            'is-entering': index === currentAnimatedIndex && animationState === 'entering'
          }"
          @load="handleImageLoaded"
          @error="handleImageError"
        />
      </div>
    </div>

    <!-- 静态图片背景层 -->
    <div
      v-show="currentBackgroundType === 'image'"
      ref="imageContainerRef"
      class="background-layer image-layer"
      :class="{ 'is-blurred': isBlurred }"
      :style="staticImageStyle"
    />

    <!-- 加载指示器 -->
    <Transition name="fade">
      <div v-if="isLoading" class="loading-overlay">
        <div class="loading-spinner">
          <div class="spinner-ring"></div>
        </div>
        <p class="loading-text">{{ loadingText }}</p>
      </div>
    </Transition>
    
    <!-- 预加载进度指示器（轻量级） -->
    <Transition name="fade">
      <div v-if="!isLoading && isTransitioning" class="preload-indicator">
        <div class="preload-dot"></div>
      </div>
    </Transition>

    <!-- 错误状态 -->
    <Transition name="fade">
      <div v-if="hasError" class="error-overlay">
        <Icon name="lucide:alert-circle" class="error-icon" />
        <p class="error-text">背景加载失败</p>
        <button class="retry-button" @click="retryLoad">
          <Icon name="lucide:refresh-cw" />
          重试
        </button>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
// 类型定义
export type BackgroundType = 'video' | 'image' | 'animated-images'
export type TransitionMode = 'fade' | 'slide' | 'zoom' | 'blur' | 'crossfade' | 'scale'

export interface BackgroundResource {
  type: BackgroundType
  url: string | string[]
  metadata?: {
    loop?: boolean
    duration?: number
    preload?: boolean
  }
}

interface Props {
  // 背景资源
  videoUrl?: string
  imageUrl?: string
  animatedImages?: string[]
  defaultImage?: string
  
  // 行为配置
  autoplay?: boolean
  loop?: boolean
  muted?: boolean
  blur?: boolean
  
  // 过渡配置
  transitionMode?: TransitionMode
  transitionDuration?: number
  
  // 预加载配置
  preload?: boolean
  lazyLoad?: boolean
  
  // 交互配置
  clickable?: boolean
  
  // 性能配置
  enableGpu?: boolean
  enableTransforms?: boolean
}

interface Emits {
  (e: 'resource-loading', loading: boolean): void
  (e: 'resource-loaded', type: BackgroundType): void
  (e: 'resource-error', error: Error): void
  (e: 'transition-start'): void
  (e: 'transition-end'): void
  (e: 'animated-sequence-complete'): void
  (e: 'click', event: MouseEvent): void
}

const props = withDefaults(defineProps<Props>(), {
  autoplay: true,
  loop: true,
  muted: true,
  blur: false,
  transitionMode: 'crossfade',
  transitionDuration: 800,
  preload: true,
  lazyLoad: false,
  clickable: true,
  enableGpu: true,
  enableTransforms: true
})

const emit = defineEmits<Emits>()

// 动态导入GSAP
let gsap: any = null
const loadGSAP = async () => {
  if (!gsap && process.client) {
    const { gsap: gsapLib } = await import('gsap')
    gsap = gsapLib
  }
  return gsap
}

// 响应式引用
const video1Ref = ref<HTMLVideoElement>()
const video2Ref = ref<HTMLVideoElement>()
const animatedContainerRef = ref<HTMLElement>()
const imageContainerRef = ref<HTMLElement>()

// 状态管理
const isLoading = ref(false)
const isTransitioning = ref(false)
const hasError = ref(false)
const loadingText = ref('加载中...')

// 当前背景状态
const currentBackgroundType = ref<BackgroundType>('image')
const currentVideoUrl = ref('')
const currentImageUrl = ref('')
const currentAnimatedImages = ref<string[]>([])

// 双视频切换状态
const activeVideoRef = ref<'video1' | 'video2'>('video1')
const videoLoadingStates = ref({
  video1: false,
  video2: false
})

// 动画序列状态
const currentAnimatedIndex = ref(0)
const previousAnimatedIndex = ref(-1)
const showTapToContinue = ref(true)
const animationState = ref<'idle' | 'entering' | 'active'>('idle')

// 计算属性
const isBlurred = computed(() => props.blur)
const shouldLoop = computed(() => props.loop)

const isLastAnimatedImage = computed(() => 
  currentAnimatedIndex.value >= currentAnimatedImages.value.length - 1
)

const staticImageStyle = computed(() => ({
  backgroundImage: currentImageUrl.value ? `url("${currentImageUrl.value}")` : 'none',
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  backgroundRepeat: 'no-repeat'
}))

// 获取当前活跃和非活跃的视频元素
const getActiveVideo = () => {
  return activeVideoRef.value === 'video1' ? video1Ref.value : video2Ref.value
}

const getInactiveVideo = () => {
  return activeVideoRef.value === 'video1' ? video2Ref.value : video1Ref.value
}

// 切换活跃视频
const switchActiveVideo = () => {
  activeVideoRef.value = activeVideoRef.value === 'video1' ? 'video2' : 'video1'
}

// 资源加载管理
const loadedResources = new Set<string>()
const preloadedResources = new Map<string, HTMLImageElement | HTMLVideoElement>()

// 视频预加载队列
const videoPreloadQueue = new Map<string, Promise<void>>()

// 预加载资源
const preloadResource = async (url: string, type: 'image' | 'video' = 'image') => {
  if (loadedResources.has(url)) return Promise.resolve()
  
  return new Promise<void>((resolve, reject) => {
    if (type === 'image') {
      const img = new Image()
      img.onload = () => {
        loadedResources.add(url)
        preloadedResources.set(url, img)
        resolve()
      }
      img.onerror = () => reject(new Error(`Failed to load image: ${url}`))
      img.src = url
    } else {
      const video = document.createElement('video')
      video.onloadeddata = () => {
        loadedResources.add(url)
        preloadedResources.set(url, video)
        resolve()
      }
      video.onerror = () => reject(new Error(`Failed to load video: ${url}`))
      video.src = url
      video.muted = true
      video.preload = 'metadata'
    }
  })
}

// GSAP过渡动画
const performTransition = async (
  fromEl: HTMLElement | null, 
  toEl: HTMLElement | null,
  mode: TransitionMode = props.transitionMode
) => {
  if (!fromEl || !toEl) return
  
  isTransitioning.value = true
  emit('transition-start')
  
  try {
    const gsapLib = await loadGSAP()
    if (gsapLib) {
      const tl = gsapLib.timeline()
      const duration = props.transitionDuration / 1000 // GSAP使用秒
      
      // 根据过渡模式执行不同动画
      switch (mode) {
        case 'crossfade':
          // 交叉渐变 - 最流畅的视频切换效果
          tl.to(fromEl, {
            opacity: 0,
            duration: duration * 0.6,
            ease: "power2.inOut"
          })
          .to(toEl, {
            opacity: 1,
            duration: duration * 0.6,
            ease: "power2.inOut"
          }, `-=${duration * 0.3}`) // 重叠0.3秒实现无缝过渡
          break
          
        case 'fade':
          tl.to(fromEl, {
            opacity: 0,
            duration: duration / 2,
            ease: "power2.inOut"
          })
          .to(toEl, {
            opacity: 1,
            duration: duration / 2,
            ease: "power2.inOut"
          })
          break
          
        case 'slide':
          tl.set(toEl, { x: "100%", opacity: 1 })
          .to(fromEl, {
            x: "-100%",
            duration: duration,
            ease: "power2.inOut"
          })
          .to(toEl, {
            x: "0%",
            duration: duration,
            ease: "power2.inOut"
          }, `-=${duration}`)
          break
          
        case 'scale':
          tl.set(toEl, { scale: 1.1, opacity: 0 })
          .to(fromEl, {
            scale: 0.9,
            opacity: 0,
            duration: duration / 2,
            ease: "power2.inOut"
          })
          .to(toEl, {
            scale: 1,
            opacity: 1,
            duration: duration / 2,
            ease: "power2.inOut"
          })
          break
          
        case 'zoom':
          tl.set(toEl, { scale: 1.05, opacity: 0 })
          .to(toEl, {
            scale: 1,
            opacity: 1,
            duration: duration,
            ease: "power2.out"
          })
          .to(fromEl, {
            opacity: 0,
            duration: duration * 0.6,
            ease: "power2.inOut"
          }, `-=${duration * 0.8}`)
          break
          
        case 'blur':
          tl.to(fromEl, {
            filter: "blur(10px)",
            opacity: 0,
            duration: duration / 2,
            ease: "power2.in"
          })
          .set(toEl, { filter: "blur(10px)", opacity: 0 })
          .to(toEl, {
            filter: "blur(0px)",
            opacity: 1,
            duration: duration / 2,
            ease: "power2.out"
          })
          break
      }
      
      await tl.play()
    } else {
      // 降级到CSS过渡
      toEl.style.transition = `opacity ${props.transitionDuration}ms cubic-bezier(0.4, 0, 0.2, 1)`
      fromEl.style.transition = `opacity ${props.transitionDuration}ms cubic-bezier(0.4, 0, 0.2, 1)`
      fromEl.style.opacity = '0'
      toEl.style.opacity = '1'
      await new Promise(resolve => setTimeout(resolve, props.transitionDuration))
    }
  } catch (error) {
    console.error('Transition animation error:', error)
    // 简单降级
    toEl.style.opacity = '1'
    fromEl.style.opacity = '0'
  } finally {
    isTransitioning.value = false
    emit('transition-end')
  }
}

// 预加载视频
const preloadVideo = async (url: string): Promise<void> => {
  if (videoPreloadQueue.has(url)) {
    return videoPreloadQueue.get(url)
  }
  
  const preloadPromise = new Promise<void>((resolve, reject) => {
    const video = document.createElement('video')
    video.muted = true
    video.preload = 'metadata'
    
    const cleanup = () => {
      video.removeEventListener('loadeddata', onLoaded)
      video.removeEventListener('error', onError)
    }
    
    const onLoaded = () => {
      cleanup()
      loadedResources.add(url)
      resolve()
    }
    
    const onError = () => {
      cleanup()
      reject(new Error(`Failed to preload video: ${url}`))
    }
    
    video.addEventListener('loadeddata', onLoaded)
    video.addEventListener('error', onError)
    video.src = url
  })
  
  videoPreloadQueue.set(url, preloadPromise)
  return preloadPromise
}

// 切换到视频背景
const switchToVideo = async (url: string) => {
  if (currentBackgroundType.value === 'video' && currentVideoUrl.value === url) {
    return // 避免重复切换
  }
  
  // 只在首次加载时显示加载状态
  const isFirstLoad = !currentBackgroundType.value
  
  if (isFirstLoad) {
    isLoading.value = true
    loadingText.value = '加载视频...'
  }
  
  try {
    const inactiveVideo = getInactiveVideo()
    const activeVideo = getActiveVideo()
    
    if (inactiveVideo && activeVideo) {
      // 立即设置新视频源并开始预加载
      inactiveVideo.src = url
      inactiveVideo.currentTime = 0
      
      // 确保新视频元素初始状态正确
      inactiveVideo.style.opacity = '0'
      inactiveVideo.style.transform = 'translate3d(0, 0, 0)'
      
      console.log('🎬 开始切换视频:', url)
      
      // 更激进的策略：立即开始过渡，同时加载视频
      let videoReady = false
      
      const loadPromise = new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          cleanup()
          // 超时不拒绝，继续执行过渡
          console.warn('⚠️ 视频加载超时，继续执行过渡')
          resolve()
        }, 3000) // 缩短到3秒，更激进
        
        const cleanup = () => {
          clearTimeout(timeout)
          inactiveVideo.removeEventListener('loadstart', onLoadStart)
          inactiveVideo.removeEventListener('loadedmetadata', onLoadedMetadata)
          inactiveVideo.removeEventListener('canplay', onCanPlay)
          inactiveVideo.removeEventListener('error', onError)
        }
        
        const onLoadStart = () => {
          console.log('📡 视频开始加载')
          // 视频开始加载就可以进行过渡了
          if (!videoReady) {
            videoReady = true
            setTimeout(() => resolve(), 100) // 100ms后开始过渡
          }
        }
        
        const onLoadedMetadata = () => {
          console.log('📊 视频元数据加载完成')
          if (!videoReady) {
            videoReady = true
            resolve()
          }
        }
        
        const onCanPlay = () => {
          console.log('▶️ 视频可以播放')
          cleanup()
          if (!videoReady) {
            videoReady = true
          }
          resolve()
        }
        
        const onError = () => {
          console.error('❌ 视频加载失败')
          cleanup()
          resolve() // 错误时也继续，不阻塞过渡
        }
        
        // 监听多个加载事件
        inactiveVideo.addEventListener('loadstart', onLoadStart)
        inactiveVideo.addEventListener('loadedmetadata', onLoadedMetadata) 
        inactiveVideo.addEventListener('canplay', onCanPlay)
        inactiveVideo.addEventListener('error', onError)
        
        // 立即开始加载
        inactiveVideo.load()
      })
      
      // 并行执行过渡动画
      if (currentVideoUrl.value && currentBackgroundType.value === 'video') {
        console.log('🎨 立即开始过渡动画（并行加载）')
        isTransitioning.value = true
        
        // 立即开始过渡动画，不等待视频完全准备好
        const transitionPromise = performTransition(activeVideo, inactiveVideo, 'crossfade')
        
        // 等待视频加载（至少部分数据）
        await loadPromise
        
        // 尝试播放新视频
        try {
          await inactiveVideo.play()
          console.log('🎵 新视频开始播放')
        } catch (playError) {
          console.warn('⚠️ 视频播放失败，继续:', playError)
        }
        
        // 等待过渡完成
        await transitionPromise
        
        // 过渡完成后暂停旧视频
        activeVideo.pause()
        activeVideo.currentTime = 0
        console.log('⏸️ 旧视频已暂停')
      } else {
        // 首次加载
        await loadPromise
        
        try {
          await inactiveVideo.play()
        } catch (playError) {
          console.warn('⚠️ 首次播放失败:', playError)
        }
        
        inactiveVideo.style.opacity = '1'
        activeVideo.style.opacity = '0'
        console.log('🆕 首次加载完成')
      }
      
      // 切换引用
      switchActiveVideo()
    }
    
    currentVideoUrl.value = url
    currentBackgroundType.value = 'video'
    emit('resource-loaded', 'video')
    console.log('✅ 视频切换完成')
    
  } catch (error) {
    hasError.value = true
    emit('resource-error', error as Error)
    console.error('❌ 视频切换错误:', error)
  } finally {
    if (isFirstLoad) {
      isLoading.value = false
    }
    isTransitioning.value = false
  }
}

// 切换到指定背景
const switchToBackground = async (
  url: string | string[], 
  type: BackgroundType
) => {
  hasError.value = false
  
  // 只在首次加载或类型变化时显示加载状态
  const shouldShowLoading = !currentBackgroundType.value || 
    (currentBackgroundType.value !== type && 
     !(currentBackgroundType.value === 'video' && type === 'image') &&
     !(currentBackgroundType.value === 'image' && type === 'video'))
  
  if (shouldShowLoading) {
    isLoading.value = true
    emit('resource-loading', true)
  }
  
  try {
    if (type === 'video' && typeof url === 'string') {
      await switchToVideo(url)
      
    } else if (type === 'image' && typeof url === 'string') {
      // 智能预加载：快速切换时在后台加载
      if (shouldShowLoading) {
        loadingText.value = '加载图片...'
      }
      
      // 预加载图片（后台进行）
      if (props.preload) {
        await preloadResource(url, 'image')
      }
      
      // 执行平滑过渡
      if (currentBackgroundType.value === 'video') {
        const activeVideo = getActiveVideo()
        if (activeVideo && imageContainerRef.value) {
          // 先设置图片URL，再执行过渡
          currentImageUrl.value = url
          await nextTick()
          await performTransition(activeVideo, imageContainerRef.value)
          activeVideo.pause()
        }
      } else if (currentBackgroundType.value === 'image') {
        // 图片到图片的切换
        const oldImageUrl = currentImageUrl.value
        currentImageUrl.value = url
        // 可以在这里添加图片淡入淡出效果
      }
      
      currentImageUrl.value = url
      currentBackgroundType.value = 'image'
      emit('resource-loaded', 'image')
      
    } else if (type === 'animated-images' && Array.isArray(url)) {
      if (shouldShowLoading) {
        loadingText.value = '加载动画序列...'
      }
      
      // 后台预加载动画图片
      if (props.preload) {
        await Promise.all(url.map(imageUrl => preloadResource(imageUrl, 'image')))
      }
      
      currentAnimatedImages.value = url
      currentAnimatedIndex.value = 0
      previousAnimatedIndex.value = -1
      showTapToContinue.value = true
      currentBackgroundType.value = 'animated-images'
      emit('resource-loaded', 'animated-images')
    }
    
  } catch (error) {
    hasError.value = true
    emit('resource-error', error as Error)
    console.error('Background loading error:', error)
  } finally {
    if (shouldShowLoading) {
      isLoading.value = false
      emit('resource-loading', false)
    }
  }
}

// 处理动画图片点击
const handleAnimatedImageClick = async () => {
  if (!props.clickable) return
  
  if (isLastAnimatedImage.value) {
    // 动画序列完成，切换到最后一帧作为静态背景
    const lastImage = currentAnimatedImages.value[currentAnimatedImages.value.length - 1]
    if (lastImage) {
      await switchToBackground(lastImage, 'image')
    }
    emit('animated-sequence-complete')
    return
  }
  
  // 切换到下一帧
  previousAnimatedIndex.value = currentAnimatedIndex.value
  currentAnimatedIndex.value++
  animationState.value = 'entering'
  
  // 使用GSAP执行帧切换动画
  try {
    const gsapLib = await loadGSAP()
    if (gsapLib) {
      const currentImage = animatedContainerRef.value?.querySelector(
        `.animated-image:nth-child(${currentAnimatedIndex.value + 1})`
      ) as HTMLElement
      
      if (currentImage) {
        await gsapLib.fromTo(currentImage, 
          {
            opacity: 0,
            scale: 1.05
          },
          {
            opacity: 1,
            scale: 1,
            duration: 0.5,
            ease: "power2.out"
          }
        )
      }
    }
  } catch (error) {
    console.warn('GSAP not available, using CSS fallback')
  }
  
  animationState.value = 'active'
  
  if (isLastAnimatedImage.value) {
    showTapToContinue.value = false
  }
}

// 事件处理
const handleVideoLoaded = () => {
  console.log('✅ Video loaded successfully')
}

const handleVideoCanPlay = () => {
  console.log('✅ Video can play')
}

const handleVideoError = (event: Event) => {
  console.error('❌ Video loading failed:', event)
  hasError.value = true
}

const handleImageLoaded = () => {
  console.log('✅ Image loaded successfully')
}

const handleImageError = () => {
  console.error('❌ Image loading failed')
}

const retryLoad = () => {
  hasError.value = false
  // 重新加载当前资源
  if (props.videoUrl) {
    switchToBackground(props.videoUrl, 'video')
  } else if (props.animatedImages?.length) {
    switchToBackground(props.animatedImages, 'animated-images')
  } else if (props.imageUrl) {
    switchToBackground(props.imageUrl, 'image')
  } else if (props.defaultImage) {
    switchToBackground(props.defaultImage, 'image')
  }
}

// 监听属性变化
watch(
  [() => props.videoUrl, () => props.imageUrl, () => props.animatedImages],
  async ([newVideo, newImage, newAnimatedImages]) => {
    // 防抖处理
    await nextTick()
    
    if (newVideo) {
      await switchToBackground(newVideo, 'video')
    } else if (newAnimatedImages?.length) {
      await switchToBackground(newAnimatedImages, 'animated-images')
    } else if (newImage) {
      await switchToBackground(newImage, 'image')
    } else if (props.defaultImage) {
      await switchToBackground(props.defaultImage, 'image')
    }
  },
  { immediate: true }
)

// 清理
onBeforeUnmount(() => {
  // 清理预加载的资源
  preloadedResources.clear()
  loadedResources.clear()
  videoPreloadQueue.clear()
  
  // 暂停所有视频
  if (video1Ref.value) {
    video1Ref.value.pause()
    video1Ref.value.src = ''
  }
  if (video2Ref.value) {
    video2Ref.value.pause()
    video2Ref.value.src = ''
  }
})
</script>

<style scoped>
.enhanced-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

/* 背景层基础样式 */
.background-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  will-change: transform, opacity;
  transform: translate3d(0, 0, 0); /* GPU加速 */
}

.background-layer.is-blurred {
  filter: blur(8px);
  transition: filter 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 视频层容器 */
.video-layers {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* 视频层 */
.video-layer {
  z-index: 1;
  width: 100%;
  height: 100%;
  object-fit: cover;
  background: black;
  
  /* 移动端优化 */
  -webkit-playsinline: true;
  playsinline: true;
  
  /* 性能优化 */
  will-change: opacity, transform;
  backface-visibility: hidden;
  
  /* 防止视频控制条显示 */
  &::-webkit-media-controls {
    display: none !important;
  }
  
  &::-webkit-media-controls-panel {
    display: none !important;
  }
  
  &::-webkit-media-controls-play-button {
    display: none !important;
  }
  
  &::-webkit-media-controls-start-playback-button {
    display: none !important;
  }
}

/* 图片层 */
.image-layer {
  z-index: 1;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* 动画序列层 */
.animated-layer {
  z-index: 1;
  cursor: pointer;
}

.animated-images-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.animated-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}

.animated-image.is-active {
  opacity: 1;
  z-index: 2;
}

.animated-image.is-entering {
  animation: imageEnter 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes imageEnter {
  0% {
    opacity: 0;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 点击继续提示 */
.tap-to-continue {
  position: absolute;
  top: 60%;
  right: 20%;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  animation: pulse 2s ease-in-out infinite;
  pointer-events: none;
}

.tap-to-continue.fade-out {
  animation: fadeOut 0.5s ease-out forwards;
}

.tap-icon {
  font-size: 24px;
  animation: bounce 1s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-4px); }
}

@keyframes fadeOut {
  to { opacity: 0; }
}

/* 缓冲层 */
.buffer-layer {
  z-index: 1.5;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* 加载覆盖层 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  color: white;
}

/* 预加载指示器（轻量级） */
.preload-indicator {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 5;
  pointer-events: none;
}

.preload-dot {
  width: 8px;
  height: 8px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: pulse 1.5s ease-in-out infinite;
}

.loading-spinner {
  margin-bottom: 16px;
}

.spinner-ring {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 14px;
  opacity: 0.9;
}

/* 错误覆盖层 */
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  color: white;
  text-align: center;
  padding: 20px;
}

.error-icon {
  width: 48px;
  height: 48px;
  color: #ef4444;
  margin-bottom: 16px;
}

.error-text {
  font-size: 16px;
  margin-bottom: 20px;
  opacity: 0.9;
}

.retry-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 12px 20px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

/* 过渡动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* 动画关键帧 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 性能优化 */
.enhanced-background * {
  will-change: auto;
}

.enhanced-background.is-transitioning * {
  will-change: transform, opacity;
}

/* GPU加速 */
.background-layer {
  transform: translate3d(0, 0, 0);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tap-to-continue {
    top: 50%;
    right: 15%;
    font-size: 12px;
    padding: 8px 12px;
  }
  
  .tap-icon {
    font-size: 20px;
  }
  
  .loading-text {
    font-size: 12px;
  }
  
  .error-text {
    font-size: 14px;
  }
}
</style>