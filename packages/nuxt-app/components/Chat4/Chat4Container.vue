<template>
  <div v-if="!isHydrating" class="chat4-container">
    <!-- 场景路由 -->
    <LiveStreamContainer 
      v-if="isLivingScene" 
      :character-id="characterId"
      :story-id="storyId"
      @scene-change="handleSceneChange"
    />
    
    <ChatRoomContainer 
      v-else-if="isPhoneScene" 
      :character-id="characterId"
      :story-id="storyId"
      @scene-change="handleSceneChange"
    />
    
    <div v-else-if="isVideoCallScene" class="video-call-scene">
      <div class="scene-placeholder">
        <h2>视频通话场景</h2>
        <p>当前场景: {{ currentScene }}</p>
        <button @click="handleSceneChange('Living')">返回直播</button>
      </div>
    </div>
    
    <div v-else-if="isMonitorScene" class="monitor-scene">
      <div class="scene-placeholder">
        <h2>监控场景</h2>
        <p>当前场景: {{ currentScene }}</p>
        <button @click="handleSceneChange('Living')">返回直播</button>
      </div>
    </div>
    
    <div v-else-if="isMapScene" class="map-scene">
      <div class="scene-placeholder">
        <h2>地图场景</h2>
        <p>当前场景: {{ currentScene }}</p>
        <button @click="handleSceneChange('Living')">返回直播</button>
      </div>
    </div>
    
    <div v-else-if="isMeetupScene" class="meetup-scene">
      <div class="scene-placeholder">
        <h2>约会场景</h2>
        <p>当前场景: {{ currentScene }}</p>
        <button @click="handleSceneChange('Living')">返回直播</button>
      </div>
    </div>
    
    <div v-else-if="isDancingScene" class="dancing-scene">
      <div class="scene-placeholder">
        <h2>舞蹈场景</h2>
        <p>当前场景: {{ currentScene }}</p>
        <button @click="handleSceneChange('Living')">返回直播</button>
      </div>
    </div>
    
    <div v-else-if="isConcertScene" class="concert-scene">
      <div class="scene-placeholder">
        <h2>演唱会场景</h2>
        <p>当前场景: {{ currentScene }}</p>
        <button @click="handleSceneChange('Living')">返回直播</button>
      </div>
    </div>
    
    <div v-else-if="isMomentScene" class="moment-scene">
      <div class="scene-placeholder">
        <h2>朋友圈场景</h2>
        <p>当前场景: {{ currentScene }}</p>
        <button @click="handleSceneChange('Living')">返回直播</button>
      </div>
    </div>
    
    <div v-else-if="isDiaryScene" class="diary-scene">
      <div class="scene-placeholder">
        <h2>日记场景</h2>
        <p>当前场景: {{ currentScene }}</p>
        <button @click="handleSceneChange('Living')">返回直播</button>
      </div>
    </div>
    
    <!-- 默认场景 -->
    <div v-else class="default-scene">
      <div class="scene-placeholder">
        <h2>未知场景</h2>
        <p>当前场景: {{ currentScene || 'null' }}</p>
        <button @click="handleSceneChange('Living')">返回直播</button>
      </div>
    </div>
    
    <!-- 全局模态框占位符 (后续实现) -->
    <!-- 
    <GiftDrawer v-model:visible="showGiftDrawer" />
    <FavorabilityDrawer v-model:visible="showFavorabilityDrawer" />
    <PaymentConfirmModal v-model:visible="showPaymentModal" />
    -->
  </div>
  
  <!-- 服务端渲染占位符 -->
  <div v-else class="chat4-placeholder">
    <div class="loading-message">Preparing interactive experience...</div>
  </div>
</template>

<script setup lang="ts">
import type { Chat4SceneState } from '~/types/chat4'
import { Chat4SceneUtils } from '~/types/chat4'
import { useChat4 } from '~/composables/useChat4'

// 显式导入场景组件
import LiveStreamContainer from '~/components/Chat4/scenes/LiveStreamContainer.vue'
import ChatRoomContainer from '~/components/Chat4/scenes/ChatRoomContainer.vue'

interface Props {
  characterId?: string
  storyId?: string
  gameMode?: 'restart' | 'continue' // 新增游戏模式参数
}

const props = defineProps<Props>()

// 确保组件仅在客户端渲染
const isHydrating = ref(true)

onMounted(() => {
  nextTick(() => {
    isHydrating.value = false
    initChat4()
  })
})

// 使用现代化Chat4架构
const {
  state,
  isReady,
  initialize,
  changeScene,
  currentSceneName
} = useChat4({
  config: {
    api: {
      baseUrl: process.env.NUXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api/v1'
    },
    features: {
      enableSound: true,
      enableHistory: true
    }
  }
})

// 从新架构中获取当前场景
const currentScene = computed(() => state.currentScene)

// 场景判断计算属性
const isLivingScene = computed(() => Chat4SceneUtils.isLivingScene(currentScene.value))
const isPhoneScene = computed(() => Chat4SceneUtils.isPhoneScene(currentScene.value))
const isVideoCallScene = computed(() => Chat4SceneUtils.isVideoCallScene(currentScene.value))
const isMonitorScene = computed(() => Chat4SceneUtils.isMonitorScene(currentScene.value))
const isMapScene = computed(() => Chat4SceneUtils.isMapScene(currentScene.value))
const isMeetupScene = computed(() => Chat4SceneUtils.isMeetupScene(currentScene.value))
const isDancingScene = computed(() => Chat4SceneUtils.isDancingScene(currentScene.value))
const isConcertScene = computed(() => Chat4SceneUtils.isConcertScene(currentScene.value))
const isMomentScene = computed(() => Chat4SceneUtils.isMomentScene(currentScene.value))
const isDiaryScene = computed(() => Chat4SceneUtils.isDiaryScene(currentScene.value))

// 初始化Chat4 - 使用新架构
const initChat4 = async () => {
  // 确定游戏模式：restart 或 continue
  const gameMode = props.gameMode || 'continue' // 默认为continue模式
  const shouldLoadHistory = gameMode === 'continue'
  
  console.log('🎮 Chat4Container: 开始初始化现代化架构', {
    characterId: props.characterId,
    storyId: props.storyId,
    gameMode,
    shouldLoadHistory
  })
  
  if (props.characterId && props.storyId) {
    // 根据游戏模式决定是否加载历史记录
    const result = await initialize({
      actorId: props.characterId,
      storyId: props.storyId,
      config: {
        autoStart: true,
        loadHistory: shouldLoadHistory, // 动态决定是否加载历史
        enableSound: true
      }
    })
    
    if (result.isErr()) {
      console.error('❌ Chat4Container: 初始化失败:', result.error.message)
    } else {
      console.log(`✅ Chat4Container: 现代化架构初始化成功 (${gameMode}模式)`)
    }
  } else {
    console.warn('⚠️ Chat4Container: 缺少必要参数，使用默认配置')
  }
}

// 场景切换处理 - 使用新架构
const handleSceneChange = async (newScene: Chat4SceneState) => {
  console.log('🔄 场景切换:', {
    from: currentScene.value,
    to: newScene
  })
  
  const result = await changeScene(newScene as any)
  
  if (result.isErr()) {
    console.error('❌ 场景切换失败:', result.error.message)
  }
}

// 监听场景变化
watch(currentScene, (newScene, oldScene) => {
  console.log('👀 场景变化监听:', {
    from: oldScene,
    to: newScene,
    sceneName: Chat4SceneUtils.getSceneDisplayName(newScene)
  })
}, { immediate: true })

// 监听初始化状态
watch(() => state.isInitialized, (initialized) => {
  if (initialized) {
    console.log('🎉 Chat4现代化架构已就绪:', {
      gameState: state.gameState,
      currentScene: state.currentScene,
      sessionId: state.sessionId,
      isConnected: state.isConnected
    })
  }
})
</script>

<style scoped>
.chat4-container {
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  position: relative;
  overflow: hidden;
  background: var(--chat4-bg-gradient);
}

.chat4-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: var(--chat4-loading-gradient);
  color: white;
}

/* 场景占位符样式 */
.scene-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: white;
  text-align: center;
  padding: 2rem;
  
  h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--chat4-secondary-color);
  }
  
  p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.8;
  }
  
  button {
    background: var(--chat4-primary-color);
    color: white;
    border: 2px solid var(--chat4-secondary-color);
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: var(--chat4-secondary-color);
      color: var(--chat4-primary-color);
      transform: scale(1.05);
    }
    
    &:active {
      transform: scale(0.95);
    }
  }
}

/* 各场景基础样式 */
.live-scene,
.chat-scene,
.video-call-scene,
.monitor-scene,
.map-scene,
.meetup-scene,
.dancing-scene,
.concert-scene,
.moment-scene,
.diary-scene,
.default-scene {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>