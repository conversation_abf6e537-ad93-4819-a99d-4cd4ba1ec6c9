<template>
  <div class="stream-overlay" :class="{ 'hidden': !visible, 'minimized': isMinimized }">
    <!-- 顶部信息栏 -->
    <div class="top-bar" :class="{ 'transparent': isTransparent }">
      <div class="stream-info">
        <div class="stream-title">{{ streamInfo.title }}</div>
        <div class="viewer-count">
          <Icon name="lucide:eye" />
          <span>{{ formatNumber(streamInfo.viewerCount) }}</span>
        </div>
      </div>
      
      <div class="stream-actions">
        <button class="action-btn" @click="toggleFullscreen" :title="isFullscreen ? '退出全屏' : '全屏'">
          <Icon :name="isFullscreen ? 'lucide:minimize' : 'lucide:maximize'" />
        </button>
        
        <button class="action-btn" @click="toggleOverlay" :title="visible ? '隐藏覆盖层' : '显示覆盖层'">
          <Icon :name="visible ? 'lucide:eye-off' : 'lucide:eye'" />
        </button>
        
        <button class="action-btn" @click="toggleMinimize" :title="isMinimized ? '展开' : '最小化'">
          <Icon :name="isMinimized ? 'lucide:chevron-down' : 'lucide:chevron-up'" />
        </button>
      </div>
    </div>
    
    <!-- 直播状态指示器 -->
    <div class="stream-status" v-if="!isMinimized">
      <div class="status-indicator" :class="`status-${streamInfo.status}`">
        <div class="status-dot"></div>
        <span>{{ getStatusText(streamInfo.status) }}</span>
      </div>
      
      <div class="stream-duration" v-if="streamInfo.status === 'live'">
        <Icon name="lucide:clock" />
        <span>{{ formatDuration(streamDuration) }}</span>
      </div>
    </div>
    
    <!-- 实时数据展示 -->
    <div class="stream-metrics" v-if="showMetrics && !isMinimized">
      <div class="metric-item">
        <Icon name="lucide:users" />
        <span class="metric-value">{{ formatNumber(streamInfo.viewerCount) }}</span>
        <span class="metric-label">观众</span>
      </div>
      
      <div class="metric-item">
        <Icon name="lucide:heart" />
        <span class="metric-value">{{ formatNumber(metrics.likes) }}</span>
        <span class="metric-label">点赞</span>
      </div>
      
      <div class="metric-item">
        <Icon name="lucide:gift" />
        <span class="metric-value">{{ formatNumber(metrics.gifts) }}</span>
        <span class="metric-label">礼物</span>
      </div>
      
      <div class="metric-item">
        <Icon name="lucide:message-circle" />
        <span class="metric-value">{{ formatNumber(metrics.comments) }}</span>
        <span class="metric-label">评论</span>
      </div>
    </div>
    
    <!-- 快捷操作面板 -->
    <div class="quick-actions" v-if="showQuickActions && !isMinimized">
      <button class="quick-btn" @click="toggleMute" :class="{ 'active': isMuted }">
        <Icon :name="isMuted ? 'lucide:volume-x' : 'lucide:volume-2'" />
        <span>{{ isMuted ? '取消静音' : '静音' }}</span>
      </button>
      
      <button class="quick-btn" @click="captureScreenshot">
        <Icon name="lucide:camera" />
        <span>截图</span>
      </button>
      
      <button class="quick-btn" @click="shareStream">
        <Icon name="lucide:share" />
        <span>分享</span>
      </button>
      
      <button class="quick-btn" @click="openSettings">
        <Icon name="lucide:settings" />
        <span>设置</span>
      </button>
    </div>
    
    <!-- 连接质量指示器 -->
    <div class="connection-quality" v-if="showConnectionQuality && !isMinimized">
      <div class="quality-indicator" :class="`quality-${connectionQuality.level}`">
        <div class="quality-bars">
          <div class="bar" :class="{ 'active': connectionQuality.level >= 1 }"></div>
          <div class="bar" :class="{ 'active': connectionQuality.level >= 2 }"></div>
          <div class="bar" :class="{ 'active': connectionQuality.level >= 3 }"></div>
          <div class="bar" :class="{ 'active': connectionQuality.level >= 4 }"></div>
        </div>
        <span class="quality-text">{{ getQualityText(connectionQuality.level) }}</span>
      </div>
      
      <div class="quality-details" v-if="showQualityDetails">
        <div class="detail-item">
          <span>延迟: {{ connectionQuality.latency }}ms</span>
        </div>
        <div class="detail-item">
          <span>带宽: {{ connectionQuality.bandwidth }}</span>
        </div>
        <div class="detail-item">
          <span>丢包率: {{ connectionQuality.packetLoss }}%</span>
        </div>
      </div>
    </div>
    
    <!-- 弹幕密度控制 -->
    <div class="danmaku-controls" v-if="showDanmakuControls && !isMinimized">
      <div class="control-label">
        <Icon name="lucide:message-square" />
        <span>弹幕密度</span>
      </div>
      
      <div class="density-slider">
        <input
          type="range"
          v-model="danmakuDensity"
          min="0"
          max="100"
          step="25"
          @input="updateDanmakuDensity"
        />
        <div class="density-labels">
          <span :class="{ 'active': danmakuDensity === 0 }">关</span>
          <span :class="{ 'active': danmakuDensity === 25 }">低</span>
          <span :class="{ 'active': danmakuDensity === 50 }">中</span>
          <span :class="{ 'active': danmakuDensity === 75 }">高</span>
          <span :class="{ 'active': danmakuDensity === 100 }">最高</span>
        </div>
      </div>
    </div>
    
    <!-- 音量控制 -->
    <div class="volume-control" v-if="showVolumeControl && !isMinimized">
      <Icon name="lucide:volume-2" />
      <input
        type="range"
        v-model="volume"
        min="0"
        max="100"
        step="5"
        class="volume-slider"
        @input="updateVolume"
      />
      <span class="volume-display">{{ volume }}%</span>
    </div>
    
    <!-- 通知/警告区域 -->
    <div class="notifications" v-if="notifications.length > 0 && !isMinimized">
      <Transition name="notification" appear>
        <div
          v-for="notification in notifications"
          :key="notification.id"
          class="notification-item"
          :class="`notification-${notification.type}`"
        >
          <Icon :name="getNotificationIcon(notification.type)" />
          <span>{{ notification.message }}</span>
          <button class="close-notification" @click="dismissNotification(notification.id)">
            <Icon name="lucide:x" />
          </button>
        </div>
      </Transition>
    </div>
    
    <!-- 底部工具栏 -->
    <div class="bottom-toolbar" v-if="showBottomToolbar && !isMinimized">
      <div class="toolbar-left">
        <button class="tool-btn" @click="toggleChat" :class="{ 'active': showChat }">
          <Icon name="lucide:message-circle" />
          <span>聊天</span>
        </button>
        
        <button class="tool-btn" @click="toggleDanmaku" :class="{ 'active': showDanmaku }">
          <Icon name="lucide:type" />
          <span>弹幕</span>
        </button>
        
        <button class="tool-btn" @click="toggleGifts" :class="{ 'active': showGifts }">
          <Icon name="lucide:gift" />
          <span>礼物</span>
        </button>
      </div>
      
      <div class="toolbar-right">
        <button class="tool-btn" @click="togglePictureInPicture" v-if="supportsPiP">
          <Icon name="lucide:picture-in-picture" />
        </button>
        
        <button class="tool-btn" @click="reportStream">
          <Icon name="lucide:flag" />
        </button>
        
        <button class="tool-btn toggle-transparency" @click="toggleTransparency">
          <Icon name="lucide:layers" />
        </button>
      </div>
    </div>
    
    <!-- 性能监控 (开发模式) -->
    <div class="performance-monitor" v-if="showPerformanceMonitor && isDevelopment && !isMinimized">
      <div class="monitor-title">性能监控</div>
      <div class="monitor-stats">
        <div class="stat">FPS: {{ performance.fps }}</div>
        <div class="stat">内存: {{ performance.memory }}MB</div>
        <div class="stat">CPU: {{ performance.cpu }}%</div>
        <div class="stat">网络: {{ performance.network }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 直播信息接口
interface StreamInfo {
  title: string
  viewerCount: number
  status: 'live' | 'offline' | 'reconnecting' | 'error'
  startTime?: number
}

// 连接质量接口
interface ConnectionQuality {
  level: number // 1-4
  latency: number
  bandwidth: string
  packetLoss: number
}

// 通知接口
interface Notification {
  id: string
  type: 'info' | 'warning' | 'error' | 'success'
  message: string
  timestamp: number
}

// 性能监控接口
interface Performance {
  fps: number
  memory: number
  cpu: number
  network: string
}

// 实时数据接口
interface StreamMetrics {
  likes: number
  gifts: number
  comments: number
}

interface Props {
  visible?: boolean
  streamInfo: StreamInfo
  showMetrics?: boolean
  showQuickActions?: boolean
  showConnectionQuality?: boolean
  showDanmakuControls?: boolean
  showVolumeControl?: boolean
  showBottomToolbar?: boolean
  showPerformanceMonitor?: boolean
  initialVolume?: number
  initialDanmakuDensity?: number
}

const props = withDefaults(defineProps<Props>(), {
  visible: true,
  showMetrics: true,
  showQuickActions: true,
  showConnectionQuality: true,
  showDanmakuControls: true,
  showVolumeControl: true,
  showBottomToolbar: true,
  showPerformanceMonitor: false,
  initialVolume: 80,
  initialDanmakuDensity: 50
})

// Emits
const emit = defineEmits<{
  'toggle-overlay': [visible: boolean]
  'toggle-fullscreen': []
  'toggle-minimize': [minimized: boolean]
  'volume-change': [volume: number]
  'danmaku-density-change': [density: number]
  'mute-toggle': [muted: boolean]
  'screenshot': []
  'share': []
  'settings': []
  'chat-toggle': [show: boolean]
  'danmaku-toggle': [show: boolean]
  'gifts-toggle': [show: boolean]
  'pip-toggle': []
  'report': []
  'transparency-toggle': [transparent: boolean]
}>()

// 响应式数据
const isMinimized = ref(false)
const isFullscreen = ref(false)
const isTransparent = ref(false)
const isMuted = ref(false)
const volume = ref(props.initialVolume)
const danmakuDensity = ref(props.initialDanmakuDensity)
const showChat = ref(true)
const showDanmaku = ref(true)
const showGifts = ref(true)
const showQualityDetails = ref(false)
const streamDuration = ref(0)
const notifications = ref<Notification[]>([])

// 模拟数据
const metrics = ref<StreamMetrics>({
  likes: 12543,
  gifts: 892,
  comments: 3467
})

const connectionQuality = ref<ConnectionQuality>({
  level: 3,
  latency: 45,
  bandwidth: '2.1 Mbps',
  packetLoss: 0.2
})

const performance = ref<Performance>({
  fps: 60,
  memory: 156,
  cpu: 23,
  network: '1.8MB/s'
})

// 计算属性
const isDevelopment = computed(() => {
  return process.env.NODE_ENV === 'development'
})

const supportsPiP = computed(() => {
  return 'pictureInPictureEnabled' in document
})

// 方法
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}

const getStatusText = (status: string): string => {
  const statusMap = {
    live: '直播中',
    offline: '离线',
    reconnecting: '重连中',
    error: '连接错误'
  }
  return statusMap[status] || '未知状态'
}

const getQualityText = (level: number): string => {
  const qualityMap = {
    1: '差',
    2: '一般',
    3: '良好',
    4: '优秀'
  }
  return qualityMap[level] || '未知'
}

const getNotificationIcon = (type: string): string => {
  const iconMap = {
    info: 'lucide:info',
    warning: 'lucide:alert-triangle',
    error: 'lucide:alert-circle',
    success: 'lucide:check-circle'
  }
  return iconMap[type] || 'lucide:bell'
}

const toggleOverlay = () => {
  emit('toggle-overlay', !props.visible)
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  emit('toggle-fullscreen')
}

const toggleMinimize = () => {
  isMinimized.value = !isMinimized.value
  emit('toggle-minimize', isMinimized.value)
}

const toggleTransparency = () => {
  isTransparent.value = !isTransparent.value
  emit('transparency-toggle', isTransparent.value)
}

const toggleMute = () => {
  isMuted.value = !isMuted.value
  emit('mute-toggle', isMuted.value)
}

const updateVolume = () => {
  emit('volume-change', volume.value)
}

const updateDanmakuDensity = () => {
  emit('danmaku-density-change', danmakuDensity.value)
}

const captureScreenshot = () => {
  emit('screenshot')
}

const shareStream = () => {
  emit('share')
}

const openSettings = () => {
  emit('settings')
}

const toggleChat = () => {
  showChat.value = !showChat.value
  emit('chat-toggle', showChat.value)
}

const toggleDanmaku = () => {
  showDanmaku.value = !showDanmaku.value
  emit('danmaku-toggle', showDanmaku.value)
}

const toggleGifts = () => {
  showGifts.value = !showGifts.value
  emit('gifts-toggle', showGifts.value)
}

const togglePictureInPicture = () => {
  emit('pip-toggle')
}

const reportStream = () => {
  emit('report')
}

const dismissNotification = (id: string) => {
  const index = notifications.value.findIndex(n => n.id === id)
  if (index > -1) {
    notifications.value.splice(index, 1)
  }
}

const addNotification = (type: Notification['type'], message: string) => {
  const notification: Notification = {
    id: Date.now().toString(),
    type,
    message,
    timestamp: Date.now()
  }
  notifications.value.push(notification)
  
  // 自动移除通知
  setTimeout(() => {
    dismissNotification(notification.id)
  }, 5000)
}

// 生命周期
let durationTimer: NodeJS.Timeout
let performanceTimer: NodeJS.Timeout

onMounted(() => {
  // 更新直播时长
  if (props.streamInfo.status === 'live' && props.streamInfo.startTime) {
    durationTimer = setInterval(() => {
      streamDuration.value = Math.floor((Date.now() - props.streamInfo.startTime!) / 1000)
    }, 1000)
  }
  
  // 更新性能数据
  if (props.showPerformanceMonitor && isDevelopment.value) {
    performanceTimer = setInterval(() => {
      performance.value = {
        fps: Math.floor(Math.random() * 10) + 55,
        memory: Math.floor(Math.random() * 50) + 120,
        cpu: Math.floor(Math.random() * 30) + 15,
        network: (Math.random() * 2 + 1).toFixed(1) + 'MB/s'
      }
    }, 2000)
  }
  
  // 模拟连接质量变化
  setInterval(() => {
    connectionQuality.value.level = Math.floor(Math.random() * 4) + 1
    connectionQuality.value.latency = Math.floor(Math.random() * 100) + 20
    connectionQuality.value.packetLoss = Math.random() * 2
  }, 10000)
})

onUnmounted(() => {
  if (durationTimer) clearInterval(durationTimer)
  if (performanceTimer) clearInterval(performanceTimer)
})

// 暴露方法给父组件
defineExpose({
  addNotification,
  minimize: () => { isMinimized.value = true },
  expand: () => { isMinimized.value = false },
  setVolume: (vol: number) => { volume.value = vol },
  setDanmakuDensity: (density: number) => { danmakuDensity.value = density },
  getMetrics: () => metrics.value,
  getConnectionQuality: () => connectionQuality.value
})
</script>

<style scoped>
.stream-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1000;
  transition: opacity 0.3s ease;
}

.stream-overlay.hidden {
  opacity: 0;
}

.stream-overlay.minimized > *:not(.top-bar) {
  display: none;
}

.stream-overlay > * {
  pointer-events: auto;
}

/* 顶部信息栏 */
.top-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);
  backdrop-filter: blur(10px);
  transition: opacity 0.3s ease;
}

.top-bar.transparent {
  opacity: 0.3;
}

.top-bar:hover {
  opacity: 1;
}

.stream-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stream-title {
  color: white;
  font-size: 16px;
  font-weight: 600;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.viewer-count {
  display: flex;
  align-items: center;
  gap: 6px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.stream-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

/* 直播状态 */
.stream-status {
  position: absolute;
  top: 70px;
  left: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 20px;
  color: white;
  font-size: 12px;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.status-live .status-dot {
  background: #ef4444;
}

.status-offline .status-dot {
  background: #6b7280;
  animation: none;
}

.status-reconnecting .status-dot {
  background: #f59e0b;
}

.status-error .status-dot {
  background: #dc2626;
}

.stream-duration {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 20px;
  color: white;
  font-size: 12px;
  backdrop-filter: blur(10px);
}

/* 实时数据 */
.stream-metrics {
  position: absolute;
  top: 70px;
  right: 16px;
  display: flex;
  gap: 12px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12px;
  color: white;
  backdrop-filter: blur(10px);
  min-width: 60px;
}

.metric-value {
  font-size: 16px;
  font-weight: 700;
}

.metric-label {
  font-size: 10px;
  opacity: 0.8;
}

/* 快捷操作 */
.quick-actions {
  position: absolute;
  top: 50%;
  right: 16px;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.quick-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 8px;
  background: rgba(0, 0, 0, 0.6);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  min-width: 60px;
}

.quick-btn:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.05);
}

.quick-btn.active {
  background: rgba(239, 68, 68, 0.8);
}

/* 连接质量 */
.connection-quality {
  position: absolute;
  bottom: 120px;
  left: 16px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12px;
  padding: 12px;
  backdrop-filter: blur(10px);
}

.quality-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-size: 12px;
  cursor: pointer;
}

.quality-bars {
  display: flex;
  gap: 2px;
  align-items: end;
}

.bar {
  width: 3px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 1px;
  transition: all 0.2s ease;
}

.bar:nth-child(1) { height: 8px; }
.bar:nth-child(2) { height: 12px; }
.bar:nth-child(3) { height: 16px; }
.bar:nth-child(4) { height: 20px; }

.bar.active {
  background: #10b981;
}

.quality-poor .bar.active {
  background: #ef4444;
}

.quality-fair .bar.active {
  background: #f59e0b;
}

.quality-details {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item {
  color: rgba(255, 255, 255, 0.8);
  font-size: 11px;
}

/* 弹幕密度控制 */
.danmaku-controls {
  position: absolute;
  bottom: 80px;
  left: 16px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12px;
  padding: 12px;
  backdrop-filter: blur(10px);
  min-width: 180px;
}

.control-label {
  display: flex;
  align-items: center;
  gap: 6px;
  color: white;
  font-size: 12px;
  margin-bottom: 8px;
}

.density-slider input {
  width: 100%;
  height: 4px;
  border-radius: 2px;
  background: rgba(255, 255, 255, 0.3);
  outline: none;
  appearance: none;
}

.density-slider input::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: white;
  cursor: pointer;
}

.density-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 6px;
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
}

.density-labels span.active {
  color: white;
  font-weight: 600;
}

/* 音量控制 */
.volume-control {
  position: absolute;
  bottom: 120px;
  right: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12px;
  color: white;
  backdrop-filter: blur(10px);
}

.volume-slider {
  width: 80px;
  height: 4px;
  border-radius: 2px;
  background: rgba(255, 255, 255, 0.3);
  outline: none;
  appearance: none;
}

.volume-slider::-webkit-slider-thumb {
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: white;
  cursor: pointer;
}

.volume-display {
  font-size: 11px;
  min-width: 30px;
  text-align: right;
}

/* 通知区域 */
.notifications {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 400px;
  width: 100%;
}

.notification-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border-left: 4px solid transparent;
}

.notification-info {
  border-left-color: #3b82f6;
}

.notification-warning {
  border-left-color: #f59e0b;
}

.notification-error {
  border-left-color: #ef4444;
}

.notification-success {
  border-left-color: #10b981;
}

.close-notification {
  margin-left: auto;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.close-notification:hover {
  color: white;
}

/* 底部工具栏 */
.bottom-toolbar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);
  backdrop-filter: blur(10px);
}

.toolbar-left,
.toolbar-right {
  display: flex;
  gap: 8px;
}

.tool-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 20px;
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.tool-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.tool-btn.active {
  background: rgba(59, 130, 246, 0.8);
}

.toggle-transparency {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  justify-content: center;
}

/* 性能监控 */
.performance-monitor {
  position: absolute;
  top: 120px;
  left: 16px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 12px;
  padding: 12px;
  backdrop-filter: blur(10px);
  min-width: 150px;
}

.monitor-title {
  color: white;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 4px;
}

.monitor-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat {
  color: rgba(255, 255, 255, 0.8);
  font-size: 11px;
}

/* 动画 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from,
.notification-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stream-metrics {
    position: static;
    margin: 16px;
    justify-content: center;
  }
  
  .quick-actions {
    position: static;
    flex-direction: row;
    margin: 16px;
    justify-content: center;
  }
  
  .connection-quality,
  .danmaku-controls,
  .volume-control {
    position: static;
    margin: 8px 16px;
  }
  
  .bottom-toolbar {
    flex-direction: column;
    gap: 8px;
  }
  
  .toolbar-left,
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .stream-title {
    max-width: 200px;
    font-size: 14px;
  }
  
  .metric-item {
    min-width: 50px;
    padding: 6px 8px;
  }
  
  .metric-value {
    font-size: 14px;
  }
  
  .quick-btn {
    min-width: 50px;
    padding: 8px 6px;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .action-btn,
  .tool-btn,
  .quick-btn {
    border: 1px solid rgba(255, 255, 255, 0.5);
  }
  
  .notification-item {
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .stream-overlay,
  .action-btn,
  .tool-btn,
  .quick-btn {
    transition: none;
  }
  
  .status-dot {
    animation: none;
  }
  
  .notification-enter-active,
  .notification-leave-active {
    transition: none;
  }
}
</style>