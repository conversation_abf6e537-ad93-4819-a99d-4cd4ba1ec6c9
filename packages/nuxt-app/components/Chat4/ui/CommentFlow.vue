<template>
  <div class="comment-flow" ref="containerRef" :class="{ 'paused': isPaused }">
    <!-- 评论流容器 -->
    <div ref="scrollRef" class="comment-list" @scroll="handleScroll">
      <div
        v-for="comment in displayComments"
        :key="comment.id"
        class="comment-item"
        :class="[
          `comment-${comment.type}`,
          { 'highlighted': comment.highlighted, 'pinned': comment.pinned }
        ]"
        @click="handleCommentClick(comment)"
      >
        <!-- 头像区域 -->
        <div class="comment-avatar" v-if="comment.type !== 'system'">
          <img v-if="comment.avatar" :src="comment.avatar" :alt="comment.username" />
          <div v-else class="avatar-placeholder">
            {{ comment.username?.charAt(0) || '?' }}
          </div>
          
          <!-- VIP标识 -->
          <div v-if="comment.isVip" class="vip-badge">
            <Icon name="lucide:crown" />
          </div>
          
          <!-- 主播标识 -->
          <div v-if="comment.isHost" class="host-badge">
            <Icon name="lucide:star" />
          </div>
        </div>
        
        <!-- 评论内容 -->
        <div class="comment-content">
          <!-- 用户信息行 -->
          <div class="comment-header" v-if="comment.username && comment.type !== 'system'">
            <span class="username" :style="{ color: comment.nameColor }">
              {{ comment.username }}
            </span>
            
            <!-- 用户等级 -->
            <div v-if="comment.level" class="user-level">
              LV{{ comment.level }}
            </div>
            
            <!-- 特殊标签 -->
            <div v-if="comment.badges" class="user-badges">
              <span 
                v-for="badge in comment.badges" 
                :key="badge.id"
                class="badge"
                :class="`badge-${badge.type}`"
              >
                {{ badge.text }}
              </span>
            </div>
            
            <span class="timestamp">{{ formatTime(comment.timestamp) }}</span>
          </div>
          
          <!-- 普通文本评论 -->
          <div v-if="comment.type === 'comment'" class="comment-text">
            <span v-html="parseCommentText(comment.content)"></span>
          </div>
          
          <!-- 礼物评论 -->
          <div v-else-if="comment.type === 'gift'" class="comment-gift-content">
            <div class="gift-info">
              <img v-if="comment.giftIcon" :src="comment.giftIcon" :alt="comment.giftName" class="gift-icon" />
              <span class="gift-text">
                送出了 <strong>{{ comment.giftName }}</strong>
                <span v-if="comment.giftCount > 1"> x{{ comment.giftCount }}</span>
              </span>
            </div>
            <div class="gift-effect">
              <HeartParticles 
                v-if="comment.showEffect" 
                :auto-emit="true"
                :particle-count="Math.min(comment.giftCount * 2, 10)"
                :color="comment.giftColor || '#ff69b4'"
                :emit-rate="500"
              />
            </div>
          </div>
          
          <!-- 系统消息 -->
          <div v-else-if="comment.type === 'system'" class="comment-text system-text">
            <Icon name="lucide:info" />
            <span>{{ comment.content }}</span>
          </div>
          
          <!-- 进入/离开消息 -->
          <div v-else-if="comment.type === 'enter' || comment.type === 'leave'" class="comment-action">
            <Icon :name="comment.type === 'enter' ? 'lucide:log-in' : 'lucide:log-out'" />
            <span>{{ comment.content }}</span>
          </div>
          
          <!-- 关注消息 -->
          <div v-else-if="comment.type === 'follow'" class="comment-follow-content">
            <Icon name="lucide:heart" />
            <span>{{ comment.content }}</span>
            <HeartParticles 
              :auto-emit="true"
              :particle-count="5"
              color="#ff4757"
              :emit-rate="1000"
            />
          </div>
          
          <!-- 默认文本显示 -->
          <div v-else class="comment-text">
            {{ comment.content }}
          </div>
          
          <!-- 互动按钮 -->
          <div v-if="showInteractions && comment.type === 'comment'" class="comment-actions">
            <button class="action-btn" @click.stop="likeComment(comment)">
              <Icon name="lucide:heart" />
              <span v-if="comment.likes">{{ comment.likes }}</span>
            </button>
            <button class="action-btn" @click.stop="replyToComment(comment)">
              <Icon name="lucide:message-circle" />
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 滚动到底部按钮 -->
    <Transition name="scroll-btn">
      <button 
        v-show="showScrollButton" 
        class="scroll-to-bottom"
        @click="scrollToBottom"
      >
        <Icon name="lucide:arrow-down" />
        <span v-if="unreadCount">{{ unreadCount }}</span>
      </button>
    </Transition>
    
    <!-- 暂停指示器 -->
    <Transition name="pause-indicator">
      <div v-show="isPaused" class="pause-indicator">
        <Icon name="lucide:pause" />
        <span>评论流已暂停</span>
        <button @click="resumeFlow">恢复</button>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import type { LiveComment } from '~/types/chat4'
import HeartParticles from './HeartParticles.vue'

interface Props {
  comments: LiveComment[]
  autoScroll?: boolean
  maxDisplayCount?: number
  pauseOnHover?: boolean
  showInteractions?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoScroll: true,
  maxDisplayCount: 50,
  pauseOnHover: true,
  showInteractions: true
})

// Emits
const emit = defineEmits<{
  'comment-click': [comment: LiveComment]
  'comment-like': [comment: LiveComment]
  'comment-reply': [comment: LiveComment]
  'scroll': [position: number]
  'pause': []
  'resume': []
}>()

const containerRef = ref<HTMLElement>()
const scrollRef = ref<HTMLElement>()
const isPaused = ref(false)
const isUserScrolling = ref(false)
const showScrollButton = ref(false)
const unreadCount = ref(0)
const lastScrollTop = ref(0)
const scrollTimer = ref<number>()

// 显示的评论（限制数量以提高性能）
const displayComments = computed(() => {
  return props.comments.slice(-props.maxDisplayCount)
})

// 格式化时间
const formatTime = (timestamp: number) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else {
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }
}

// 解析评论文本（支持表情符号、@用户、链接等）
const parseCommentText = (text: string): string => {
  // 解析表情符号
  let parsed = text.replace(/:\w+:/g, (match) => {
    const emoji = match.slice(1, -1)
    return `<span class="emoji">${match}</span>`
  })
  
  // 解析@用户
  parsed = parsed.replace(/@(\w+)/g, '<span class="mention">@$1</span>')
  
  // 解析链接
  parsed = parsed.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank" class="link">$1</a>')
  
  return parsed
}

// 事件处理
const handleScroll = (event: Event) => {
  const container = event.target as HTMLElement
  const scrollTop = container.scrollTop
  const scrollHeight = container.scrollHeight
  const clientHeight = container.clientHeight
  
  // 检测用户是否主动滚动
  const isNearBottom = scrollTop + clientHeight >= scrollHeight - 50
  isUserScrolling.value = !isNearBottom
  showScrollButton.value = !isNearBottom
  
  // 暂停自动滚动
  if (props.pauseOnHover && !isNearBottom) {
    pauseFlow()
  } else if (isNearBottom && isPaused.value) {
    resumeFlow()
  }
  
  lastScrollTop.value = scrollTop
  emit('scroll', scrollTop)
  
  // 重置滚动检测
  if (scrollTimer.value) {
    clearTimeout(scrollTimer.value)
  }
  scrollTimer.value = window.setTimeout(() => {
    isUserScrolling.value = false
  }, 1000)
}

const handleCommentClick = (comment: LiveComment) => {
  emit('comment-click', comment)
}

const likeComment = (comment: LiveComment) => {
  emit('comment-like', comment)
}

const replyToComment = (comment: LiveComment) => {
  emit('comment-reply', comment)
}

// 流控制方法
const pauseFlow = () => {
  if (!isPaused.value) {
    isPaused.value = true
    emit('pause')
  }
}

const resumeFlow = () => {
  if (isPaused.value) {
    isPaused.value = false
    emit('resume')
    scrollToBottom()
  }
}

// 自动滚动到底部
const scrollToBottom = () => {
  if (scrollRef.value) {
    nextTick(() => {
      scrollRef.value!.scrollTop = scrollRef.value!.scrollHeight
      unreadCount.value = 0
      showScrollButton.value = false
      isUserScrolling.value = false
    })
  }
}

// 自动滚动处理
const handleAutoScroll = () => {
  if (props.autoScroll && !isUserScrolling.value && !isPaused.value) {
    scrollToBottom()
  } else if (!props.autoScroll || isUserScrolling.value) {
    unreadCount.value++
  }
}

// 入场动画
const animateNewComment = () => {
  if (scrollRef.value) {
    const commentItems = scrollRef.value.querySelectorAll('.comment-item')
    const lastComment = commentItems[commentItems.length - 1] as HTMLElement
    
    if (lastComment) {
      lastComment.classList.add('comment-enter')
      setTimeout(() => {
        lastComment.classList.remove('comment-enter')
      }, 300)
    }
  }
}

// 监听评论变化
watch(
  () => props.comments.length,
  (newLength, oldLength) => {
    if (newLength > oldLength) {
      nextTick(() => {
        handleAutoScroll()
        animateNewComment()
      })
    }
  }
)

watch(
  () => displayComments.value.length,
  (newLength, oldLength) => {
    if (newLength > oldLength) {
      nextTick(() => {
        animateNewComment()
      })
    }
  }
)

// 暴露方法给父组件
defineExpose({
  pause: pauseFlow,
  resume: resumeFlow,
  scrollToBottom,
  getVisibleComments: () => displayComments.value,
  isPaused: () => isPaused.value
})

onMounted(() => {
  if (props.autoScroll) {
    scrollToBottom()
  }
})

onUnmounted(() => {
  if (scrollTimer.value) {
    clearTimeout(scrollTimer.value)
  }
})
</script>

<style scoped>
.comment-flow {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.comment-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 12px;
  scroll-behavior: smooth;
  position: relative;
}

/* 自定义滚动条 */
.comment-list::-webkit-scrollbar {
  width: 4px;
}

.comment-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.comment-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.comment-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 评论项 */
.comment-item {
  display: flex;
  gap: 10px;
  padding: 8px 12px;
  margin-bottom: 6px;
  border-radius: 10px;
  transition: all 0.2s ease;
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  cursor: pointer;
}

.comment-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.comment-item.highlighted {
  background: rgba(255, 235, 59, 0.2);
  border: 1px solid #ffc107;
}

.comment-item.pinned {
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid #4caf50;
}

/* 新评论入场动画 */
.comment-enter {
  animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 头像区域 */
.comment-avatar {
  position: relative;
  flex-shrink: 0;
  width: 36px;
  height: 36px;
}

.comment-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--chat4-primary-color, #667eea), var(--chat4-secondary-color, #764ba2));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
}

.vip-badge, .host-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: white;
}

.vip-badge {
  background: linear-gradient(45deg, #ffd700, #ff8c00);
}

.host-badge {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
}

/* 评论内容 */
.comment-content {
  flex: 1;
  min-width: 0;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  flex-wrap: wrap;
}

.username {
  font-size: 13px;
  font-weight: 600;
  color: var(--chat4-secondary-color, #764ba2);
  cursor: pointer;
}

.user-level {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: 500;
}

.user-badges {
  display: flex;
  gap: 4px;
}

.badge {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: 500;
  color: white;
}

.badge-vip {
  background: linear-gradient(45deg, #ffd700, #ff8c00);
}

.badge-moderator {
  background: linear-gradient(45deg, #4caf50, #45a049);
}

.badge-subscriber {
  background: linear-gradient(45deg, #2196f3, #1976d2);
}

.timestamp {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.5);
  margin-left: auto;
}

/* 评论文本 */
.comment-text {
  font-size: 14px;
  line-height: 1.4;
  color: white;
  word-wrap: break-word;
  word-break: break-word;
}

.comment-text :deep(.emoji) {
  font-size: 16px;
}

.comment-text :deep(.mention) {
  color: #1976d2;
  font-weight: 600;
  cursor: pointer;
}

.comment-text :deep(.link) {
  color: #1976d2;
  text-decoration: none;
}

.comment-text :deep(.link:hover) {
  text-decoration: underline;
}

.system-text {
  display: flex;
  align-items: center;
  gap: 6px;
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
  font-size: 13px;
}

/* 礼物内容 */
.comment-gift-content {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.gift-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.gift-icon {
  width: 24px;
  height: 24px;
}

.gift-text {
  font-size: 14px;
  color: #ff6b6b;
  font-weight: 500;
}

.gift-effect {
  position: absolute;
  top: 0;
  right: 0;
  width: 80px;
  height: 40px;
  pointer-events: none;
}

/* 动作消息 */
.comment-action {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #888;
}

.comment-follow-content {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #ff4757;
  font-weight: 500;
  position: relative;
}

/* 互动按钮 */
.comment-actions {
  display: flex;
  gap: 8px;
  margin-top: 6px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.comment-item:hover .comment-actions {
  opacity: 1;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border: none;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* 滚动到底部按钮 */
.scroll-to-bottom {
  position: absolute;
  bottom: 16px;
  right: 16px;
  width: 48px;
  height: 48px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  z-index: 10;
}

.scroll-to-bottom:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.scroll-to-bottom span {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ff4757;
  color: white;
  font-size: 10px;
  font-weight: bold;
  min-width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 暂停指示器 */
.pause-indicator {
  position: absolute;
  top: 16px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  z-index: 10;
  backdrop-filter: blur(10px);
}

.pause-indicator button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.pause-indicator button:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 不同类型评论的样式 */
.comment-comment {
  /* 普通评论默认样式 */
}

.comment-gift {
  background: rgba(255, 107, 107, 0.1);
  border-left: 3px solid #ff6b6b;
}

.comment-follow {
  background: rgba(255, 206, 84, 0.1);
  border-left: 3px solid #ffce54;
}

.comment-enter, .comment-leave {
  background: rgba(74, 144, 226, 0.1);
  border-left: 3px solid #4a90e2;
}

.comment-system {
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  margin: 8px 0;
  padding: 12px;
}

.comment-system .comment-content {
  text-align: center;
}

/* 动画 */
.scroll-btn-enter-active, .scroll-btn-leave-active {
  transition: all 0.3s ease;
}

.scroll-btn-enter-from, .scroll-btn-leave-to {
  opacity: 0;
  transform: translateY(10px) scale(0.8);
}

.pause-indicator-enter-active, .pause-indicator-leave-active {
  transition: all 0.3s ease;
}

.pause-indicator-enter-from, .pause-indicator-leave-to {
  opacity: 0;
  transform: translateX(-50%) translateY(-10px);
}

/* 暂停状态 */
.comment-flow.paused .comment-list {
  filter: grayscale(0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .comment-list {
    padding: 6px 8px;
  }
  
  .comment-item {
    padding: 6px 8px;
    gap: 8px;
  }
  
  .comment-avatar {
    width: 32px;
    height: 32px;
  }
  
  .avatar-placeholder {
    font-size: 12px;
  }
  
  .username {
    font-size: 12px;
  }
  
  .comment-text {
    font-size: 13px;
  }
  
  .timestamp {
    font-size: 10px;
  }
  
  .scroll-to-bottom {
    width: 44px;
    height: 44px;
    bottom: 12px;
    right: 12px;
  }
}

@media (max-width: 480px) {
  .comment-header {
    flex-wrap: wrap;
  }
  
  .timestamp {
    order: 1;
    width: 100%;
    margin-left: 0;
    margin-top: 2px;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .comment-item {
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
  
  .username {
    font-weight: 700;
  }
  
  .action-btn {
    border: 1px solid currentColor;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .comment-item,
  .action-btn,
  .scroll-to-bottom {
    transition: none;
  }
  
  .scroll-btn-enter-active,
  .scroll-btn-leave-active,
  .pause-indicator-enter-active,
  .pause-indicator-leave-active {
    transition: none;
  }
  
  .comment-enter {
    animation: none;
  }
}

/* 空状态 */
.comment-flow:empty::after {
  content: '暂无评论，快来抢沙发吧！';
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.5);
  font-size: 14px;
  font-style: italic;
}
</style>