<template>
  <div class="ending-content" :class="{ 'show': visible, 'animated': showAnimations }">
    <!-- 背景效果 -->
    <div class="ending-background">
      <div class="gradient-overlay"></div>
      <div class="particles-container" v-if="showParticles">
        <HeartParticles
          :auto-emit="true"
          :particle-count="30"
          :emit-rate="100"
          :color="endingData.particleColor || '#ff69b4'"
          :custom-emojis="endingData.particles || ['✨', '💫', '🌟', '💖']"
          particle-type="emoji"
        />
      </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="ending-main">
      <!-- 角色头像区域 -->
      <div class="character-section" v-if="endingData.character">
        <div class="character-avatar">
          <img 
            v-if="endingData.character.avatar" 
            :src="endingData.character.avatar" 
            :alt="endingData.character.name"
            class="avatar-image"
          />
          <div v-else class="avatar-placeholder">
            {{ endingData.character.name?.charAt(0) || '?' }}
          </div>
          
          <!-- 情感状态指示器 -->
          <div class="emotion-indicator" :class="`emotion-${endingData.character.emotion}`">
            <Icon :name="getEmotionIcon(endingData.character.emotion)" />
          </div>
        </div>
        
        <div class="character-info">
          <div class="character-name">{{ endingData.character.name }}</div>
          <div class="character-relationship">{{ endingData.character.relationship }}</div>
        </div>
      </div>
      
      <!-- 结局标题 -->
      <div class="ending-title">
        <h1 class="title-text">{{ endingData.title }}</h1>
        <div class="title-subtitle" v-if="endingData.subtitle">
          {{ endingData.subtitle }}
        </div>
      </div>
      
      <!-- 结局描述 -->
      <div class="ending-description">
        <div class="description-text" v-html="formattedDescription"></div>
      </div>
      
      <!-- 好感度/成就显示 -->
      <div class="ending-stats" v-if="endingData.stats">
        <div class="stats-grid">
          <div class="stat-item" v-if="endingData.stats.favorability !== undefined">
            <Icon name="lucide:heart" />
            <div class="stat-content">
              <div class="stat-label">好感度</div>
              <div class="stat-value">{{ endingData.stats.favorability }}</div>
            </div>
          </div>
          
          <div class="stat-item" v-if="endingData.stats.interactions !== undefined">
            <Icon name="lucide:message-circle" />
            <div class="stat-content">
              <div class="stat-label">互动次数</div>
              <div class="stat-value">{{ endingData.stats.interactions }}</div>
            </div>
          </div>
          
          <div class="stat-item" v-if="endingData.stats.playTime !== undefined">
            <Icon name="lucide:clock" />
            <div class="stat-content">
              <div class="stat-label">游戏时长</div>
              <div class="stat-value">{{ formatPlayTime(endingData.stats.playTime) }}</div>
            </div>
          </div>
          
          <div class="stat-item" v-if="endingData.stats.achievements !== undefined">
            <Icon name="lucide:trophy" />
            <div class="stat-content">
              <div class="stat-label">成就达成</div>
              <div class="stat-value">{{ endingData.stats.achievements }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 解锁内容 -->
      <div class="unlocked-content" v-if="endingData.unlocked && endingData.unlocked.length > 0">
        <div class="unlocked-title">
          <Icon name="lucide:unlock" />
          <span>解锁内容</span>
        </div>
        
        <div class="unlocked-grid">
          <div
            v-for="item in endingData.unlocked"
            :key="item.id"
            class="unlocked-item"
            :class="`unlock-${item.type}`"
          >
            <div class="unlock-icon">
              <img v-if="item.image" :src="item.image" :alt="item.name" />
              <Icon v-else :name="getUnlockIcon(item.type)" />
            </div>
            <div class="unlock-info">
              <div class="unlock-name">{{ item.name }}</div>
              <div class="unlock-description">{{ item.description }}</div>
            </div>
            <div class="unlock-badge">
              <Icon name="lucide:sparkles" />
            </div>
          </div>
        </div>
      </div>
      
      <!-- 评价系统 -->
      <div class="ending-rating" v-if="showRating">
        <div class="rating-title">
          <Icon name="lucide:star" />
          <span>为这个结局评分</span>
        </div>
        
        <div class="rating-stars">
          <button
            v-for="star in 5"
            :key="star"
            class="star-btn"
            :class="{ 'active': star <= currentRating, 'hover': star <= hoverRating }"
            @click="setRating(star)"
            @mouseenter="hoverRating = star"
            @mouseleave="hoverRating = 0"
          >
            <Icon name="lucide:star" />
          </button>
        </div>
        
        <div class="rating-text" v-if="currentRating > 0">
          {{ getRatingText(currentRating) }}
        </div>
      </div>
      
      <!-- 分享功能 */
      <div class="ending-share" v-if="showShare">
        <div class="share-title">
          <Icon name="lucide:share-2" />
          <span>分享你的结局</span>
        </div>
        
        <div class="share-buttons">
          <button class="share-btn" @click="shareToSocial('screenshot')">
            <Icon name="lucide:camera" />
            <span>截图分享</span>
          </button>
          
          <button class="share-btn" @click="shareToSocial('text')">
            <Icon name="lucide:type" />
            <span>文字分享</span>
          </button>
          
          <button class="share-btn" @click="shareToSocial('link')">
            <Icon name="lucide:link" />
            <span>链接分享</span>
          </button>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="ending-actions">
        <button class="action-btn secondary" @click="restart" v-if="showRestart">
          <Icon name="lucide:rotate-ccw" />
          <span>重新开始</span>
        </button>
        
        <button class="action-btn secondary" @click="saveProgress" v-if="showSave">
          <Icon name="lucide:save" />
          <span>保存进度</span>
        </button>
        
        <button class="action-btn primary" @click="continueStory" v-if="showContinue">
          <Icon name="lucide:play" />
          <span>{{ continueText }}</span>
        </button>
        
        <button class="action-btn secondary" @click="goToMenu" v-if="showMenu">
          <Icon name="lucide:home" />
          <span>返回主页</span>
        </button>
      </div>
    </div>
    
    <!-- 侧边栏信息 -->
    <div class="ending-sidebar" v-if="showSidebar && endingData.sidebarInfo">
      <div class="sidebar-section" v-if="endingData.sidebarInfo.tips">
        <div class="sidebar-title">
          <Icon name="lucide:lightbulb" />
          <span>小贴士</span>
        </div>
        <div class="sidebar-content">
          <div
            v-for="tip in endingData.sidebarInfo.tips"
            :key="tip.id"
            class="tip-item"
          >
            {{ tip.text }}
          </div>
        </div>
      </div>
      
      <div class="sidebar-section" v-if="endingData.sidebarInfo.nextSteps">
        <div class="sidebar-title">
          <Icon name="lucide:compass" />
          <span>接下来</span>
        </div>
        <div class="sidebar-content">
          <div
            v-for="step in endingData.sidebarInfo.nextSteps"
            :key="step.id"
            class="next-step-item"
            @click="selectNextStep(step)"
          >
            <Icon :name="step.icon || 'lucide:arrow-right'" />
            <span>{{ step.text }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div class="ending-loading" v-if="isLoading">
      <div class="loading-spinner"></div>
      <div class="loading-text">{{ loadingText }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import HeartParticles from './HeartParticles.vue'

// 结局数据接口
interface EndingData {
  title: string
  subtitle?: string
  description: string
  type: 'good' | 'bad' | 'neutral' | 'special'
  character?: {
    name: string
    avatar?: string
    relationship: string
    emotion: 'happy' | 'sad' | 'love' | 'angry' | 'surprised' | 'neutral'
  }
  stats?: {
    favorability?: number
    interactions?: number
    playTime?: number // 分钟
    achievements?: number
  }
  unlocked?: UnlockedItem[]
  particleColor?: string
  particles?: string[]
  sidebarInfo?: {
    tips?: { id: string; text: string }[]
    nextSteps?: { id: string; text: string; icon?: string; action: string }[]
  }
}

// 解锁内容接口
interface UnlockedItem {
  id: string
  type: 'scene' | 'character' | 'outfit' | 'special' | 'achievement'
  name: string
  description: string
  image?: string
}

interface Props {
  visible?: boolean
  endingData: EndingData
  showAnimations?: boolean
  showParticles?: boolean
  showRating?: boolean
  showShare?: boolean
  showRestart?: boolean
  showSave?: boolean
  showContinue?: boolean
  showMenu?: boolean
  showSidebar?: boolean
  continueText?: string
  loadingText?: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  showAnimations: true,
  showParticles: true,
  showRating: true,
  showShare: true,
  showRestart: true,
  showSave: true,
  showContinue: false,
  showMenu: true,
  showSidebar: true,
  continueText: '继续故事',
  loadingText: '保存中...'
})

// Emits
const emit = defineEmits<{
  'restart': []
  'save-progress': []
  'continue-story': []
  'go-to-menu': []
  'rate': [rating: number]
  'share': [type: 'screenshot' | 'text' | 'link']
  'next-step': [step: any]
}>()

// 响应式数据
const currentRating = ref(0)
const hoverRating = ref(0)
const isLoading = ref(false)

// 计算属性
const formattedDescription = computed(() => {
  return props.endingData.description.replace(/\n/g, '<br>')
})

// 方法
const getEmotionIcon = (emotion: string): string => {
  const emotionIcons = {
    happy: 'lucide:smile',
    sad: 'lucide:frown',
    love: 'lucide:heart',
    angry: 'lucide:angry',
    surprised: 'lucide:zap',
    neutral: 'lucide:meh'
  }
  return emotionIcons[emotion] || 'lucide:meh'
}

const getUnlockIcon = (type: string): string => {
  const unlockIcons = {
    scene: 'lucide:map',
    character: 'lucide:user',
    outfit: 'lucide:shirt',
    special: 'lucide:star',
    achievement: 'lucide:trophy'
  }
  return unlockIcons[type] || 'lucide:gift'
}

const formatPlayTime = (minutes: number): string => {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  
  if (hours > 0) {
    return `${hours}小时${mins}分钟`
  }
  return `${mins}分钟`
}

const getRatingText = (rating: number): string => {
  const ratingTexts = {
    1: '不太满意',
    2: '还可以',
    3: '不错',
    4: '很喜欢',
    5: '完美结局！'
  }
  return ratingTexts[rating] || ''
}

const setRating = (rating: number) => {
  currentRating.value = rating
  emit('rate', rating)
}

const shareToSocial = (type: 'screenshot' | 'text' | 'link') => {
  emit('share', type)
}

const restart = () => {
  emit('restart')
}

const saveProgress = async () => {
  isLoading.value = true
  
  try {
    // 模拟保存延迟
    await new Promise(resolve => setTimeout(resolve, 2000))
    emit('save-progress')
  } finally {
    isLoading.value = false
  }
}

const continueStory = () => {
  emit('continue-story')
}

const goToMenu = () => {
  emit('go-to-menu')
}

const selectNextStep = (step: any) => {
  emit('next-step', step)
}

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    // 重置评分
    currentRating.value = 0
    hoverRating.value = 0
  }
})

// 暴露方法给父组件
defineExpose({
  setRating: (rating: number) => { currentRating.value = rating },
  getRating: () => currentRating.value,
  triggerSave: saveProgress,
  showLoading: (show: boolean) => { isLoading.value = show }
})
</script>

<style scoped>
.ending-content {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  opacity: 0;
  transform: scale(0.95);
  transition: all 0.5s ease;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.ending-content.show {
  opacity: 1;
  transform: scale(1);
}

/* 背景效果 */
.ending-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.9) 0%,
    rgba(118, 75, 162, 0.9) 50%,
    rgba(255, 105, 180, 0.9) 100%
  );
}

.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

/* 主要内容 */
.ending-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  z-index: 1;
  position: relative;
  overflow-y: auto;
  max-height: 100vh;
}

/* 角色区域 */
.character-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32px;
  animation: slideInUp 0.8s ease-out 0.2s both;
}

.character-avatar {
  position: relative;
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}

.avatar-image,
.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.avatar-image {
  object-fit: cover;
}

.avatar-placeholder {
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 48px;
  font-weight: 600;
}

.emotion-indicator {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  border: 2px solid white;
}

.emotion-happy { background: #10b981; }
.emotion-sad { background: #6b7280; }
.emotion-love { background: #ef4444; }
.emotion-angry { background: #f59e0b; }
.emotion-surprised { background: #8b5cf6; }
.emotion-neutral { background: #64748b; }

.character-info {
  text-align: center;
}

.character-name {
  font-size: 24px;
  font-weight: 700;
  color: white;
  margin-bottom: 4px;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
}

.character-relationship {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.5);
}

/* 结局标题 */
.ending-title {
  text-align: center;
  margin-bottom: 32px;
  animation: slideInUp 0.8s ease-out 0.4s both;
}

.title-text {
  font-size: 48px;
  font-weight: 800;
  color: white;
  margin-bottom: 8px;
  text-shadow: 0 4px 16px rgba(0, 0, 0, 0.5);
  line-height: 1.2;
}

.title-subtitle {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
}

/* 结局描述 */
.ending-description {
  max-width: 600px;
  text-align: center;
  margin-bottom: 32px;
  animation: slideInUp 0.8s ease-out 0.6s both;
}

.description-text {
  font-size: 16px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.5);
}

/* 统计数据 */
.ending-stats {
  margin-bottom: 32px;
  animation: slideInUp 0.8s ease-out 0.8s both;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-item > svg {
  color: rgba(255, 255, 255, 0.8);
  font-size: 20px;
}

.stat-content {
  flex: 1;
}

.stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 2px;
}

.stat-value {
  font-size: 18px;
  font-weight: 700;
  color: white;
}

/* 解锁内容 */
.unlocked-content {
  margin-bottom: 32px;
  animation: slideInUp 0.8s ease-out 1s both;
  max-width: 800px;
  width: 100%;
}

.unlocked-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin-bottom: 16px;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  justify-content: center;
}

.unlocked-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.unlocked-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  transition: all 0.3s ease;
}

.unlocked-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.unlock-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.unlock-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 12px;
}

.unlock-info {
  flex: 1;
}

.unlock-name {
  font-size: 14px;
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.unlock-description {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
}

.unlock-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  color: #ffd700;
  font-size: 16px;
  animation: sparkle 1.5s ease-in-out infinite;
}

/* 评价系统 */
.ending-rating {
  text-align: center;
  margin-bottom: 24px;
  animation: slideInUp 0.8s ease-out 1.2s both;
}

.rating-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 12px;
}

.rating-stars {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
}

.star-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.3);
  font-size: 24px;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 4px;
  border-radius: 4px;
}

.star-btn:hover,
.star-btn.hover {
  color: #ffd700;
  transform: scale(1.1);
}

.star-btn.active {
  color: #ffd700;
}

.rating-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  font-style: italic;
}

/* 分享功能 */
.ending-share {
  text-align: center;
  margin-bottom: 32px;
  animation: slideInUp 0.8s ease-out 1.4s both;
}

.share-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 12px;
}

.share-buttons {
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
}

.share-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.share-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* 操作按钮 */
.ending-actions {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: center;
  animation: slideInUp 0.8s ease-out 1.6s both;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.action-btn.primary {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: 1px solid rgba(16, 185, 129, 0.5);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.action-btn.primary:hover {
  background: linear-gradient(135deg, #059669, #047857);
}

.action-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 侧边栏 */
.ending-sidebar {
  width: 300px;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  padding: 24px;
  overflow-y: auto;
  z-index: 1;
  border-left: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-section {
  margin-bottom: 24px;
}

.sidebar-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 12px;
}

.sidebar-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tip-item {
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

.next-step-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  font-size: 14px;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.next-step-item:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 加载状态 */
.ending-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-text {
  color: white;
  font-size: 16px;
}

/* 动画定义 */
@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes sparkle {
  0%, 100% { transform: scale(1) rotate(0deg); }
  50% { transform: scale(1.2) rotate(180deg); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .ending-content {
    flex-direction: column;
  }
  
  .ending-sidebar {
    width: 100%;
    max-height: 200px;
    border-left: none;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
}

@media (max-width: 768px) {
  .ending-main {
    padding: 24px 16px;
  }
  
  .title-text {
    font-size: 36px;
  }
  
  .character-avatar {
    width: 100px;
    height: 100px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .unlocked-grid {
    grid-template-columns: 1fr;
  }
  
  .ending-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .action-btn {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .title-text {
    font-size: 28px;
  }
  
  .character-avatar {
    width: 80px;
    height: 80px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .ending-sidebar {
    padding: 16px;
  }
  
  .share-buttons {
    flex-direction: column;
    align-items: center;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .stat-item,
  .unlocked-item,
  .share-btn,
  .action-btn {
    border-width: 2px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .ending-content,
  .character-section,
  .ending-title,
  .ending-description,
  .ending-stats,
  .unlocked-content,
  .ending-rating,
  .ending-share,
  .ending-actions {
    animation: none;
  }
  
  .action-btn,
  .unlocked-item,
  .star-btn {
    transition: none;
  }
  
  .unlock-badge {
    animation: none;
  }
  
  .loading-spinner {
    animation: none;
    border-top-color: transparent;
  }
}
</style>