<template>
  <Teleport to="body">
    <div class="payment-modal-overlay" :class="{ 'visible': visible }" @click="handleOverlayClick">
      <div class="payment-modal" :class="{ 'show': visible }" @click.stop>
        <!-- 模态框头部 -->
        <div class="modal-header">
          <div class="header-title">
            <Icon name="lucide:credit-card" />
            <span>确认支付</span>
          </div>
          <button class="close-btn" @click="closeModal">
            <Icon name="lucide:x" />
          </button>
        </div>
        
        <!-- 支付详情 -->
        <div class="payment-details">
          <!-- 商品信息 -->
          <div class="item-info">
            <div class="item-image">
              <img v-if="paymentData.itemImage" :src="paymentData.itemImage" :alt="paymentData.itemName" />
              <div v-else class="image-placeholder">
                <Icon name="lucide:gift" />
              </div>
            </div>
            <div class="item-details">
              <div class="item-name">{{ paymentData.itemName }}</div>
              <div class="item-description">{{ paymentData.itemDescription }}</div>
              <div class="item-quantity" v-if="paymentData.quantity > 1">
                数量: {{ paymentData.quantity }}
              </div>
            </div>
          </div>
          
          <!-- 价格明细 -->
          <div class="price-breakdown">
            <div class="price-item">
              <span>单价</span>
              <span>{{ formatPrice(paymentData.unitPrice) }}</span>
            </div>
            <div v-if="paymentData.quantity > 1" class="price-item">
              <span>数量</span>
              <span>{{ paymentData.quantity }}</span>
            </div>
            <div v-if="paymentData.discount > 0" class="price-item discount">
              <span>优惠</span>
              <span>-{{ formatPrice(paymentData.discount) }}</span>
            </div>
            <div class="price-item total">
              <span>总计</span>
              <span>{{ formatPrice(totalAmount) }}</span>
            </div>
          </div>
          
          <!-- 接收者信息 -->
          <div v-if="paymentData.recipient" class="recipient-info">
            <div class="recipient-label">
              <Icon name="lucide:user" />
              <span>接收者</span>
            </div>
            <div class="recipient-details">
              <img v-if="paymentData.recipient.avatar" :src="paymentData.recipient.avatar" :alt="paymentData.recipient.name" class="recipient-avatar" />
              <div class="recipient-name">{{ paymentData.recipient.name }}</div>
            </div>
          </div>
          
          <!-- 当前余额 -->
          <div class="balance-info">
            <div class="balance-label">
              <Icon name="lucide:wallet" />
              <span>当前余额</span>
            </div>
            <div class="balance-amount" :class="{ 'insufficient': isInsufficientBalance }">
              {{ formatPrice(currentBalance) }}
            </div>
          </div>
          
          <!-- 余额不足提示 -->
          <div v-if="isInsufficientBalance" class="insufficient-notice">
            <Icon name="lucide:alert-triangle" />
            <span>余额不足，需要充值 {{ formatPrice(totalAmount - currentBalance) }}</span>
          </div>
        </div>
        
        <!-- 支付方式选择 -->
        <div class="payment-methods">
          <div class="methods-title">
            <Icon name="lucide:credit-card" />
            <span>支付方式</span>
          </div>
          <div class="methods-list">
            <div
              v-for="method in paymentMethods"
              :key="method.id"
              class="method-item"
              :class="{ 
                'selected': selectedMethod === method.id,
                'disabled': method.disabled || (method.requireSufficientBalance && isInsufficientBalance)
              }"
              @click="selectPaymentMethod(method.id)"
            >
              <div class="method-icon">
                <Icon :name="method.icon" />
              </div>
              <div class="method-info">
                <div class="method-name">{{ method.name }}</div>
                <div class="method-description">{{ method.description }}</div>
                <div v-if="method.bonus" class="method-bonus">
                  <Icon name="lucide:gift" />
                  <span>{{ method.bonus }}</span>
                </div>
              </div>
              <div class="method-check">
                <Icon v-if="selectedMethod === method.id" name="lucide:check-circle" />
                <Icon v-else name="lucide:circle" />
              </div>
            </div>
          </div>
        </div>
        
        <!-- 优惠码 -->
        <div class="coupon-section">
          <div class="coupon-input">
            <input
              v-model="couponCode"
              type="text"
              placeholder="输入优惠码（可选）"
              class="coupon-field"
              @input="validateCoupon"
            />
            <button 
              class="apply-coupon-btn" 
              :class="{ 'loading': isValidatingCoupon }"
              :disabled="!couponCode || isValidatingCoupon"
              @click="applyCoupon"
            >
              <Icon v-if="isValidatingCoupon" name="lucide:loader-2" class="animate-spin" />
              <span v-else>应用</span>
            </button>
          </div>
          <div v-if="appliedCoupon" class="applied-coupon">
            <Icon name="lucide:check-circle" />
            <span>{{ appliedCoupon.name }} ({{ appliedCoupon.description }})</span>
            <button class="remove-coupon" @click="removeCoupon">
              <Icon name="lucide:x" />
            </button>
          </div>
        </div>
        
        <!-- 安全提示 -->
        <div class="security-notice">
          <Icon name="lucide:shield-check" />
          <span>您的支付信息将通过加密通道安全传输</span>
        </div>
        
        <!-- 底部操作 -->
        <div class="modal-footer">
          <button class="cancel-btn" @click="closeModal" :disabled="isProcessing">
            取消
          </button>
          <button 
            class="confirm-btn" 
            :class="{ 'loading': isProcessing }"
            :disabled="!canConfirmPayment"
            @click="confirmPayment"
          >
            <Icon v-if="isProcessing" name="lucide:loader-2" class="animate-spin" />
            <span v-else>确认支付 {{ formatPrice(totalAmount) }}</span>
          </button>
        </div>
        
        <!-- 支付成功动画 -->
        <div v-if="showSuccessAnimation" class="success-animation">
          <div class="success-content">
            <div class="success-icon">
              <Icon name="lucide:check-circle" />
            </div>
            <div class="success-text">支付成功！</div>
            <HeartParticles 
              :auto-emit="true"
              :particle-count="20"
              :emit-rate="300"
              color="#4caf50"
              :custom-emojis="['✨', '🎉', '💫', '🎊']"
              particle-type="emoji"
            />
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import HeartParticles from './HeartParticles.vue'

// 支付数据接口
interface PaymentData {
  itemName: string
  itemDescription: string
  itemImage?: string
  unitPrice: number
  quantity: number
  discount: number
  recipient?: {
    id: string
    name: string
    avatar?: string
  }
}

interface PaymentMethod {
  id: string
  name: string
  description: string
  icon: string
  disabled?: boolean
  requireSufficientBalance?: boolean
  bonus?: string
}

interface Coupon {
  code: string
  name: string
  description: string
  discount: number
  type: 'fixed' | 'percentage'
}

interface Props {
  visible: boolean
  paymentData: PaymentData
  currentBalance: number
  showCouponSection?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  paymentData: () => ({
    itemName: '',
    itemDescription: '',
    unitPrice: 0,
    quantity: 1,
    discount: 0
  }),
  currentBalance: 0,
  showCouponSection: true
})

// Emits
const emit = defineEmits<{
  'close': []
  'confirm': [paymentMethod: string, totalAmount: number, coupon?: Coupon]
  'method-select': [methodId: string]
  'coupon-apply': [coupon: Coupon]
  'insufficient-balance': [required: number, current: number]
}>()

// 响应式数据
const selectedMethod = ref('balance')
const couponCode = ref('')
const appliedCoupon = ref<Coupon | null>(null)
const isProcessing = ref(false)
const isValidatingCoupon = ref(false)
const showSuccessAnimation = ref(false)

// 支付方式列表
const paymentMethods: PaymentMethod[] = [
  {
    id: 'balance',
    name: '账户余额',
    description: '使用账户余额支付',
    icon: 'lucide:wallet',
    requireSufficientBalance: true
  },
  {
    id: 'wechat',
    name: '微信支付',
    description: '使用微信进行支付',
    icon: 'lucide:smartphone',
    bonus: '充值立减5%'
  },
  {
    id: 'alipay',
    name: '支付宝',
    description: '使用支付宝进行支付',
    icon: 'lucide:credit-card',
    bonus: '首次充值送10%'
  },
  {
    id: 'card',
    name: '银行卡',
    description: '使用银行卡进行支付',
    icon: 'lucide:credit-card'
  }
]

// 模拟优惠码数据
const availableCoupons: Coupon[] = [
  {
    code: 'WELCOME10',
    name: '新用户优惠',
    description: '立减10元',
    discount: 10,
    type: 'fixed'
  },
  {
    code: 'SAVE20',
    name: '限时优惠',
    description: '8折优惠',
    discount: 20,
    type: 'percentage'
  }
]

// 计算属性
const totalAmount = computed(() => {
  let amount = props.paymentData.unitPrice * props.paymentData.quantity - props.paymentData.discount
  
  if (appliedCoupon.value) {
    if (appliedCoupon.value.type === 'fixed') {
      amount -= appliedCoupon.value.discount
    } else {
      amount = amount * (1 - appliedCoupon.value.discount / 100)
    }
  }
  
  return Math.max(0, amount)
})

const isInsufficientBalance = computed(() => {
  return selectedMethod.value === 'balance' && props.currentBalance < totalAmount.value
})

const canConfirmPayment = computed(() => {
  return selectedMethod.value && 
         !isProcessing.value && 
         (!isInsufficientBalance.value || selectedMethod.value !== 'balance')
})

// 方法
const formatPrice = (price: number): string => {
  return `¥${price.toFixed(2)}`
}

const selectPaymentMethod = (methodId: string) => {
  const method = paymentMethods.find(m => m.id === methodId)
  if (method?.disabled || (method?.requireSufficientBalance && isInsufficientBalance.value)) {
    return
  }
  
  selectedMethod.value = methodId
  emit('method-select', methodId)
}

const validateCoupon = () => {
  // 实时验证优惠码格式
  couponCode.value = couponCode.value.toUpperCase()
}

const applyCoupon = async () => {
  if (!couponCode.value || isValidatingCoupon.value) return
  
  isValidatingCoupon.value = true
  
  try {
    // 模拟验证延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const coupon = availableCoupons.find(c => c.code === couponCode.value)
    if (coupon) {
      appliedCoupon.value = coupon
      couponCode.value = ''
      emit('coupon-apply', coupon)
    } else {
      // 优惠码无效
      alert('优惠码无效')
    }
  } catch (error) {
    console.error('验证优惠码失败:', error)
  } finally {
    isValidatingCoupon.value = false
  }
}

const removeCoupon = () => {
  appliedCoupon.value = null
}

const handleOverlayClick = () => {
  if (!isProcessing.value) {
    closeModal()
  }
}

const closeModal = () => {
  if (isProcessing.value) return
  
  emit('close')
  // 重置状态
  selectedMethod.value = 'balance'
  couponCode.value = ''
  appliedCoupon.value = null
  showSuccessAnimation.value = false
}

const confirmPayment = async () => {
  if (!canConfirmPayment.value) return
  
  // 检查余额不足
  if (isInsufficientBalance.value) {
    emit('insufficient-balance', totalAmount.value, props.currentBalance)
    return
  }
  
  isProcessing.value = true
  
  try {
    // 模拟支付处理
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 显示成功动画
    showSuccessAnimation.value = true
    
    // 触发确认事件
    emit('confirm', selectedMethod.value, totalAmount.value, appliedCoupon.value || undefined)
    
    // 延迟关闭
    setTimeout(() => {
      closeModal()
    }, 2000)
    
  } catch (error) {
    console.error('支付失败:', error)
    alert('支付失败，请重试')
  } finally {
    isProcessing.value = false
  }
}

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    // 模态框打开时重置状态
    selectedMethod.value = 'balance'
    couponCode.value = ''
    appliedCoupon.value = null
    showSuccessAnimation.value = false
    isProcessing.value = false
  }
})

// 暴露方法给父组件
defineExpose({
  selectMethod: selectPaymentMethod,
  applyCoupon: (coupon: Coupon) => { appliedCoupon.value = coupon },
  removeCoupon,
  getTotalAmount: () => totalAmount.value,
  getSelectedMethod: () => selectedMethod.value
})
</script>

<style scoped>
.payment-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.payment-modal-overlay.visible {
  opacity: 1;
  visibility: visible;
}

.payment-modal {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 480px;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(0.9) translateY(20px);
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.payment-modal.show {
  transform: scale(1) translateY(0);
}

/* 头部 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16px 16px 0 0;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* 支付详情 */
.payment-details {
  padding: 24px;
}

.item-info {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
}

.item-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 24px;
}

.item-details {
  flex: 1;
}

.item-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.item-description {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 6px;
}

.item-quantity {
  font-size: 12px;
  color: #999;
}

/* 价格明细 */
.price-breakdown {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 14px;
  color: #666;
}

.price-item:not(:last-child) {
  border-bottom: 1px solid #f0f0f0;
}

.price-item.discount {
  color: #28a745;
}

.price-item.total {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-top: 2px solid #e9ecef;
  margin-top: 8px;
  padding-top: 12px;
}

/* 接收者信息 */
.recipient-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.recipient-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
}

.recipient-details {
  display: flex;
  align-items: center;
  gap: 8px;
}

.recipient-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.recipient-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* 余额信息 */
.balance-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #e8f5e8;
  border-radius: 8px;
  margin-bottom: 16px;
}

.balance-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #28a745;
}

.balance-amount {
  font-size: 16px;
  font-weight: 600;
  color: #28a745;
}

.balance-amount.insufficient {
  color: #dc3545;
}

.insufficient-notice {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  margin-bottom: 16px;
  font-size: 14px;
  color: #856404;
}

/* 支付方式 */
.payment-methods {
  padding: 0 24px 24px;
}

.methods-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.methods-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.method-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.method-item:hover:not(.disabled) {
  border-color: #667eea;
  background: #f8f9ff;
}

.method-item.selected {
  border-color: #667eea;
  background: #f8f9ff;
}

.method-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f8f9fa;
}

.method-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  font-size: 20px;
}

.method-info {
  flex: 1;
}

.method-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.method-description {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.method-bonus {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #28a745;
  font-weight: 500;
}

.method-check {
  color: #667eea;
  font-size: 20px;
}

/* 优惠码 */
.coupon-section {
  padding: 0 24px 24px;
}

.coupon-input {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.coupon-field {
  flex: 1;
  height: 44px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.coupon-field:focus {
  outline: none;
  border-color: #667eea;
}

.apply-coupon-btn {
  width: 80px;
  height: 44px;
  border: none;
  border-radius: 8px;
  background: #667eea;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.apply-coupon-btn:hover:not(:disabled) {
  background: #5a6fd8;
}

.apply-coupon-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.apply-coupon-btn.loading {
  pointer-events: none;
}

.applied-coupon {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #e8f5e8;
  border: 1px solid #28a745;
  border-radius: 8px;
  font-size: 14px;
  color: #28a745;
}

.remove-coupon {
  margin-left: auto;
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 50%;
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

/* 安全提示 */
.security-notice {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: #f8f9fa;
  color: #666;
  font-size: 12px;
  border-top: 1px solid #e9ecef;
}

/* 底部操作 */
.modal-footer {
  display: flex;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  border-radius: 0 0 16px 16px;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 48px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.cancel-btn {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #e9ecef;
}

.cancel-btn:hover:not(:disabled) {
  background: #e9ecef;
}

.confirm-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.confirm-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(40, 167, 69, 0.3);
}

.confirm-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.confirm-btn.loading {
  pointer-events: none;
}

/* 成功动画 */
.success-animation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  z-index: 10;
}

.success-content {
  text-align: center;
  position: relative;
}

.success-icon {
  font-size: 60px;
  color: #28a745;
  margin-bottom: 16px;
  animation: success-bounce 0.6s ease-out;
}

.success-text {
  font-size: 20px;
  font-weight: 600;
  color: #28a745;
  animation: success-fade-in 0.8s ease-out 0.3s both;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes success-bounce {
  0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
  40%, 43% { transform: translateY(-20px); }
  70% { transform: translateY(-10px); }
  90% { transform: translateY(-4px); }
}

@keyframes success-fade-in {
  0% { opacity: 0; transform: translateY(10px); }
  100% { opacity: 1; transform: translateY(0); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .payment-modal {
    max-width: 100%;
    margin: 0;
    border-radius: 16px 16px 0 0;
  }
  
  .modal-header {
    padding: 16px 20px;
  }
  
  .payment-details {
    padding: 20px;
  }
  
  .payment-methods {
    padding: 0 20px 20px;
  }
  
  .coupon-section {
    padding: 0 20px 20px;
  }
  
  .modal-footer {
    padding: 16px 20px;
    flex-direction: column;
  }
  
  .item-info {
    flex-direction: column;
    text-align: center;
  }
  
  .item-image {
    margin: 0 auto;
  }
}

@media (max-width: 480px) {
  .payment-modal-overlay {
    padding: 0;
  }
  
  .payment-modal {
    border-radius: 0;
    max-height: 100vh;
  }
  
  .modal-header {
    border-radius: 0;
  }
  
  .header-title {
    font-size: 16px;
  }
  
  .coupon-input {
    flex-direction: column;
  }
  
  .apply-coupon-btn {
    width: 100%;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .method-item {
    border-width: 2px;
  }
  
  .method-item.selected {
    border-color: #000;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .payment-modal-overlay,
  .payment-modal,
  .method-item,
  .confirm-btn {
    transition: none;
  }
  
  .animate-spin,
  .success-icon,
  .success-text {
    animation: none;
  }
}
</style>