<template>
  <div class="favorability-drawer-overlay" :class="{ 'visible': visible }" @click="handleOverlayClick">
    <div class="favorability-drawer" :class="{ 'open': visible }" @click.stop>
      <!-- 抽屉头部 -->
      <div class="drawer-header">
        <div class="header-title">
          <Icon name="lucide:heart" />
          <span>好感度系统</span>
        </div>
        <div class="header-level">
          <span>LV{{ currentLevel }}</span>
          <div class="level-progress">
            <div class="progress-bar" :style="{ width: `${levelProgress}%` }"></div>
          </div>
        </div>
        <button class="close-btn" @click="closeDrawer">
          <Icon name="lucide:x" />
        </button>
      </div>
      
      <!-- 好感度等级显示 -->
      <div class="level-display">
        <div class="character-avatar">
          <img v-if="characterInfo.avatar" :src="characterInfo.avatar" :alt="characterInfo.name" />
          <div v-else class="avatar-placeholder">
            {{ characterInfo.name?.charAt(0) || '?' }}
          </div>
          
          <!-- 等级徽章 -->
          <div class="level-badge" :class="`level-${currentLevel}`">
            <Icon :name="getLevelIcon(currentLevel)" />
            <span>{{ currentLevel }}</span>
          </div>
        </div>
        
        <div class="level-info">
          <div class="character-name">{{ characterInfo.name }}</div>
          <div class="level-title">{{ getLevelTitle(currentLevel) }}</div>
          <div class="favorability-points">
            <span class="current-points">{{ currentPoints }}</span>
            <span class="separator">/</span>
            <span class="max-points">{{ nextLevelPoints }}</span>
            <span class="unit">好感度</span>
          </div>
        </div>
      </div>
      
      <!-- 好感度进度条 -->
      <div class="progress-section">
        <div class="progress-container">
          <div class="progress-track">
            <div 
              class="progress-fill" 
              :style="{ 
                width: `${levelProgress}%`,
                background: getLevelGradient(currentLevel)
              }"
            ></div>
            
            <!-- 进度点 -->
            <div 
              v-for="milestone in progressMilestones" 
              :key="milestone.value"
              class="progress-milestone"
              :class="{ 'reached': currentPoints >= milestone.value }"
              :style="{ left: `${(milestone.value / nextLevelPoints) * 100}%` }"
              @click="showMilestoneTooltip(milestone)"
            >
              <Icon :name="milestone.icon" />
              <div class="milestone-tooltip" v-if="activeMilestone?.value === milestone.value">
                <div class="tooltip-title">{{ milestone.title }}</div>
                <div class="tooltip-reward">{{ milestone.reward }}</div>
              </div>
            </div>
          </div>
          
          <div class="progress-labels">
            <span class="label-start">{{ getLevelTitle(currentLevel) }}</span>
            <span class="label-end">{{ getLevelTitle(currentLevel + 1) }}</span>
          </div>
        </div>
      </div>
      
      <!-- 增加好感度方式 -->
      <div class="favorability-actions">
        <div class="actions-title">
          <Icon name="lucide:trending-up" />
          <span>提升好感度</span>
        </div>
        
        <div class="action-grid">
          <div
            v-for="action in favorabilityActions"
            :key="action.id"
            class="action-item"
            :class="{ 
              'affordable': action.cost <= userBalance,
              'recommended': action.recommended 
            }"
            @click="selectAction(action)"
          >
            <div class="action-icon">
              <img v-if="action.image" :src="action.image" :alt="action.name" />
              <Icon v-else :name="action.icon" />
            </div>
            
            <div class="action-info">
              <div class="action-name">{{ action.name }}</div>
              <div class="action-description">{{ action.description }}</div>
              <div class="action-reward">
                <Icon name="lucide:heart" />
                <span>+{{ action.favorability }}</span>
              </div>
            </div>
            
            <div class="action-cost">
              <Icon name="lucide:coins" />
              <span>{{ action.cost }}</span>
            </div>
            
            <!-- 推荐标识 -->
            <div v-if="action.recommended" class="recommended-badge">
              推荐
            </div>
            
            <!-- 限时标识 -->
            <div v-if="action.limited" class="limited-badge">
              限时
            </div>
          </div>
        </div>
      </div>
      
      <!-- 好感度历史记录 -->
      <div class="favorability-history" v-if="showHistory">
        <div class="history-title">
          <Icon name="lucide:clock" />
          <span>最近记录</span>
          <button class="toggle-history" @click="showHistory = !showHistory">
            <Icon name="lucide:chevron-up" />
          </button>
        </div>
        
        <div class="history-list">
          <div
            v-for="record in recentHistory"
            :key="record.id"
            class="history-item"
            :class="`history-${record.type}`"
          >
            <div class="history-icon">
              <Icon :name="getHistoryIcon(record.type)" />
            </div>
            <div class="history-content">
              <div class="history-action">{{ record.action }}</div>
              <div class="history-time">{{ formatTime(record.timestamp) }}</div>
            </div>
            <div class="history-points" :class="{ 'positive': record.points > 0, 'negative': record.points < 0 }">
              {{ record.points > 0 ? '+' : '' }}{{ record.points }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 等级奖励预览 -->
      <div class="level-rewards" v-if="nextLevelRewards.length > 0">
        <div class="rewards-title">
          <Icon name="lucide:gift" />
          <span>下一等级奖励预览</span>
        </div>
        
        <div class="rewards-grid">
          <div
            v-for="reward in nextLevelRewards"
            :key="reward.id"
            class="reward-item"
          >
            <div class="reward-icon">
              <img v-if="reward.image" :src="reward.image" :alt="reward.name" />
              <Icon v-else :name="reward.icon" />
            </div>
            <div class="reward-info">
              <div class="reward-name">{{ reward.name }}</div>
              <div class="reward-description">{{ reward.description }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 底部操作 -->
      <div class="drawer-footer">
        <div class="footer-stats">
          <div class="stat-item">
            <Icon name="lucide:calendar" />
            <span>相识 {{ relationshipDays }} 天</span>
          </div>
          <div class="stat-item">
            <Icon name="lucide:trending-up" />
            <span>今日 +{{ todayPoints }} 好感度</span>
          </div>
        </div>
        
        <div class="footer-actions">
          <button class="history-toggle" @click="showHistory = !showHistory">
            <Icon :name="showHistory ? 'lucide:chevron-down' : 'lucide:chevron-up'" />
            <span>{{ showHistory ? '收起' : '查看历史' }}</span>
          </button>
          <button 
            class="quick-boost-btn" 
            :disabled="!canQuickBoost"
            @click="performQuickBoost"
          >
            <Icon name="lucide:zap" />
            <span>快速提升</span>
          </button>
        </div>
      </div>
      
      <!-- 好感度提升效果 -->
      <div v-if="showBoostEffect" class="boost-effect">
        <HeartParticles 
          :auto-emit="true"
          :particle-count="15"
          :emit-rate="200"
          color="#ff69b4"
          :custom-emojis="['💖', '💕', '💗', '💘']"
          particle-type="emoji"
        />
        <div class="boost-text">
          <div class="boost-amount">+{{ lastBoostAmount }}</div>
          <div class="boost-label">好感度</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import HeartParticles from './HeartParticles.vue'

// 好感度动作接口
interface FavorabilityAction {
  id: string
  name: string
  description: string
  icon: string
  image?: string
  cost: number
  favorability: number
  recommended?: boolean
  limited?: boolean
  category: 'gift' | 'activity' | 'interaction' | 'special'
}

// 历史记录接口
interface FavorabilityRecord {
  id: string
  action: string
  points: number
  timestamp: number
  type: 'gift' | 'chat' | 'activity' | 'penalty' | 'bonus'
}

// 等级奖励接口
interface LevelReward {
  id: string
  name: string
  description: string
  icon: string
  image?: string
  type: 'unlock' | 'bonus' | 'special'
}

// 里程碑接口
interface ProgressMilestone {
  value: number
  title: string
  reward: string
  icon: string
}

// 角色信息接口
interface CharacterInfo {
  id: string
  name: string
  avatar?: string
}

interface Props {
  visible: boolean
  characterInfo: CharacterInfo
  currentPoints: number
  userBalance: number
  showHistory?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  currentPoints: 0,
  userBalance: 1000,
  showHistory: false
})

// Emits
const emit = defineEmits<{
  'close': []
  'action-select': [action: FavorabilityAction]
  'quick-boost': []
  'milestone-click': [milestone: ProgressMilestone]
}>()

// 响应式数据
const showHistory = ref(props.showHistory)
const activeMilestone = ref<ProgressMilestone | null>(null)
const showBoostEffect = ref(false)
const lastBoostAmount = ref(0)

// 等级配置
const levelThresholds = [0, 100, 300, 600, 1000, 1500, 2200, 3000, 4000, 5200, 6600, 8200, 10000]
const levelTitles = [
  '陌生人', '初相识', '有好感', '朋友', '好朋友', 
  '知心朋友', '特别的人', '心动', '暧昧', '恋人', '深爱', '灵魂伴侣'
]

// 计算属性
const currentLevel = computed(() => {
  for (let i = levelThresholds.length - 1; i >= 0; i--) {
    if (props.currentPoints >= levelThresholds[i]) {
      return i
    }
  }
  return 0
})

const nextLevelPoints = computed(() => {
  const nextLevel = Math.min(currentLevel.value + 1, levelThresholds.length - 1)
  return levelThresholds[nextLevel]
})

const levelProgress = computed(() => {
  if (currentLevel.value >= levelThresholds.length - 1) return 100
  
  const currentLevelMin = levelThresholds[currentLevel.value]
  const nextLevelMin = levelThresholds[currentLevel.value + 1]
  const progress = ((props.currentPoints - currentLevelMin) / (nextLevelMin - currentLevelMin)) * 100
  
  return Math.max(0, Math.min(100, progress))
})

const relationshipDays = computed(() => {
  // 模拟相识天数
  return Math.floor(Math.random() * 365) + 1
})

const todayPoints = computed(() => {
  // 模拟今日获得的好感度
  return Math.floor(Math.random() * 50) + 10
})

const canQuickBoost = computed(() => {
  return props.userBalance >= 100 && currentLevel.value < levelThresholds.length - 1
})

// 进度里程碑
const progressMilestones = computed(() => {
  const milestones: ProgressMilestone[] = []
  const currentLevelMin = levelThresholds[currentLevel.value]
  const nextLevelMin = nextLevelPoints.value
  const step = (nextLevelMin - currentLevelMin) / 4
  
  for (let i = 1; i <= 3; i++) {
    milestones.push({
      value: currentLevelMin + step * i,
      title: `里程碑 ${i}`,
      reward: `解锁新互动方式`,
      icon: 'lucide:star'
    })
  }
  
  return milestones
})

// 好感度提升方式
const favorabilityActions: FavorabilityAction[] = [
  {
    id: 'daily_chat',
    name: '日常聊天',
    description: '温馨的日常对话',
    icon: 'lucide:message-circle',
    cost: 0,
    favorability: 5,
    recommended: true,
    category: 'interaction'
  },
  {
    id: 'send_flower',
    name: '送花',
    description: '表达心意的鲜花',
    icon: 'lucide:flower',
    cost: 50,
    favorability: 20,
    category: 'gift'
  },
  {
    id: 'watch_movie',
    name: '一起看电影',
    description: '共度美好时光',
    icon: 'lucide:film',
    cost: 100,
    favorability: 35,
    recommended: true,
    category: 'activity'
  },
  {
    id: 'romantic_dinner',
    name: '烛光晚餐',
    description: '浪漫的二人世界',
    icon: 'lucide:utensils',
    cost: 200,
    favorability: 50,
    category: 'activity'
  },
  {
    id: 'surprise_gift',
    name: '惊喜礼物',
    description: '精心准备的礼物',
    icon: 'lucide:gift',
    cost: 300,
    favorability: 80,
    limited: true,
    category: 'special'
  },
  {
    id: 'travel_together',
    name: '一起旅行',
    description: '美好的回忆之旅',
    icon: 'lucide:map-pin',
    cost: 500,
    favorability: 120,
    category: 'special'
  }
]

// 历史记录数据
const recentHistory: FavorabilityRecord[] = [
  {
    id: '1',
    action: '发送了玫瑰花',
    points: 20,
    timestamp: Date.now() - 1000 * 60 * 5,
    type: 'gift'
  },
  {
    id: '2',
    action: '进行了温馨对话',
    points: 5,
    timestamp: Date.now() - 1000 * 60 * 30,
    type: 'chat'
  },
  {
    id: '3',
    action: '一起看了电影',
    points: 35,
    timestamp: Date.now() - 1000 * 60 * 60 * 2,
    type: 'activity'
  }
]

// 下一等级奖励
const nextLevelRewards = computed(() => {
  const rewards: LevelReward[] = []
  const nextLevel = currentLevel.value + 1
  
  if (nextLevel < levelTitles.length) {
    rewards.push(
      {
        id: 'new_scene',
        name: '新场景解锁',
        description: `解锁 ${levelTitles[nextLevel]} 专属场景`,
        icon: 'lucide:map',
        type: 'unlock'
      },
      {
        id: 'chat_bonus',
        name: '对话加成',
        description: '聊天好感度获得 +50%',
        icon: 'lucide:trending-up',
        type: 'bonus'
      }
    )
    
    if (nextLevel >= 5) {
      rewards.push({
        id: 'special_gift',
        name: '专属礼物',
        description: '解锁特殊礼物选项',
        icon: 'lucide:star',
        type: 'special'
      })
    }
  }
  
  return rewards
})

// 方法
const getLevelTitle = (level: number): string => {
  return levelTitles[Math.min(level, levelTitles.length - 1)]
}

const getLevelIcon = (level: number): string => {
  const icons = [
    'lucide:user', 'lucide:users', 'lucide:smile', 'lucide:heart-handshake',
    'lucide:heart', 'lucide:hearts', 'lucide:crown', 'lucide:diamond',
    'lucide:gem', 'lucide:star', 'lucide:sparkles', 'lucide:infinity'
  ]
  return icons[Math.min(level, icons.length - 1)]
}

const getLevelGradient = (level: number): string => {
  const gradients = [
    'linear-gradient(135deg, #9ca3af, #6b7280)',
    'linear-gradient(135deg, #60a5fa, #3b82f6)',
    'linear-gradient(135deg, #34d399, #10b981)',
    'linear-gradient(135deg, #f59e0b, #d97706)',
    'linear-gradient(135deg, #ec4899, #db2777)',
    'linear-gradient(135deg, #8b5cf6, #7c3aed)',
    'linear-gradient(135deg, #06b6d4, #0891b2)',
    'linear-gradient(135deg, #ef4444, #dc2626)',
    'linear-gradient(135deg, #f97316, #ea580c)',
    'linear-gradient(135deg, #84cc16, #65a30d)',
    'linear-gradient(135deg, #fbbf24, #f59e0b)',
    'linear-gradient(135deg, #a855f7, #9333ea)'
  ]
  return gradients[Math.min(level, gradients.length - 1)]
}

const getHistoryIcon = (type: string): string => {
  const iconMap = {
    gift: 'lucide:gift',
    chat: 'lucide:message-circle',
    activity: 'lucide:calendar',
    penalty: 'lucide:alert-triangle',
    bonus: 'lucide:star'
  }
  return iconMap[type] || 'lucide:circle'
}

const formatTime = (timestamp: number): string => {
  const diff = Date.now() - timestamp
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days > 0) return `${days}天前`
  if (hours > 0) return `${hours}小时前`
  if (minutes > 0) return `${minutes}分钟前`
  return '刚刚'
}

const handleOverlayClick = () => {
  closeDrawer()
}

const closeDrawer = () => {
  emit('close')
}

const selectAction = (action: FavorabilityAction) => {
  if (action.cost <= props.userBalance) {
    emit('action-select', action)
    
    // 显示提升效果
    lastBoostAmount.value = action.favorability
    showBoostEffect.value = true
    
    setTimeout(() => {
      showBoostEffect.value = false
    }, 2000)
  }
}

const performQuickBoost = () => {
  if (canQuickBoost.value) {
    emit('quick-boost')
    
    // 显示快速提升效果
    lastBoostAmount.value = 50
    showBoostEffect.value = true
    
    setTimeout(() => {
      showBoostEffect.value = false
    }, 2000)
  }
}

const showMilestoneTooltip = (milestone: ProgressMilestone) => {
  activeMilestone.value = activeMilestone.value?.value === milestone.value ? null : milestone
  emit('milestone-click', milestone)
}

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    activeMilestone.value = null
  }
})

// 暴露方法给父组件
defineExpose({
  showBoostEffect: (amount: number) => {
    lastBoostAmount.value = amount
    showBoostEffect.value = true
    setTimeout(() => showBoostEffect.value = false, 2000)
  },
  getCurrentLevel: () => currentLevel.value,
  getLevelProgress: () => levelProgress.value,
  getNextLevelPoints: () => nextLevelPoints.value
})
</script>

<style scoped>
.favorability-drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
}

.favorability-drawer-overlay.visible {
  opacity: 1;
  visibility: visible;
}

.favorability-drawer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  max-height: 85vh;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 50%, #ffa8a8 100%);
  border-radius: 20px 20px 0 0;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.3);
}

.favorability-drawer.open {
  transform: translateY(0);
}

/* 头部 */
.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-size: 18px;
  font-weight: 600;
}

.header-level {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.level-progress {
  width: 60px;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: white;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* 等级显示 */
.level-display {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.character-avatar {
  position: relative;
  width: 80px;
  height: 80px;
}

.character-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
  font-weight: 600;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.level-badge {
  position: absolute;
  bottom: -4px;
  right: -4px;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: linear-gradient(45deg, #ffd700, #ff8c00);
  border-radius: 12px;
  color: white;
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.level-info {
  flex: 1;
}

.character-name {
  font-size: 20px;
  font-weight: 700;
  color: white;
  margin-bottom: 4px;
}

.level-title {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8px;
}

.favorability-points {
  display: flex;
  align-items: baseline;
  gap: 4px;
  font-size: 14px;
  color: white;
}

.current-points {
  font-size: 24px;
  font-weight: 700;
}

.separator {
  font-size: 18px;
  opacity: 0.6;
}

.max-points {
  font-size: 16px;
  opacity: 0.8;
}

.unit {
  font-size: 12px;
  opacity: 0.6;
}

/* 进度区域 */
.progress-section {
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
}

.progress-container {
  position: relative;
}

.progress-track {
  position: relative;
  height: 12px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  overflow: visible;
  margin: 16px 0;
}

.progress-fill {
  height: 100%;
  border-radius: 6px;
  transition: all 0.3s ease;
  position: relative;
}

.progress-milestone {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 5;
}

.progress-milestone.reached {
  background: #ffd700;
  color: #333;
  transform: translate(-50%, -50%) scale(1.2);
}

.milestone-tooltip {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 10;
}

.tooltip-title {
  font-weight: 600;
  margin-bottom: 2px;
}

.tooltip-reward {
  opacity: 0.8;
}

.progress-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

/* 好感度动作 */
.favorability-actions {
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
}

.actions-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 16px;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 12px;
}

.action-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.action-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.action-item.affordable {
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.action-item:not(.affordable) {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-item.recommended {
  border: 2px solid #ffd700;
  background: rgba(255, 215, 0, 0.1);
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.action-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 12px;
}

.action-info {
  flex: 1;
}

.action-name {
  font-size: 14px;
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.action-description {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 6px;
}

.action-reward {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #ff69b4;
  font-weight: 500;
}

.action-cost {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #ffd700;
  font-weight: 600;
}

.recommended-badge,
.limited-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 600;
  color: white;
}

.recommended-badge {
  background: #ffd700;
  color: #333;
}

.limited-badge {
  background: #ff6b6b;
}

/* 历史记录 */
.favorability-history {
  max-height: 200px;
  overflow-y: auto;
  background: rgba(0, 0, 0, 0.1);
}

.history-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.history-title span {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: white;
}

.toggle-history {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.toggle-history:hover {
  background: rgba(255, 255, 255, 0.1);
}

.history-list {
  padding: 0 20px 16px;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.history-item:last-child {
  border-bottom: none;
}

.history-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
}

.history-content {
  flex: 1;
}

.history-action {
  font-size: 13px;
  color: white;
  margin-bottom: 2px;
}

.history-time {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
}

.history-points {
  font-size: 14px;
  font-weight: 600;
}

.history-points.positive {
  color: #4ade80;
}

.history-points.negative {
  color: #f87171;
}

/* 等级奖励 */
.level-rewards {
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
}

.rewards-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 16px;
}

.rewards-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.reward-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
}

.reward-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.reward-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.reward-info {
  flex: 1;
}

.reward-name {
  font-size: 14px;
  font-weight: 600;
  color: white;
  margin-bottom: 2px;
}

.reward-description {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

/* 底部操作 */
.drawer-footer {
  padding: 16px 20px;
  background: rgba(0, 0, 0, 0.2);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.footer-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

.footer-actions {
  display: flex;
  gap: 12px;
}

.history-toggle,
.quick-boost-btn {
  flex: 1;
  height: 44px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.history-toggle {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.history-toggle:hover {
  background: rgba(255, 255, 255, 0.3);
}

.quick-boost-btn {
  background: linear-gradient(135deg, #ffd700, #ffb347);
  color: #333;
}

.quick-boost-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(255, 215, 0, 0.4);
}

.quick-boost-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* 提升效果 */
.boost-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
  z-index: 100;
}

.boost-text {
  margin-top: 20px;
  animation: boost-float 2s ease-out;
}

.boost-amount {
  font-size: 36px;
  font-weight: 700;
  color: #ff69b4;
  text-shadow: 0 0 20px rgba(255, 105, 180, 0.8);
}

.boost-label {
  font-size: 14px;
  color: white;
  opacity: 0.8;
}

@keyframes boost-float {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-50px);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .favorability-drawer {
    max-height: 90vh;
  }
  
  .level-display {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .character-avatar {
    width: 60px;
    height: 60px;
  }
  
  .action-grid {
    grid-template-columns: 1fr;
  }
  
  .action-item {
    padding: 12px;
  }
  
  .footer-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .drawer-header {
    padding: 12px 16px;
  }
  
  .level-display {
    padding: 16px;
  }
  
  .favorability-actions {
    padding: 16px;
  }
  
  .drawer-footer {
    padding: 12px 16px;
  }
  
  .footer-stats {
    flex-direction: column;
    gap: 8px;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .action-item {
    border: 1px solid rgba(255, 255, 255, 0.5);
  }
  
  .action-item.recommended {
    border-width: 2px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .favorability-drawer-overlay,
  .favorability-drawer,
  .action-item,
  .quick-boost-btn {
    transition: none;
  }
  
  .boost-effect {
    animation: none;
  }
}
</style>