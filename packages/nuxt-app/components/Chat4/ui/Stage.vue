<template>
  <div class="stage-container" :class="{ 'interactive': interactive, 'animated': showAnimations }">
    <!-- 舞台背景 -->
    <div class="stage-background" :style="{ backgroundImage: backgroundGradient }">
      <div class="stage-lighting" :class="`lighting-${stageData.mood}`"></div>
      <div class="stage-particles" v-if="showParticles">
        <HeartParticles
          :auto-emit="true"
          :particle-count="particleCount"
          :emit-rate="particleEmitRate"
          :color="stageData.primaryColor || '#ff69b4'"
          :particle-type="particleType"
          :custom-emojis="stageEmojis"
        />
      </div>
    </div>
    
    <!-- 舞台主体 -->
    <div class="stage-main">
      <!-- 角色展示区 -->
      <div class="character-area">
        <div 
          v-for="character in displayedCharacters" 
          :key="character.id"
          class="character-slot"
          :class="[
            `slot-${character.position}`,
            { 'active': character.active, 'highlighted': character.highlighted }
          ]"
          :style="getCharacterStyle(character)"
          @click="selectCharacter(character)"
        >
          <!-- 角色头像 -->
          <div class="character-avatar">
            <img 
              v-if="character.avatar" 
              :src="character.avatar" 
              :alt="character.name"
              class="avatar-image"
            />
            <div v-else class="avatar-placeholder">
              {{ character.name?.charAt(0) || '?' }}
            </div>
            
            <!-- 状态指示器 -->
            <div class="character-status" :class="`status-${character.status}`">
              <Icon :name="getStatusIcon(character.status)" />
            </div>
            
            <!-- 等级徽章 -->
            <div class="character-level" v-if="character.level">
              <span>{{ character.level }}</span>
            </div>
          </div>
          
          <!-- 角色信息 -->
          <div class="character-info">
            <div class="character-name">{{ character.name }}</div>
            <div class="character-role" v-if="character.role">{{ character.role }}</div>
            
            <!-- 好感度条 -->
            <div class="favorability-bar" v-if="character.favorability !== undefined">
              <div class="bar-background">
                <div 
                  class="bar-fill" 
                  :style="{ 
                    width: `${character.favorability}%`,
                    background: getFavorabilityGradient(character.favorability)
                  }"
                ></div>
              </div>
              <div class="favorability-value">{{ character.favorability }}%</div>
            </div>
          </div>
          
          <!-- 交互按钮 -->
          <div class="character-actions" v-if="character.active && showActions">
            <button 
              class="action-btn"
              @click.stop="interactWithCharacter(character, 'talk')"
              :disabled="!character.canTalk"
            >
              <Icon name="lucide:message-circle" />
            </button>
            
            <button 
              class="action-btn"
              @click.stop="interactWithCharacter(character, 'gift')"
              :disabled="!character.canGift"
            >
              <Icon name="lucide:gift" />
            </button>
            
            <button 
              class="action-btn"
              @click.stop="interactWithCharacter(character, 'touch')"
              :disabled="!character.canTouch"
            >
              <Icon name="lucide:heart" />
            </button>
          </div>
          
          <!-- 特殊效果 -->
          <div class="character-effects" v-if="character.effects">
            <div 
              v-for="effect in character.effects"
              :key="effect.id"
              class="effect"
              :class="`effect-${effect.type}`"
            >
              {{ effect.content }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 舞台中央装饰 -->
      <div class="stage-center" v-if="stageData.centerDecoration">
        <div class="center-decoration" :class="`decoration-${stageData.centerDecoration.type}`">
          <img 
            v-if="stageData.centerDecoration.image" 
            :src="stageData.centerDecoration.image" 
            :alt="stageData.centerDecoration.name"
          />
          <div v-else class="decoration-placeholder">
            <Icon :name="stageData.centerDecoration.icon || 'lucide:star'" />
          </div>
          <div class="decoration-name">{{ stageData.centerDecoration.name }}</div>
        </div>
      </div>
      
      <!-- 舞台道具 -->
      <div class="stage-props">
        <div 
          v-for="prop in stageData.props" 
          :key="prop.id"
          class="stage-prop"
          :class="`prop-${prop.type}`"
          :style="getPropStyle(prop)"
          @click="interactWithProp(prop)"
        >
          <img v-if="prop.image" :src="prop.image" :alt="prop.name" />
          <Icon v-else :name="prop.icon || 'lucide:box'" />
          <div class="prop-tooltip" v-if="prop.showTooltip">{{ prop.name }}</div>
        </div>
      </div>
    </div>
    
    <!-- 舞台信息栏 -->
    <div class="stage-info" v-if="showInfo">
      <div class="info-section">
        <div class="info-title">
          <Icon name="lucide:map" />
          <span>{{ stageData.name }}</span>
        </div>
        <div class="info-description">{{ stageData.description }}</div>
      </div>
      
      <div class="info-section" v-if="stageData.atmosphere">
        <div class="atmosphere-indicator" :class="`atmosphere-${stageData.atmosphere}`">
          <Icon :name="getAtmosphereIcon(stageData.atmosphere)" />
          <span>{{ getAtmosphereText(stageData.atmosphere) }}</span>
        </div>
      </div>
      
      <div class="info-section" v-if="stageData.timeOfDay">
        <div class="time-indicator">
          <Icon name="lucide:clock" />
          <span>{{ getTimeText(stageData.timeOfDay) }}</span>
        </div>
      </div>
    </div>
    
    <!-- 舞台控制面板 -->
    <div class="stage-controls" v-if="showControls">
      <div class="control-group">
        <button 
          class="control-btn"
          @click="toggleLighting"
          :class="{ 'active': lightingEnabled }"
        >
          <Icon name="lucide:lightbulb" />
          <span>灯光</span>
        </button>
        
        <button 
          class="control-btn"
          @click="toggleMusic"
          :class="{ 'active': musicEnabled }"
        >
          <Icon name="lucide:music" />
          <span>音乐</span>
        </button>
        
        <button 
          class="control-btn"
          @click="toggleEffects"
          :class="{ 'active': effectsEnabled }"
        >
          <Icon name="lucide:sparkles" />
          <span>特效</span>
        </button>
      </div>
      
      <div class="control-group">
        <button class="control-btn" @click="resetStage">
          <Icon name="lucide:refresh-cw" />
          <span>重置</span>
        </button>
        
        <button class="control-btn" @click="captureStage">
          <Icon name="lucide:camera" />
          <span>截图</span>
        </button>
      </div>
    </div>
    
    <!-- 音效指示器 -->
    <div class="sound-effects" v-if="activeSounds.length > 0">
      <div 
        v-for="sound in activeSounds"
        :key="sound.id"
        class="sound-indicator"
        :class="`sound-${sound.type}`"
      >
        <Icon :name="getSoundIcon(sound.type)" />
        <span>{{ sound.name }}</span>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div class="stage-loading" v-if="isLoading">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-text">{{ loadingText }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import HeartParticles from './HeartParticles.vue'

// 角色接口
interface Character {
  id: string
  name: string
  avatar?: string
  role?: string
  position: 'left' | 'center' | 'right' | 'far-left' | 'far-right'
  favorability?: number
  level?: number
  status: 'idle' | 'talking' | 'happy' | 'sad' | 'excited' | 'thinking'
  active?: boolean
  highlighted?: boolean
  canTalk?: boolean
  canGift?: boolean
  canTouch?: boolean
  effects?: CharacterEffect[]
  transform?: {
    x?: number
    y?: number
    scale?: number
    rotation?: number
  }
}

// 角色特效接口
interface CharacterEffect {
  id: string
  type: 'speech' | 'emotion' | 'action' | 'status'
  content: string
  duration?: number
}

// 道具接口
interface StageProp {
  id: string
  name: string
  type: 'decoration' | 'interactive' | 'furniture' | 'plant' | 'light'
  image?: string
  icon?: string
  position: {
    x: number
    y: number
  }
  scale?: number
  interactive?: boolean
  showTooltip?: boolean
}

// 舞台数据接口
interface StageData {
  name: string
  description: string
  mood: 'romantic' | 'cheerful' | 'calm' | 'energetic' | 'mysterious'
  atmosphere: 'bright' | 'dim' | 'colorful' | 'warm' | 'cool'
  timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night'
  primaryColor?: string
  secondaryColor?: string
  backgroundImage?: string
  centerDecoration?: {
    type: 'fountain' | 'statue' | 'tree' | 'stage' | 'altar'
    name: string
    image?: string
    icon?: string
  }
  props: StageProp[]
}

// 音效接口
interface SoundEffect {
  id: string
  type: 'ambient' | 'music' | 'sfx' | 'voice'
  name: string
}

interface Props {
  stageData: StageData
  characters: Character[]
  interactive?: boolean
  showAnimations?: boolean
  showParticles?: boolean
  showActions?: boolean
  showInfo?: boolean
  showControls?: boolean
  maxCharacters?: number
  loadingText?: string
}

const props = withDefaults(defineProps<Props>(), {
  interactive: true,
  showAnimations: true,
  showParticles: true,
  showActions: true,
  showInfo: true,
  showControls: false,
  maxCharacters: 5,
  loadingText: '加载中...'
})

// Emits
const emit = defineEmits<{
  'character-select': [character: Character]
  'character-interact': [character: Character, action: string]
  'prop-interact': [prop: StageProp]
  'lighting-toggle': [enabled: boolean]
  'music-toggle': [enabled: boolean]
  'effects-toggle': [enabled: boolean]
  'stage-reset': []
  'stage-capture': []
}>()

// 响应式数据
const lightingEnabled = ref(true)
const musicEnabled = ref(false)
const effectsEnabled = ref(true)
const isLoading = ref(false)
const activeSounds = ref<SoundEffect[]>([])

// 计算属性
const displayedCharacters = computed(() => {
  return props.characters.slice(0, props.maxCharacters)
})

const backgroundGradient = computed(() => {
  if (props.stageData.backgroundImage) {
    return `url(${props.stageData.backgroundImage})`
  }
  
  const primary = props.stageData.primaryColor || '#667eea'
  const secondary = props.stageData.secondaryColor || '#764ba2'
  return `linear-gradient(135deg, ${primary}, ${secondary})`
})

const particleCount = computed(() => {
  const moodMap = {
    romantic: 20,
    cheerful: 15,
    calm: 5,
    energetic: 25,
    mysterious: 8
  }
  return moodMap[props.stageData.mood] || 10
})

const particleEmitRate = computed(() => {
  const moodMap = {
    romantic: 200,
    cheerful: 150,
    calm: 500,
    energetic: 100,
    mysterious: 300
  }
  return moodMap[props.stageData.mood] || 200
})

const particleType = computed(() => {
  return props.stageData.mood === 'romantic' ? 'emoji' : 'heart'
})

const stageEmojis = computed(() => {
  const emojiMap = {
    romantic: ['💕', '💖', '💗', '💘'],
    cheerful: ['✨', '🌟', '⭐', '💫'],
    calm: ['🌸', '🍃', '🌿', '🦋'],
    energetic: ['🎉', '🎊', '🔥', '⚡'],
    mysterious: ['🌙', '⭐', '✨', '🔮']
  }
  return emojiMap[props.stageData.mood] || ['✨']
})

// 方法
const getCharacterStyle = (character: Character) => {
  const style: any = {}
  
  if (character.transform) {
    const transforms = []
    if (character.transform.x !== undefined || character.transform.y !== undefined) {
      transforms.push(`translate(${character.transform.x || 0}px, ${character.transform.y || 0}px)`)
    }
    if (character.transform.scale !== undefined) {
      transforms.push(`scale(${character.transform.scale})`)
    }
    if (character.transform.rotation !== undefined) {
      transforms.push(`rotate(${character.transform.rotation}deg)`)
    }
    if (transforms.length > 0) {
      style.transform = transforms.join(' ')
    }
  }
  
  return style
}

const getPropStyle = (prop: StageProp) => {
  return {
    left: `${prop.position.x}%`,
    top: `${prop.position.y}%`,
    transform: prop.scale ? `scale(${prop.scale})` : undefined
  }
}

const getStatusIcon = (status: string): string => {
  const iconMap = {
    idle: 'lucide:user',
    talking: 'lucide:message-circle',
    happy: 'lucide:smile',
    sad: 'lucide:frown',
    excited: 'lucide:zap',
    thinking: 'lucide:brain'
  }
  return iconMap[status] || 'lucide:user'
}

const getAtmosphereIcon = (atmosphere: string): string => {
  const iconMap = {
    bright: 'lucide:sun',
    dim: 'lucide:moon',
    colorful: 'lucide:palette',
    warm: 'lucide:flame',
    cool: 'lucide:snowflake'
  }
  return iconMap[atmosphere] || 'lucide:sun'
}

const getAtmosphereText = (atmosphere: string): string => {
  const textMap = {
    bright: '明亮',
    dim: '昏暗',
    colorful: '多彩',
    warm: '温暖',
    cool: '清爽'
  }
  return textMap[atmosphere] || '未知'
}

const getTimeText = (timeOfDay: string): string => {
  const textMap = {
    morning: '上午',
    afternoon: '下午',
    evening: '傍晚',
    night: '夜晚'
  }
  return textMap[timeOfDay] || '未知'
}

const getSoundIcon = (type: string): string => {
  const iconMap = {
    ambient: 'lucide:volume-2',
    music: 'lucide:music',
    sfx: 'lucide:zap',
    voice: 'lucide:mic'
  }
  return iconMap[type] || 'lucide:volume-2'
}

const getFavorabilityGradient = (value: number): string => {
  if (value >= 80) return 'linear-gradient(90deg, #10b981, #059669)'
  if (value >= 60) return 'linear-gradient(90deg, #f59e0b, #d97706)'
  if (value >= 40) return 'linear-gradient(90deg, #ef4444, #dc2626)'
  return 'linear-gradient(90deg, #6b7280, #4b5563)'
}

const selectCharacter = (character: Character) => {
  if (props.interactive) {
    emit('character-select', character)
  }
}

const interactWithCharacter = (character: Character, action: string) => {
  emit('character-interact', character, action)
  
  // 添加交互音效
  addSoundEffect({
    id: Date.now().toString(),
    type: 'sfx',
    name: `${action}音效`
  })
}

const interactWithProp = (prop: StageProp) => {
  if (prop.interactive) {
    emit('prop-interact', prop)
  }
}

const toggleLighting = () => {
  lightingEnabled.value = !lightingEnabled.value
  emit('lighting-toggle', lightingEnabled.value)
}

const toggleMusic = () => {
  musicEnabled.value = !musicEnabled.value
  emit('music-toggle', musicEnabled.value)
  
  if (musicEnabled.value) {
    addSoundEffect({
      id: 'bg-music',
      type: 'music',
      name: '背景音乐'
    })
  } else {
    removeSoundEffect('bg-music')
  }
}

const toggleEffects = () => {
  effectsEnabled.value = !effectsEnabled.value
  emit('effects-toggle', effectsEnabled.value)
}

const resetStage = () => {
  emit('stage-reset')
  activeSounds.value = []
}

const captureStage = () => {
  emit('stage-capture')
}

const addSoundEffect = (sound: SoundEffect) => {
  activeSounds.value.push(sound)
  
  // 自动移除音效
  if (sound.type === 'sfx') {
    setTimeout(() => {
      removeSoundEffect(sound.id)
    }, 2000)
  }
}

const removeSoundEffect = (soundId: string) => {
  const index = activeSounds.value.findIndex(s => s.id === soundId)
  if (index > -1) {
    activeSounds.value.splice(index, 1)
  }
}

// 监听器
watch(() => props.stageData.mood, () => {
  // 根据心情添加环境音效
  if (lightingEnabled.value) {
    addSoundEffect({
      id: 'ambient',
      type: 'ambient',
      name: '环境音效'
    })
  }
})

// 暴露方法给父组件
defineExpose({
  addCharacterEffect: (characterId: string, effect: CharacterEffect) => {
    const character = props.characters.find(c => c.id === characterId)
    if (character) {
      if (!character.effects) character.effects = []
      character.effects.push(effect)
      
      // 自动移除效果
      if (effect.duration) {
        setTimeout(() => {
          const effectIndex = character.effects!.findIndex(e => e.id === effect.id)
          if (effectIndex > -1) {
            character.effects!.splice(effectIndex, 1)
          }
        }, effect.duration)
      }
    }
  },
  removeCharacterEffect: (characterId: string, effectId: string) => {
    const character = props.characters.find(c => c.id === characterId)
    if (character && character.effects) {
      const index = character.effects.findIndex(e => e.id === effectId)
      if (index > -1) {
        character.effects.splice(index, 1)
      }
    }
  },
  addSoundEffect,
  removeSoundEffect,
  setLoading: (loading: boolean) => { isLoading.value = loading }
})
</script>

<style scoped>
.stage-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 400px;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.stage-container.interactive {
  cursor: pointer;
}

.stage-container.animated .character-slot {
  animation: characterEntrance 0.8s ease-out;
}

.stage-container.animated .stage-prop {
  animation: propFadeIn 1s ease-out;
}

/* 舞台背景 */
.stage-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.stage-lighting {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  transition: all 0.5s ease;
}

.lighting-romantic {
  background: radial-gradient(circle at 50% 30%, rgba(255, 182, 193, 0.3) 0%, transparent 70%);
}

.lighting-cheerful {
  background: radial-gradient(circle at 50% 30%, rgba(255, 223, 0, 0.2) 0%, transparent 70%);
}

.lighting-calm {
  background: radial-gradient(circle at 50% 30%, rgba(135, 206, 235, 0.2) 0%, transparent 70%);
}

.lighting-energetic {
  background: radial-gradient(circle at 50% 30%, rgba(255, 69, 0, 0.3) 0%, transparent 70%);
}

.lighting-mysterious {
  background: radial-gradient(circle at 50% 30%, rgba(75, 0, 130, 0.4) 0%, transparent 70%);
}

.stage-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

/* 舞台主体 */
.stage-main {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  z-index: 1;
}

/* 角色区域 */
.character-area {
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  flex: 1;
  position: relative;
  margin-bottom: 60px;
}

.character-slot {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
  max-width: 120px;
}

.character-slot:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-5px);
}

.character-slot.active {
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.character-slot.highlighted {
  animation: highlight-pulse 2s ease-in-out infinite;
}

/* 角色头像 */
.character-avatar {
  position: relative;
  width: 80px;
  height: 80px;
}

.avatar-image,
.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.avatar-image {
  object-fit: cover;
}

.avatar-placeholder {
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
  font-weight: 600;
}

.character-status {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  border: 2px solid white;
}

.status-idle { background: #6b7280; }
.status-talking { background: #3b82f6; }
.status-happy { background: #10b981; }
.status-sad { background: #ef4444; }
.status-excited { background: #f59e0b; }
.status-thinking { background: #8b5cf6; }

.character-level {
  position: absolute;
  bottom: -4px;
  right: -4px;
  background: linear-gradient(45deg, #ffd700, #ff8c00);
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 8px;
  border: 1px solid white;
}

/* 角色信息 */
.character-info {
  text-align: center;
  color: white;
}

.character-name {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.character-role {
  font-size: 11px;
  opacity: 0.8;
  margin-bottom: 8px;
}

.favorability-bar {
  display: flex;
  align-items: center;
  gap: 6px;
}

.bar-background {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.favorability-value {
  font-size: 10px;
  font-weight: 600;
  min-width: 30px;
}

/* 角色交互按钮 */
.character-actions {
  display: flex;
  gap: 6px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.character-slot:hover .character-actions {
  opacity: 1;
}

.action-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
}

.action-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 角色特效 */
.character-effects {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  gap: 4px;
  pointer-events: none;
}

.effect {
  padding: 4px 8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 12px;
  font-size: 11px;
  white-space: nowrap;
  animation: effectFadeIn 0.3s ease-out;
}

.effect-speech {
  background: rgba(59, 130, 246, 0.9);
}

.effect-emotion {
  background: rgba(239, 68, 68, 0.9);
}

.effect-action {
  background: rgba(16, 185, 129, 0.9);
}

/* 舞台中央装饰 */
.stage-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 0;
}

.center-decoration {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  text-align: center;
}

.center-decoration img,
.decoration-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
}

.decoration-placeholder {
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
}

.decoration-name {
  color: white;
  font-size: 12px;
  font-weight: 500;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

/* 舞台道具 */
.stage-props {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
}

.stage-prop {
  position: absolute;
  width: 40px;
  height: 40px;
  color: white;
  transition: all 0.3s ease;
  cursor: pointer;
  pointer-events: auto;
}

.stage-prop:hover {
  transform: scale(1.1);
}

.stage-prop img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.prop-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  margin-bottom: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stage-prop:hover .prop-tooltip {
  opacity: 1;
}

/* 舞台信息栏 */
.stage-info {
  position: absolute;
  top: 16px;
  left: 16px;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 12px 16px;
  color: white;
  max-width: 250px;
  z-index: 2;
}

.info-section {
  margin-bottom: 8px;
}

.info-section:last-child {
  margin-bottom: 0;
}

.info-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
}

.info-description {
  font-size: 12px;
  opacity: 0.8;
  line-height: 1.3;
}

.atmosphere-indicator,
.time-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

/* 舞台控制面板 */
.stage-controls {
  position: absolute;
  bottom: 16px;
  right: 16px;
  display: flex;
  gap: 12px;
  z-index: 2;
}

.control-group {
  display: flex;
  gap: 6px;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 8px;
}

.control-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.control-btn.active {
  background: rgba(59, 130, 246, 0.8);
  border-color: rgba(59, 130, 246, 0.8);
}

/* 音效指示器 */
.sound-effects {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  flex-direction: column;
  gap: 6px;
  z-index: 2;
}

.sound-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  color: white;
  font-size: 11px;
  animation: soundFadeIn 0.3s ease-out;
}

.sound-music {
  border: 1px solid #10b981;
  background: rgba(16, 185, 129, 0.2);
}

.sound-sfx {
  border: 1px solid #f59e0b;
  background: rgba(245, 158, 11, 0.2);
}

/* 加载状态 */
.stage-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: white;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 16px;
}

/* 动画定义 */
@keyframes characterEntrance {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes propFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes highlight-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(255, 255, 255, 0.6);
  }
}

@keyframes effectFadeIn {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes soundFadeIn {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stage-main {
    padding: 12px;
  }
  
  .character-area {
    margin-bottom: 40px;
  }
  
  .character-slot {
    max-width: 100px;
    padding: 12px 8px;
  }
  
  .character-avatar {
    width: 60px;
    height: 60px;
  }
  
  .stage-info {
    max-width: 200px;
    padding: 8px 12px;
  }
  
  .stage-controls {
    bottom: 12px;
    right: 12px;
  }
  
  .control-group {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .character-area {
    flex-wrap: wrap;
    justify-content: center;
    gap: 16px;
  }
  
  .character-slot {
    max-width: 80px;
  }
  
  .character-avatar {
    width: 50px;
    height: 50px;
  }
  
  .stage-info {
    position: static;
    margin-bottom: 16px;
    max-width: none;
  }
  
  .stage-controls {
    position: static;
    justify-content: center;
    margin-top: 16px;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .character-slot {
    border: 2px solid rgba(255, 255, 255, 0.5);
  }
  
  .character-slot.active {
    border-color: white;
  }
  
  .control-btn {
    border-width: 2px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .stage-container,
  .character-slot,
  .action-btn,
  .control-btn,
  .stage-prop {
    transition: none;
  }
  
  .stage-container.animated .character-slot,
  .stage-container.animated .stage-prop {
    animation: none;
  }
  
  .character-slot.highlighted {
    animation: none;
  }
  
  .loading-spinner {
    animation: none;
    border-top-color: transparent;
  }
}
</style>