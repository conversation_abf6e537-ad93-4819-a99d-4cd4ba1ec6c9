<template>
  <div class="stream-controls" :class="{ 'compact': isCompact, 'hidden': !visible }">
    <!-- 主控制面板 -->
    <div class="controls-main">
      <!-- 播放控制组 -->
      <div class="control-group play-controls">
        <button 
          class="control-btn primary"
          :class="{ 'playing': isPlaying }"
          @click="togglePlay"
          :disabled="isLoading"
        >
          <Icon v-if="isLoading" name="lucide:loader-2" class="animate-spin" />
          <Icon v-else :name="isPlaying ? 'lucide:pause' : 'lucide:play'" />
          <span v-if="!isCompact">{{ isPlaying ? '暂停' : '播放' }}</span>
        </button>
        
        <button 
          class="control-btn"
          @click="stop"
          :disabled="!canStop"
        >
          <Icon name="lucide:square" />
          <span v-if="!isCompact">停止</span>
        </button>
        
        <button 
          class="control-btn"
          @click="restart"
          :disabled="!canRestart"
        >
          <Icon name="lucide:rotate-ccw" />
          <span v-if="!isCompact">重播</span>
        </button>
      </div>
      
      <!-- 进度控制 -->
      <div class="control-group progress-controls" v-if="showProgress">
        <div class="time-display">
          <span class="current-time">{{ formatTime(currentTime) }}</span>
          <span class="time-separator">/</span>
          <span class="total-time">{{ formatTime(totalTime) }}</span>
        </div>
        
        <div class="progress-container">
          <input
            type="range"
            class="progress-slider"
            :value="progressPercentage"
            min="0"
            max="100"
            step="0.1"
            @input="seek"
            @mousedown="startSeeking"
            @mouseup="endSeeking"
            :disabled="!canSeek"
          />
          <div class="progress-track">
            <div class="progress-fill" :style="{ width: `${progressPercentage}%` }"></div>
            <div class="progress-buffer" :style="{ width: `${bufferPercentage}%` }"></div>
            <div 
              v-for="marker in progressMarkers" 
              :key="marker.id"
              class="progress-marker"
              :style="{ left: `${(marker.time / totalTime) * 100}%` }"
              :title="marker.label"
            ></div>
          </div>
        </div>
      </div>
      
      <!-- 音量控制 -->
      <div class="control-group volume-controls">
        <button 
          class="control-btn"
          @click="toggleMute"
        >
          <Icon :name="getVolumeIcon()" />
        </button>
        
        <div class="volume-container" v-if="!isCompact">
          <input
            type="range"
            class="volume-slider"
            v-model="volume"
            min="0"
            max="100"
            step="1"
            @input="updateVolume"
          />
          <span class="volume-display">{{ volume }}%</span>
        </div>
      </div>
      
      <!-- 速度控制 -->
      <div class="control-group speed-controls" v-if="showSpeedControl">
        <button 
          class="control-btn speed-btn"
          @click="toggleSpeedMenu"
          :class="{ 'active': showSpeedMenu }"
        >
          <span class="speed-value">{{ playbackSpeed }}x</span>
          <Icon name="lucide:chevron-up" />
        </button>
        
        <div class="speed-menu" v-if="showSpeedMenu">
          <button
            v-for="speed in speedOptions"
            :key="speed"
            class="speed-option"
            :class="{ 'active': playbackSpeed === speed }"
            @click="setSpeed(speed)"
          >
            {{ speed }}x
          </button>
        </div>
      </div>
      
      <!-- 质量控制 -->
      <div class="control-group quality-controls" v-if="showQualityControl">
        <button 
          class="control-btn quality-btn"
          @click="toggleQualityMenu"
          :class="{ 'active': showQualityMenu }"
        >
          <Icon name="lucide:settings" />
          <span v-if="!isCompact">{{ currentQuality }}</span>
          <Icon name="lucide:chevron-up" />
        </button>
        
        <div class="quality-menu" v-if="showQualityMenu">
          <button
            v-for="quality in qualityOptions"
            :key="quality.value"
            class="quality-option"
            :class="{ 'active': currentQuality === quality.label }"
            @click="setQuality(quality.value)"
          >
            {{ quality.label }}
          </button>
        </div>
      </div>
      
      <!-- 画面控制 -->
      <div class="control-group display-controls">
        <button 
          class="control-btn"
          @click="toggleFullscreen"
          v-if="showFullscreenControl"
        >
          <Icon :name="isFullscreen ? 'lucide:minimize' : 'lucide:maximize'" />
          <span v-if="!isCompact">{{ isFullscreen ? '退出全屏' : '全屏' }}</span>
        </button>
        
        <button 
          class="control-btn"
          @click="togglePictureInPicture"
          v-if="showPiPControl && supportsPiP"
        >
          <Icon name="lucide:picture-in-picture" />
          <span v-if="!isCompact">画中画</span>
        </button>
        
        <button 
          class="control-btn"
          @click="takeScreenshot"
          v-if="showScreenshotControl"
        >
          <Icon name="lucide:camera" />
          <span v-if="!isCompact">截图</span>
        </button>
      </div>
      
      <!-- 更多选项 -->
      <div class="control-group more-controls" v-if="showMoreControls">
        <button 
          class="control-btn"
          @click="toggleMoreMenu"
          :class="{ 'active': showMoreMenu }"
        >
          <Icon name="lucide:more-horizontal" />
        </button>
        
        <div class="more-menu" v-if="showMoreMenu">
          <button class="more-option" @click="shareStream" v-if="showShare">
            <Icon name="lucide:share" />
            <span>分享</span>
          </button>
          
          <button class="more-option" @click="reportIssue" v-if="showReport">
            <Icon name="lucide:flag" />
            <span>举报</span>
          </button>
          
          <button class="more-option" @click="downloadStream" v-if="showDownload">
            <Icon name="lucide:download" />
            <span>下载</span>
          </button>
          
          <button class="more-option" @click="openSettings" v-if="showSettings">
            <Icon name="lucide:settings" />
            <span>设置</span>
          </button>
        </div>
      </div>
    </div>
    
    <!-- 状态信息栏 -->
    <div class="controls-status" v-if="showStatus && !isCompact">
      <div class="status-item" v-if="streamInfo.viewers !== undefined">
        <Icon name="lucide:eye" />
        <span>{{ formatNumber(streamInfo.viewers) }} 观看</span>
      </div>
      
      <div class="status-item" v-if="streamInfo.likes !== undefined">
        <Icon name="lucide:heart" />
        <span>{{ formatNumber(streamInfo.likes) }} 点赞</span>
      </div>
      
      <div class="status-item connection-status" :class="`status-${connectionStatus}`">
        <div class="status-dot"></div>
        <span>{{ getConnectionText() }}</span>
      </div>
      
      <div class="status-item" v-if="showBitrate">
        <Icon name="lucide:activity" />
        <span>{{ currentBitrate }}</span>
      </div>
    </div>
    
    <!-- 键盘快捷键提示 -->
    <div class="keyboard-hints" v-if="showKeyboardHints && !isCompact">
      <div class="hint-item">
        <kbd>Space</kbd>
        <span>播放/暂停</span>
      </div>
      <div class="hint-item">
        <kbd>F</kbd>
        <span>全屏</span>
      </div>
      <div class="hint-item">
        <kbd>M</kbd>
        <span>静音</span>
      </div>
      <div class="hint-item">
        <kbd>↑↓</kbd>
        <span>音量</span>
      </div>
    </div>
    
    <!-- 加载指示器 -->
    <div class="loading-indicator" v-if="isBuffering">
      <div class="loading-spinner"></div>
      <span>缓冲中...</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

// 流信息接口
interface StreamInfo {
  viewers?: number
  likes?: number
  duration?: number
  isLive?: boolean
}

// 进度标记接口
interface ProgressMarker {
  id: string
  time: number
  label: string
}

// 质量选项接口
interface QualityOption {
  value: string
  label: string
  bitrate?: number
}

interface Props {
  visible?: boolean
  isCompact?: boolean
  isPlaying?: boolean
  isLoading?: boolean
  isBuffering?: boolean
  canStop?: boolean
  canRestart?: boolean
  canSeek?: boolean
  currentTime?: number
  totalTime?: number
  bufferPercentage?: number
  initialVolume?: number
  initialSpeed?: number
  streamInfo?: StreamInfo
  connectionStatus?: 'excellent' | 'good' | 'poor' | 'offline'
  currentBitrate?: string
  showProgress?: boolean
  showSpeedControl?: boolean
  showQualityControl?: boolean
  showFullscreenControl?: boolean
  showPiPControl?: boolean
  showScreenshotControl?: boolean
  showMoreControls?: boolean
  showStatus?: boolean
  showKeyboardHints?: boolean
  showShare?: boolean
  showReport?: boolean
  showDownload?: boolean
  showSettings?: boolean
  showBitrate?: boolean
  progressMarkers?: ProgressMarker[]
  qualityOptions?: QualityOption[]
}

const props = withDefaults(defineProps<Props>(), {
  visible: true,
  isCompact: false,
  isPlaying: false,
  isLoading: false,
  isBuffering: false,
  canStop: true,
  canRestart: true,
  canSeek: true,
  currentTime: 0,
  totalTime: 0,
  bufferPercentage: 0,
  initialVolume: 80,
  initialSpeed: 1,
  streamInfo: () => ({}),
  connectionStatus: 'good',
  currentBitrate: '1.2 Mbps',
  showProgress: true,
  showSpeedControl: true,
  showQualityControl: true,
  showFullscreenControl: true,
  showPiPControl: true,
  showScreenshotControl: true,
  showMoreControls: true,
  showStatus: true,
  showKeyboardHints: false,
  showShare: true,
  showReport: true,
  showDownload: false,
  showSettings: true,
  showBitrate: true,
  progressMarkers: () => [],
  qualityOptions: () => [
    { value: '1080p', label: '1080P' },
    { value: '720p', label: '720P' },
    { value: '480p', label: '480P' },
    { value: 'auto', label: '自动' }
  ]
})

// Emits
const emit = defineEmits<{
  'play': []
  'pause': []
  'stop': []
  'restart': []
  'seek': [time: number]
  'volume-change': [volume: number]
  'mute-toggle': [muted: boolean]
  'speed-change': [speed: number]
  'quality-change': [quality: string]
  'fullscreen-toggle': []
  'pip-toggle': []
  'screenshot': []
  'share': []
  'report': []
  'download': []
  'settings': []
}>()

// 响应式数据
const volume = ref(props.initialVolume)
const isMuted = ref(false)
const isFullscreen = ref(false)
const isSeeking = ref(false)
const playbackSpeed = ref(props.initialSpeed)
const currentQuality = ref('自动')
const showSpeedMenu = ref(false)
const showQualityMenu = ref(false)
const showMoreMenu = ref(false)

// 速度选项
const speedOptions = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2]

// 计算属性
const progressPercentage = computed(() => {
  if (props.totalTime === 0) return 0
  return (props.currentTime / props.totalTime) * 100
})

const supportsPiP = computed(() => {
  return 'pictureInPictureEnabled' in document
})

// 方法
const formatTime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}

const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const getVolumeIcon = (): string => {
  if (isMuted.value || volume.value === 0) return 'lucide:volume-x'
  if (volume.value < 50) return 'lucide:volume-1'
  return 'lucide:volume-2'
}

const getConnectionText = (): string => {
  const statusMap = {
    excellent: '连接优秀',
    good: '连接良好',
    poor: '连接较差',
    offline: '连接断开'
  }
  return statusMap[props.connectionStatus] || '未知状态'
}

const togglePlay = () => {
  if (props.isPlaying) {
    emit('pause')
  } else {
    emit('play')
  }
}

const stop = () => {
  emit('stop')
}

const restart = () => {
  emit('restart')
}

const seek = (event: Event) => {
  const target = event.target as HTMLInputElement
  const percentage = parseFloat(target.value)
  const time = (percentage / 100) * props.totalTime
  emit('seek', time)
}

const startSeeking = () => {
  isSeeking.value = true
}

const endSeeking = () => {
  isSeeking.value = false
}

const toggleMute = () => {
  isMuted.value = !isMuted.value
  emit('mute-toggle', isMuted.value)
}

const updateVolume = () => {
  emit('volume-change', volume.value)
  if (volume.value > 0 && isMuted.value) {
    isMuted.value = false
    emit('mute-toggle', false)
  }
}

const toggleSpeedMenu = () => {
  showSpeedMenu.value = !showSpeedMenu.value
  showQualityMenu.value = false
  showMoreMenu.value = false
}

const setSpeed = (speed: number) => {
  playbackSpeed.value = speed
  showSpeedMenu.value = false
  emit('speed-change', speed)
}

const toggleQualityMenu = () => {
  showQualityMenu.value = !showQualityMenu.value
  showSpeedMenu.value = false
  showMoreMenu.value = false
}

const setQuality = (quality: string) => {
  const option = props.qualityOptions.find(q => q.value === quality)
  if (option) {
    currentQuality.value = option.label
    showQualityMenu.value = false
    emit('quality-change', quality)
  }
}

const toggleMoreMenu = () => {
  showMoreMenu.value = !showMoreMenu.value
  showSpeedMenu.value = false
  showQualityMenu.value = false
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  emit('fullscreen-toggle')
}

const togglePictureInPicture = () => {
  emit('pip-toggle')
}

const takeScreenshot = () => {
  emit('screenshot')
}

const shareStream = () => {
  emit('share')
  showMoreMenu.value = false
}

const reportIssue = () => {
  emit('report')
  showMoreMenu.value = false
}

const downloadStream = () => {
  emit('download')
  showMoreMenu.value = false
}

const openSettings = () => {
  emit('settings')
  showMoreMenu.value = false
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
    return
  }
  
  switch (event.code) {
    case 'Space':
      event.preventDefault()
      togglePlay()
      break
    case 'KeyF':
      event.preventDefault()
      toggleFullscreen()
      break
    case 'KeyM':
      event.preventDefault()
      toggleMute()
      break
    case 'ArrowUp':
      event.preventDefault()
      volume.value = Math.min(100, volume.value + 5)
      updateVolume()
      break
    case 'ArrowDown':
      event.preventDefault()
      volume.value = Math.max(0, volume.value - 5)
      updateVolume()
      break
    case 'ArrowLeft':
      event.preventDefault()
      if (props.canSeek) {
        emit('seek', Math.max(0, props.currentTime - 10))
      }
      break
    case 'ArrowRight':
      event.preventDefault()
      if (props.canSeek) {
        emit('seek', Math.min(props.totalTime, props.currentTime + 10))
      }
      break
  }
}

// 点击外部关闭菜单
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.control-group')) {
    showSpeedMenu.value = false
    showQualityMenu.value = false
    showMoreMenu.value = false
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  document.removeEventListener('click', handleClickOutside)
})

// 监听器
watch(() => props.initialVolume, (newVolume) => {
  volume.value = newVolume
})

// 暴露方法给父组件
defineExpose({
  setVolume: (vol: number) => { volume.value = vol },
  setMuted: (muted: boolean) => { isMuted.value = muted },
  setSpeed: (speed: number) => { playbackSpeed.value = speed },
  setQuality: (quality: string) => setQuality(quality),
  getVolume: () => volume.value,
  getMuted: () => isMuted.value,
  getSpeed: () => playbackSpeed.value,
  closeMenus: () => {
    showSpeedMenu.value = false
    showQualityMenu.value = false
    showMoreMenu.value = false
  }
})
</script>

<style scoped>
.stream-controls {
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.8) 100%);
  padding: 16px 20px;
  border-radius: 0 0 12px 12px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
}

.stream-controls.hidden {
  opacity: 0;
  transform: translateY(100%);
}

.stream-controls.compact {
  padding: 8px 12px;
}

.stream-controls.compact .controls-main {
  gap: 8px;
}

.stream-controls.compact .control-btn {
  min-width: 36px;
  height: 36px;
  padding: 0;
}

/* 主控制面板 */
.controls-main {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
  margin-bottom: 12px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.control-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  min-width: 44px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  white-space: nowrap;
}

.control-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.control-btn.primary {
  background: linear-gradient(135deg, #10b981, #059669);
  border-color: rgba(16, 185, 129, 0.5);
}

.control-btn.primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669, #047857);
}

.control-btn.primary.playing {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  border-color: rgba(239, 68, 68, 0.5);
}

.control-btn.active {
  background: rgba(59, 130, 246, 0.8);
  border-color: rgba(59, 130, 246, 0.8);
}

/* 进度控制 */
.progress-controls {
  flex: 1;
  min-width: 200px;
}

.time-display {
  display: flex;
  align-items: center;
  gap: 4px;
  color: white;
  font-size: 12px;
  margin-bottom: 4px;
}

.time-separator {
  opacity: 0.6;
}

.progress-container {
  position: relative;
  width: 100%;
}

.progress-slider {
  width: 100%;
  height: 6px;
  background: transparent;
  appearance: none;
  cursor: pointer;
  position: relative;
  z-index: 2;
}

.progress-slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #10b981;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.progress-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #10b981;
  cursor: pointer;
  border: 2px solid white;
}

.progress-track {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  transform: translateY(-50%);
  pointer-events: none;
  z-index: 1;
}

.progress-fill {
  height: 100%;
  background: #10b981;
  border-radius: 3px;
  transition: width 0.1s ease;
}

.progress-buffer {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-marker {
  position: absolute;
  top: 50%;
  width: 3px;
  height: 12px;
  background: #ffd700;
  transform: translate(-50%, -50%);
  border-radius: 1px;
}

/* 音量控制 */
.volume-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.volume-slider {
  width: 80px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  appearance: none;
  border-radius: 2px;
  cursor: pointer;
}

.volume-slider::-webkit-slider-thumb {
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: white;
  cursor: pointer;
}

.volume-display {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  min-width: 30px;
  text-align: right;
}

/* 速度控制 */
.speed-btn {
  min-width: 60px;
  justify-content: space-between;
}

.speed-value {
  font-weight: 600;
}

.speed-menu,
.quality-menu,
.more-menu {
  position: absolute;
  bottom: 100%;
  left: 0;
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 4px;
  margin-bottom: 8px;
  backdrop-filter: blur(10px);
  z-index: 10;
  min-width: 100px;
}

.speed-option,
.quality-option,
.more-option {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: none;
  color: white;
  font-size: 14px;
  cursor: pointer;
  border-radius: 4px;
  transition: background 0.2s ease;
  white-space: nowrap;
}

.speed-option:hover,
.quality-option:hover,
.more-option:hover {
  background: rgba(255, 255, 255, 0.1);
}

.speed-option.active,
.quality-option.active {
  background: rgba(59, 130, 246, 0.8);
}

/* 质量控制 */
.quality-btn {
  min-width: 80px;
  justify-content: space-between;
}

/* 状态信息栏 */
.controls-status {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 12px;
  margin-top: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.connection-status .status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 4px;
}

.status-excellent .status-dot {
  background: #10b981;
  animation: pulse 2s ease-in-out infinite;
}

.status-good .status-dot {
  background: #f59e0b;
}

.status-poor .status-dot {
  background: #ef4444;
}

.status-offline .status-dot {
  background: #6b7280;
}

/* 键盘快捷键提示 */
.keyboard-hints {
  display: flex;
  gap: 12px;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 8px;
  flex-wrap: wrap;
}

.hint-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

kbd {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 10px;
  font-family: monospace;
}

/* 加载指示器 */
.loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(0, 0, 0, 0.8);
  padding: 8px 16px;
  border-radius: 20px;
  color: white;
  font-size: 14px;
  backdrop-filter: blur(10px);
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stream-controls {
    padding: 12px 16px;
  }
  
  .controls-main {
    gap: 12px;
  }
  
  .control-group {
    gap: 6px;
  }
  
  .control-btn {
    padding: 6px 10px;
    min-width: 40px;
    height: 36px;
    font-size: 12px;
  }
  
  .progress-controls {
    order: -1;
    width: 100%;
    margin-bottom: 8px;
  }
  
  .volume-container {
    display: none;
  }
  
  .keyboard-hints {
    display: none;
  }
  
  .controls-status {
    flex-wrap: wrap;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .stream-controls {
    padding: 8px 12px;
  }
  
  .control-btn span {
    display: none;
  }
  
  .control-btn {
    min-width: 36px;
    height: 32px;
    padding: 0;
    justify-content: center;
  }
  
  .time-display {
    justify-content: center;
  }
  
  .controls-status {
    justify-content: center;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .control-btn {
    border-width: 2px;
  }
  
  .progress-track {
    background: rgba(255, 255, 255, 0.5);
  }
  
  .speed-menu,
  .quality-menu,
  .more-menu {
    border-width: 2px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .stream-controls,
  .control-btn {
    transition: none;
  }
  
  .animate-spin,
  .loading-spinner {
    animation: none;
  }
  
  .status-excellent .status-dot {
    animation: none;
  }
  
  .progress-fill,
  .progress-buffer {
    transition: none;
  }
}
</style>