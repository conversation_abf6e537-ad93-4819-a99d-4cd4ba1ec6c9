<template>
  <div class="chat4-loading" :class="{ 'fullscreen': fullscreen, 'overlay': overlay }">
    <!-- 背景层 -->
    <div class="loading-background" v-if="showBackground">
      <div class="gradient-overlay"></div>
      <div class="animated-bg"></div>
    </div>
    
    <!-- 主要内容 -->
    <div class="loading-content">
      <!-- Logo 或图标 -->
      <div class="loading-logo" v-if="showLogo">
        <div class="logo-container">
          <img v-if="logoUrl" :src="logoUrl" :alt="logoAlt" class="logo-image" />
          <div v-else class="logo-fallback">
            <Icon name="lucide:heart" />
          </div>
        </div>
        
        <!-- 脉冲效果 -->
        <div class="pulse-rings">
          <div class="pulse-ring ring-1"></div>
          <div class="pulse-ring ring-2"></div>
          <div class="pulse-ring ring-3"></div>
        </div>
      </div>
      
      <!-- 加载动画 -->
      <div class="loading-animation">
        <!-- 心跳动画 -->
        <div class="heart-animation" v-if="animationType === 'heart'">
          <div class="floating-hearts">
            <div 
              v-for="i in heartCount" 
              :key="`heart-${i}`"
              class="floating-heart"
              :style="{ 
                animationDelay: `${i * 0.5}s`,
                left: `${Math.random() * 80 + 10}%`
              }"
            >
              💖
            </div>
          </div>
        </div>
        
        <!-- 圆点动画 -->
        <div class="dots-animation" v-else-if="animationType === 'dots'">
          <div class="loading-dots">
            <div 
              v-for="i in 3" 
              :key="`dot-${i}`"
              class="loading-dot"
              :style="{ animationDelay: `${(i - 1) * 0.2}s` }"
            ></div>
          </div>
        </div>
        
        <!-- 波浪动画 -->
        <div class="wave-animation" v-else-if="animationType === 'wave'">
          <div class="wave-container">
            <div 
              v-for="i in 4" 
              :key="`wave-${i}`"
              class="wave-bar"
              :style="{ animationDelay: `${(i - 1) * 0.1}s` }"
            ></div>
          </div>
        </div>
        
        <!-- 旋转动画 -->
        <div class="spinner-animation" v-else-if="animationType === 'spinner'">
          <div class="loading-spinner">
            <div class="spinner-inner"></div>
          </div>
        </div>
        
        <!-- 进度条动画 -->
        <div class="progress-animation" v-else-if="animationType === 'progress'">
          <div class="progress-container">
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: `${progress}%` }"
              ></div>
              <div class="progress-glow"></div>
            </div>
            <div class="progress-text">{{ Math.round(progress) }}%</div>
          </div>
        </div>
      </div>
      
      <!-- 加载文本 -->
      <div class="loading-text" v-if="showText">
        <div class="main-text">{{ currentText }}</div>
        <div class="sub-text" v-if="subText">{{ subText }}</div>
        
        <!-- 打字机效果 -->
        <div class="typing-animation" v-if="showTyping">
          <span class="typing-cursor">|</span>
        </div>
      </div>
      
      <!-- 提示信息 -->
      <div class="loading-tips" v-if="showTips && currentTip">
        <div class="tip-icon">
          <Icon name="lucide:lightbulb" />
        </div>
        <div class="tip-text">{{ currentTip }}</div>
      </div>
      
      <!-- 加载阶段指示器 -->
      <div class="loading-stages" v-if="showStages && stages.length > 0">
        <div 
          v-for="(stage, index) in stages"
          :key="`stage-${index}`"
          class="stage-item"
          :class="{ 
            'completed': index < currentStage,
            'active': index === currentStage,
            'pending': index > currentStage
          }"
        >
          <div class="stage-icon">
            <Icon v-if="index < currentStage" name="lucide:check" />
            <Icon v-else-if="index === currentStage" name="lucide:loader-2" class="animate-spin" />
            <span v-else>{{ index + 1 }}</span>
          </div>
          <div class="stage-label">{{ stage.label }}</div>
        </div>
      </div>
      
      <!-- 取消按钮 -->
      <div class="loading-actions" v-if="showCancel">
        <button class="cancel-btn" @click="handleCancel" :disabled="!cancellable">
          <Icon name="lucide:x" />
          <span>取消</span>
        </button>
      </div>
    </div>
    
    <!-- 粒子效果 -->
    <div class="particle-effects" v-if="showParticles">
      <div 
        v-for="i in particleCount" 
        :key="`particle-${i}`"
        class="particle"
        :style="{
          left: `${Math.random() * 100}%`,
          animationDelay: `${Math.random() * 3}s`,
          animationDuration: `${3 + Math.random() * 2}s`
        }"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

// 加载阶段接口
interface LoadingStage {
  label: string
  duration?: number
}

interface Props {
  visible?: boolean
  fullscreen?: boolean
  overlay?: boolean
  animationType?: 'heart' | 'dots' | 'wave' | 'spinner' | 'progress'
  text?: string
  subText?: string
  showLogo?: boolean
  showText?: boolean
  showTips?: boolean
  showStages?: boolean
  showCancel?: boolean
  showBackground?: boolean
  showParticles?: boolean
  showTyping?: boolean
  logoUrl?: string
  logoAlt?: string
  cancellable?: boolean
  progress?: number
  stages?: LoadingStage[]
  tips?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  visible: true,
  fullscreen: false,
  overlay: false,
  animationType: 'heart',
  text: '加载中...',
  subText: '',
  showLogo: true,
  showText: true,
  showTips: true,
  showStages: false,
  showCancel: false,
  showBackground: true,
  showParticles: true,
  showTyping: false,
  logoAlt: 'Logo',
  cancellable: true,
  progress: 0,
  stages: () => [],
  tips: () => [
    '正在连接服务器...',
    '加载用户数据...',
    '准备聊天环境...',
    '初始化界面...',
    '即将完成...'
  ]
})

// Emits
const emit = defineEmits<{
  'cancel': []
  'stage-change': [stage: number]
  'complete': []
}>()

// 响应式数据
const currentText = ref(props.text)
const currentTip = ref('')
const currentStage = ref(0)
const heartCount = ref(8)
const particleCount = ref(20)

// 定时器
let textCycleTimer: NodeJS.Timeout | null = null
let tipCycleTimer: NodeJS.Timeout | null = null
let stageTimer: NodeJS.Timeout | null = null

// 计算属性
const loadingTexts = computed(() => [
  '正在加载精彩内容...',
  '准备最佳体验...',
  '连接中...',
  '初始化完成',
  props.text
])

// 方法
const startTextCycle = () => {
  if (!props.showText) return
  
  let index = 0
  textCycleTimer = setInterval(() => {
    currentText.value = loadingTexts.value[index]
    index = (index + 1) % loadingTexts.value.length
  }, 2000)
}

const startTipCycle = () => {
  if (!props.showTips || props.tips.length === 0) return
  
  let index = 0
  currentTip.value = props.tips[index]
  
  tipCycleTimer = setInterval(() => {
    index = (index + 1) % props.tips.length
    currentTip.value = props.tips[index]
  }, 3000)
}

const startStageProgress = () => {
  if (!props.showStages || props.stages.length === 0) return
  
  const processStage = (stageIndex: number) => {
    if (stageIndex >= props.stages.length) {
      emit('complete')
      return
    }
    
    currentStage.value = stageIndex
    emit('stage-change', stageIndex)
    
    const stage = props.stages[stageIndex]
    const duration = stage.duration || 2000
    
    stageTimer = setTimeout(() => {
      processStage(stageIndex + 1)
    }, duration)
  }
  
  processStage(0)
}

const handleCancel = () => {
  if (props.cancellable) {
    emit('cancel')
  }
}

const cleanup = () => {
  if (textCycleTimer) {
    clearInterval(textCycleTimer)
    textCycleTimer = null
  }
  
  if (tipCycleTimer) {
    clearInterval(tipCycleTimer)
    tipCycleTimer = null
  }
  
  if (stageTimer) {
    clearTimeout(stageTimer)
    stageTimer = null
  }
}

// 生命周期
onMounted(() => {
  startTextCycle()
  startTipCycle()
  startStageProgress()
})

onUnmounted(() => {
  cleanup()
})

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    startTextCycle()
    startTipCycle()
    startStageProgress()
  } else {
    cleanup()
  }
})

watch(() => props.tips, () => {
  if (tipCycleTimer) {
    clearInterval(tipCycleTimer)
  }
  startTipCycle()
}, { deep: true })

// 暴露方法给父组件
defineExpose({
  setStage: (stage: number) => { currentStage.value = stage },
  setText: (text: string) => { currentText.value = text },
  setTip: (tip: string) => { currentTip.value = tip },
  nextStage: () => {
    if (currentStage.value < props.stages.length - 1) {
      currentStage.value++
      emit('stage-change', currentStage.value)
    }
  },
  complete: () => { emit('complete') }
})
</script>

<style scoped>
.chat4-loading {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.chat4-loading.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  min-height: 100vh;
}

.chat4-loading.overlay {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
}

/* 背景层 */
.loading-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.8) 0%,
    rgba(118, 75, 162, 0.8) 50%,
    rgba(255, 105, 180, 0.8) 100%
  );
}

.animated-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at 50% 50%,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 70%
  );
  animation: breathe 4s ease-in-out infinite;
}

/* 主要内容 */
.loading-content {
  position: relative;
  text-align: center;
  color: white;
  z-index: 10;
  max-width: 400px;
  padding: 40px 20px;
}

/* Logo */
.loading-logo {
  position: relative;
  margin-bottom: 32px;
}

.logo-container {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto;
}

.logo-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 50%;
  animation: logoSpin 3s linear infinite;
}

.logo-fallback {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #ff6b9d, #ff8cc8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  animation: logoSpin 3s linear infinite;
}

.pulse-rings {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2px solid rgba(255, 105, 180, 0.6);
  border-radius: 50%;
  animation: pulse 2s ease-out infinite;
}

.ring-1 {
  width: 100px;
  height: 100px;
  animation-delay: 0s;
}

.ring-2 {
  width: 130px;
  height: 130px;
  animation-delay: 0.7s;
}

.ring-3 {
  width: 160px;
  height: 160px;
  animation-delay: 1.4s;
}

/* 加载动画 */
.loading-animation {
  margin-bottom: 24px;
}

/* 心跳动画 */
.heart-animation {
  height: 60px;
  position: relative;
  overflow: hidden;
}

.floating-hearts {
  position: relative;
  width: 100%;
  height: 100%;
}

.floating-heart {
  position: absolute;
  font-size: 24px;
  animation: floatUp 3s ease-in-out infinite;
  opacity: 0;
}

/* 圆点动画 */
.dots-animation {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-dots {
  display: flex;
  gap: 8px;
}

.loading-dot {
  width: 12px;
  height: 12px;
  background: #ff69b4;
  border-radius: 50%;
  animation: dotBounce 1.5s ease-in-out infinite;
}

/* 波浪动画 */
.wave-animation {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wave-container {
  display: flex;
  gap: 4px;
  align-items: end;
}

.wave-bar {
  width: 6px;
  background: #ff69b4;
  border-radius: 3px;
  animation: waveGrow 1.2s ease-in-out infinite;
}

/* 旋转动画 */
.spinner-animation {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  position: relative;
}

.spinner-inner {
  width: 100%;
  height: 100%;
  border: 4px solid rgba(255, 105, 180, 0.3);
  border-top: 4px solid #ff69b4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 进度条动画 */
.progress-animation {
  margin-bottom: 16px;
}

.progress-container {
  width: 100%;
}

.progress-bar {
  position: relative;
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff6b9d, #ff8cc8);
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  animation: progressGlow 2s ease-in-out infinite;
}

.progress-text {
  font-size: 14px;
  opacity: 0.8;
}

/* 加载文本 */
.loading-text {
  margin-bottom: 20px;
}

.main-text {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
  animation: textFade 2s ease-in-out infinite;
}

.sub-text {
  font-size: 14px;
  opacity: 0.8;
}

.typing-animation {
  display: inline-flex;
  align-items: center;
  margin-left: 4px;
}

.typing-cursor {
  font-size: 18px;
  animation: blink 1s step-end infinite;
}

/* 提示信息 */
.loading-tips {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
}

.tip-icon {
  font-size: 16px;
  opacity: 0.8;
}

.tip-text {
  font-size: 14px;
  opacity: 0.9;
  animation: tipFade 3s ease-in-out infinite;
}

/* 加载阶段 */
.loading-stages {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.stage-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.stage-item.completed {
  background: rgba(16, 185, 129, 0.2);
}

.stage-item.active {
  background: rgba(255, 105, 180, 0.2);
  animation: stagePulse 1.5s ease-in-out infinite;
}

.stage-item.pending {
  background: rgba(255, 255, 255, 0.1);
  opacity: 0.6;
}

.stage-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.stage-item.completed .stage-icon {
  background: #10b981;
  color: white;
}

.stage-item.active .stage-icon {
  background: #ff69b4;
  color: white;
}

.stage-item.pending .stage-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.stage-label {
  font-size: 14px;
  flex: 1;
}

/* 取消按钮 */
.loading-actions {
  margin-top: 20px;
}

.cancel-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.cancel-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.cancel-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 粒子效果 */
.particle-effects {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 105, 180, 0.6);
  border-radius: 50%;
  animation: particleFloat 5s linear infinite;
}

/* 动画定义 */
@keyframes breathe {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.5; }
}

@keyframes logoSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0% { transform: translate(-50%, -50%) scale(0.8); opacity: 1; }
  100% { transform: translate(-50%, -50%) scale(1.5); opacity: 0; }
}

@keyframes floatUp {
  0% { transform: translateY(0); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(-60px); opacity: 0; }
}

@keyframes dotBounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-20px); }
  60% { transform: translateY(-10px); }
}

@keyframes waveGrow {
  0%, 100% { height: 10px; }
  25% { height: 30px; }
  50% { height: 20px; }
  75% { height: 35px; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes progressGlow {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes textFade {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

@keyframes tipFade {
  0%, 20%, 80%, 100% { opacity: 0.9; }
  40%, 60% { opacity: 0.6; }
}

@keyframes stagePulse {
  0%, 100% { background: rgba(255, 105, 180, 0.2); }
  50% { background: rgba(255, 105, 180, 0.4); }
}

@keyframes particleFloat {
  0% { transform: translateY(100vh) translateX(0) rotate(0deg); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(-20px) translateX(20px) rotate(360deg); opacity: 0; }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-content {
    padding: 20px 16px;
    max-width: 300px;
  }
  
  .logo-container {
    width: 60px;
    height: 60px;
  }
  
  .logo-fallback {
    font-size: 24px;
  }
  
  .main-text {
    font-size: 16px;
  }
  
  .loading-stages {
    gap: 8px;
  }
  
  .stage-item {
    padding: 6px 10px;
  }
}

@media (max-width: 480px) {
  .loading-content {
    padding: 16px 12px;
  }
  
  .floating-heart {
    font-size: 18px;
  }
  
  .loading-tips {
    padding: 8px 12px;
    margin-bottom: 16px;
  }
  
  .tip-text {
    font-size: 12px;
  }
  
  .stage-label {
    font-size: 12px;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .loading-dots .loading-dot,
  .wave-bar,
  .progress-fill {
    background: #ffffff;
  }
  
  .cancel-btn {
    border-width: 2px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .animated-bg,
  .logo-image,
  .logo-fallback,
  .pulse-ring,
  .floating-heart,
  .loading-dot,
  .wave-bar,
  .spinner-inner,
  .progress-glow,
  .main-text,
  .typing-cursor,
  .tip-text,
  .stage-item.active,
  .particle {
    animation: none;
  }
  
  .progress-fill {
    transition: none;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .loading-tips {
    background: rgba(0, 0, 0, 0.3);
  }
  
  .cancel-btn {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.3);
  }
}
</style>