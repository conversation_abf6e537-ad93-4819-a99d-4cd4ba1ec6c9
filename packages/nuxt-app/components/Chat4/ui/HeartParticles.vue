<template>
  <div 
    ref="containerRef" 
    class="heart-particles" 
    :class="{ 'auto-emit': autoEmit }"
    @click="handleClick"
  >
    <!-- 心形粒子容器 -->
    <div class="particles-container">
      <div 
        v-for="particle in particles" 
        :key="particle.id"
        class="heart-particle"
        :class="[
          `heart-${particle.type}`,
          { 'heart-animated': particle.animated }
        ]"
        :style="getParticleStyle(particle)"
      >
        <!-- 不同类型的心形 -->
        <div v-if="particle.type === 'emoji'" class="heart-emoji">
          {{ particle.emoji }}
        </div>
        <div v-else-if="particle.type === 'svg'" class="heart-svg">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
          </svg>
        </div>
        <div v-else-if="particle.type === 'icon'" class="heart-icon">
          <Icon :name="particle.iconName || 'lucide:heart'" />
        </div>
        <div v-else class="heart-shape">
          ❤️
        </div>
      </div>
    </div>
    
    <!-- 发射源指示器 -->
    <div v-if="showEmitter && !autoEmit" class="emitter-indicator">
      <div class="emitter-pulse"></div>
      <Icon name="lucide:heart" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'

// 粒子接口定义
interface HeartParticle {
  id: string
  x: number
  y: number
  vx: number // x轴速度
  vy: number // y轴速度
  life: number // 生命周期 (0-1)
  maxLife: number // 最大生命周期
  size: number
  rotation: number
  rotationSpeed: number
  opacity: number
  color: string
  type: 'emoji' | 'svg' | 'icon' | 'shape'
  emoji?: string
  iconName?: string
  animated: boolean
  gravity: number
  bounce: number
  scale: number
}

interface Props {
  // 基础配置
  autoEmit?: boolean // 自动发射
  particleCount?: number // 每次发射的粒子数量
  emitRate?: number // 自动发射频率 (ms)
  
  // 粒子样式
  color?: string | string[] // 颜色，支持单色或多色数组
  size?: number | [number, number] // 大小，支持固定值或范围
  particleType?: 'emoji' | 'svg' | 'icon' | 'shape' | 'mixed'
  customEmojis?: string[]
  customIcons?: string[]
  
  // 物理效果
  gravity?: number // 重力强度
  bounce?: number // 弹跳系数
  friction?: number // 摩擦力
  spread?: number // 散布角度 (degrees)
  velocity?: number | [number, number] // 初始速度
  
  // 生命周期
  lifetime?: number | [number, number] // 粒子生存时间 (ms)
  fadeOut?: boolean // 是否淡出
  
  // 发射配置
  emitFromCenter?: boolean // 从中心发射
  emitFromMouse?: boolean // 从鼠标位置发射
  emitFromEdges?: boolean // 从边缘发射
  clickToEmit?: boolean // 点击发射
  
  // 高级效果
  enableRotation?: boolean // 启用旋转
  enableScale?: boolean // 启用缩放动画
  enableTrail?: boolean // 启用拖尾效果
  
  // 性能配置
  maxParticles?: number // 最大粒子数量
  enableGpu?: boolean // 启用GPU加速
  
  // UI配置
  showEmitter?: boolean // 显示发射源
  containerClass?: string
}

// Props定义
const props = withDefaults(defineProps<Props>(), {
  autoEmit: false,
  particleCount: 8,
  emitRate: 1000,
  color: '#ff69b4',
  size: 20,
  particleType: 'mixed',
  customEmojis: () => ['❤️', '💖', '💕', '🥰', '😍', '💝', '💗', '💓'],
  customIcons: () => ['lucide:heart', 'lucide:heart-crack', 'lucide:heart-handshake'],
  gravity: 0.3,
  bounce: 0.6,
  friction: 0.98,
  spread: 60,
  velocity: [3, 8],
  lifetime: [2000, 4000],
  fadeOut: true,
  emitFromCenter: true,
  emitFromMouse: false,
  emitFromEdges: false,
  clickToEmit: true,
  enableRotation: true,
  enableScale: true,
  enableTrail: false,
  maxParticles: 100,
  enableGpu: true,
  showEmitter: false,
  containerClass: ''
})

// Emits
const emit = defineEmits<{
  'particle-emit': [count: number]
  'particle-complete': [particle: HeartParticle]
  'animation-start': []
  'animation-end': []
}>()

// 响应式数据
const containerRef = ref<HTMLElement>()
const particles = ref<HeartParticle[]>([])
const animationId = ref<number>()
const autoEmitTimer = ref<number>()
const isAnimating = ref(false)
const lastEmitTime = ref(0)

// 计算属性
const colorArray = computed(() => {
  if (Array.isArray(props.color)) {
    return props.color
  }
  return [props.color]
})

const sizeRange = computed(() => {
  if (Array.isArray(props.size)) {
    return props.size
  }
  return [props.size * 0.8, props.size * 1.2]
})

const velocityRange = computed(() => {
  if (Array.isArray(props.velocity)) {
    return props.velocity
  }
  return [props.velocity * 0.8, props.velocity * 1.2]
})

const lifetimeRange = computed(() => {
  if (Array.isArray(props.lifetime)) {
    return props.lifetime
  }
  return [props.lifetime * 0.8, props.lifetime * 1.2]
})

// 工具函数
const random = (min: number, max: number) => Math.random() * (max - min) + min

const randomChoice = <T>(array: T[]): T => array[Math.floor(Math.random() * array.length)]

const degToRad = (degrees: number) => degrees * Math.PI / 180

const getRandomColor = () => randomChoice(colorArray.value)

const getRandomSize = () => random(sizeRange.value[0], sizeRange.value[1])

const getRandomVelocity = () => random(velocityRange.value[0], velocityRange.value[1])

const getRandomLifetime = () => random(lifetimeRange.value[0], lifetimeRange.value[1])

// 创建粒子
const createParticle = (x: number, y: number): HeartParticle => {
  const angle = degToRad(random(-props.spread / 2, props.spread / 2))
  const velocity = getRandomVelocity()
  const lifetime = getRandomLifetime()
  
  const particle: HeartParticle = {
    id: Date.now().toString() + Math.random(),
    x,
    y,
    vx: Math.sin(angle) * velocity,
    vy: -Math.cos(angle) * velocity, // 负值表示向上
    life: 0,
    maxLife: lifetime,
    size: getRandomSize(),
    rotation: props.enableRotation ? random(0, 360) : 0,
    rotationSpeed: props.enableRotation ? random(-5, 5) : 0,
    opacity: 1,
    color: getRandomColor(),
    type: getParticleType(),
    animated: true,
    gravity: props.gravity,
    bounce: props.bounce,
    scale: 1
  }
  
  // 设置特殊属性
  if (particle.type === 'emoji') {
    particle.emoji = randomChoice(props.customEmojis)
  } else if (particle.type === 'icon') {
    particle.iconName = randomChoice(props.customIcons)
  }
  
  return particle
}

const getParticleType = (): HeartParticle['type'] => {
  if (props.particleType === 'mixed') {
    return randomChoice(['emoji', 'svg', 'icon'] as const)
  }
  return props.particleType
}

// 获取发射位置
const getEmitPosition = (mouseX?: number, mouseY?: number): [number, number] => {
  if (!containerRef.value) return [0, 0]
  
  const rect = containerRef.value.getBoundingClientRect()
  const centerX = rect.width / 2
  const centerY = rect.height / 2
  
  if (props.emitFromMouse && mouseX !== undefined && mouseY !== undefined) {
    return [mouseX - rect.left, mouseY - rect.top]
  } else if (props.emitFromEdges) {
    const edge = Math.floor(Math.random() * 4)
    switch (edge) {
      case 0: return [random(0, rect.width), 0] // 顶部
      case 1: return [rect.width, random(0, rect.height)] // 右侧
      case 2: return [random(0, rect.width), rect.height] // 底部
      case 3: return [0, random(0, rect.height)] // 左侧
      default: return [centerX, centerY]
    }
  } else {
    return [centerX, centerY]
  }
}

// 发射粒子
const emitParticles = (x?: number, y?: number, count?: number) => {
  const [emitX, emitY] = getEmitPosition(x, y)
  const particleCount = count || props.particleCount
  
  // 限制最大粒子数量
  if (particles.value.length + particleCount > props.maxParticles) {
    const removeCount = particles.value.length + particleCount - props.maxParticles
    particles.value.splice(0, removeCount)
  }
  
  for (let i = 0; i < particleCount; i++) {
    const particle = createParticle(emitX, emitY)
    particles.value.push(particle)
  }
  
  emit('particle-emit', particleCount)
  
  if (!isAnimating.value) {
    startAnimation()
  }
}

// 更新粒子
const updateParticles = (deltaTime: number) => {
  if (!containerRef.value) return
  
  const rect = containerRef.value.getBoundingClientRect()
  
  particles.value.forEach((particle, index) => {
    // 更新生命周期
    particle.life += deltaTime
    const lifeRatio = particle.life / particle.maxLife
    
    // 物理更新
    particle.vy += particle.gravity * deltaTime * 0.016 // 模拟60fps
    particle.vx *= props.friction
    particle.vy *= props.friction
    
    particle.x += particle.vx
    particle.y += particle.vy
    
    // 边界碰撞
    if (particle.x < 0 || particle.x > rect.width) {
      particle.vx *= -particle.bounce
      particle.x = Math.max(0, Math.min(rect.width, particle.x))
    }
    
    if (particle.y > rect.height) {
      particle.vy *= -particle.bounce
      particle.y = rect.height
    }
    
    // 旋转更新
    if (props.enableRotation) {
      particle.rotation += particle.rotationSpeed * deltaTime * 0.1
    }
    
    // 缩放动画
    if (props.enableScale) {
      particle.scale = 1 + Math.sin(lifeRatio * Math.PI) * 0.3
    }
    
    // 透明度更新
    if (props.fadeOut) {
      particle.opacity = Math.max(0, 1 - lifeRatio)
    }
    
    // 移除过期粒子
    if (particle.life >= particle.maxLife) {
      emit('particle-complete', particle)
      particles.value.splice(index, 1)
    }
  })
}

// 获取粒子样式
const getParticleStyle = (particle: HeartParticle) => {
  const transform = [
    `translate(${particle.x}px, ${particle.y}px)`,
    `scale(${particle.scale})`,
    `rotate(${particle.rotation}deg)`
  ].join(' ')
  
  return {
    transform,
    opacity: particle.opacity,
    color: particle.color,
    fontSize: `${particle.size}px`,
    width: `${particle.size}px`,
    height: `${particle.size}px`,
    zIndex: Math.floor(particle.life / 100)
  }
}

// 动画循环
let lastTime = 0
const animate = (currentTime: number) => {
  const deltaTime = currentTime - lastTime
  lastTime = currentTime
  
  updateParticles(deltaTime)
  
  if (particles.value.length > 0) {
    animationId.value = requestAnimationFrame(animate)
  } else {
    stopAnimation()
  }
}

const startAnimation = () => {
  if (isAnimating.value) return
  
  isAnimating.value = true
  lastTime = performance.now()
  animationId.value = requestAnimationFrame(animate)
  emit('animation-start')
}

const stopAnimation = () => {
  if (animationId.value) {
    cancelAnimationFrame(animationId.value)
  }
  isAnimating.value = false
  emit('animation-end')
}

// 事件处理
const handleClick = (event: MouseEvent) => {
  if (props.clickToEmit) {
    const rect = containerRef.value?.getBoundingClientRect()
    if (rect) {
      const x = event.clientX
      const y = event.clientY
      emitParticles(x, y)
    }
  }
}

// 自动发射
const startAutoEmit = () => {
  if (props.autoEmit && !autoEmitTimer.value) {
    autoEmitTimer.value = window.setInterval(() => {
      emitParticles()
    }, props.emitRate)
  }
}

const stopAutoEmit = () => {
  if (autoEmitTimer.value) {
    clearInterval(autoEmitTimer.value)
    autoEmitTimer.value = undefined
  }
}

// 清除所有粒子
const clearParticles = () => {
  particles.value = []
  stopAnimation()
}

// 暴露方法
const burst = (x?: number, y?: number, count?: number) => {
  emitParticles(x, y, count || props.particleCount * 2)
}

const pulse = (count = 3, delay = 200) => {
  for (let i = 0; i < count; i++) {
    setTimeout(() => {
      emitParticles()
    }, i * delay)
  }
}

const cascade = (steps = 5, delay = 100) => {
  for (let i = 0; i < steps; i++) {
    setTimeout(() => {
      const count = Math.max(1, Math.floor(props.particleCount / steps))
      emitParticles(undefined, undefined, count)
    }, i * delay)
  }
}

// 暴露方法给父组件
defineExpose({
  emit: emitParticles,
  burst,
  pulse,
  cascade,
  clear: clearParticles,
  start: startAutoEmit,
  stop: stopAutoEmit,
  isAnimating: () => isAnimating.value,
  particleCount: () => particles.value.length
})

// 生命周期
onMounted(() => {
  if (props.autoEmit) {
    startAutoEmit()
  }
})

onUnmounted(() => {
  stopAutoEmit()
  stopAnimation()
})
</script>

<style scoped>
.heart-particles {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.heart-particles.auto-emit {
  pointer-events: none;
}

.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.heart-particle {
  position: absolute;
  pointer-events: none;
  user-select: none;
  transform-origin: center;
  will-change: transform, opacity;
}

.heart-particle.heart-animated {
  animation: heartbeat 0.6s ease-out;
}

/* 不同类型的心形样式 */
.heart-emoji {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: inherit;
  line-height: 1;
}

.heart-svg {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.heart-svg svg {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.heart-icon {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: inherit;
}

.heart-shape {
  font-size: inherit;
  line-height: 1;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* 发射源指示器 */
.emitter-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 105, 180, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ff69b4;
  font-size: 20px;
  pointer-events: auto;
  cursor: pointer;
  transition: all 0.3s ease;
}

.emitter-indicator:hover {
  background: rgba(255, 105, 180, 0.3);
  transform: translate(-50%, -50%) scale(1.1);
}

.emitter-pulse {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(255, 105, 180, 0.3);
  animation: pulse 2s ease-in-out infinite;
}

/* 动画定义 */
@keyframes heartbeat {
  0% {
    transform: scale(0.5);
    opacity: 0;
  }
  15% {
    transform: scale(1.2);
    opacity: 1;
  }
  30% {
    transform: scale(1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.5);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
}

/* GPU加速优化 */
.heart-particle {
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 高性能模式 */
@media (prefers-reduced-motion: no-preference) {
  .heart-particle {
    transition: transform 0.1s ease-out;
  }
}

@media (prefers-reduced-motion: reduce) {
  .heart-particle {
    animation: none;
    transition: none;
  }
  
  .emitter-pulse {
    animation: none;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .heart-particle {
    font-size: 0.8em;
  }
  
  .emitter-indicator {
    width: 36px;
    height: 36px;
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .heart-particle {
    font-size: 0.7em;
  }
  
  .emitter-indicator {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .emitter-indicator {
    background: rgba(255, 105, 180, 0.3);
  }
  
  .emitter-indicator:hover {
    background: rgba(255, 105, 180, 0.4);
  }
  
  .heart-svg svg,
  .heart-shape {
    filter: drop-shadow(0 2px 6px rgba(255, 255, 255, 0.1));
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .heart-particle {
    filter: contrast(1.2);
  }
  
  .emitter-indicator {
    border: 2px solid currentColor;
  }
}
</style>