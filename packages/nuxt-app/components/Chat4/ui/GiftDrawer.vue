<template>
  <div class="gift-drawer-overlay" :class="{ 'visible': visible }" @click="handleOverlayClick">
    <div class="gift-drawer" :class="{ 'open': visible }" @click.stop>
      <!-- 抽屉头部 -->
      <div class="drawer-header">
        <div class="header-title">
          <Icon name="lucide:gift" />
          <span>选择礼物</span>
        </div>
        <div class="header-balance">
          <Icon name="lucide:coins" />
          <span>余额: {{ userBalance }}</span>
        </div>
        <button class="close-btn" @click="closeDrawer">
          <Icon name="lucide:x" />
        </button>
      </div>
      
      <!-- 礼物分类标签 -->
      <div class="gift-categories">
        <button
          v-for="category in giftCategories"
          :key="category.id"
          class="category-tab"
          :class="{ 'active': currentCategory === category.id }"
          @click="selectCategory(category.id)"
        >
          <Icon :name="category.icon" />
          <span>{{ category.name }}</span>
        </button>
      </div>
      
      <!-- 礼物网格 -->
      <div class="gift-grid-container">
        <div class="gift-grid">
          <div
            v-for="gift in filteredGifts"
            :key="gift.id"
            class="gift-item"
            :class="{ 
              'selected': selectedGift?.id === gift.id,
              'affordable': gift.price <= userBalance,
              'limited': gift.limited && gift.stock <= 10
            }"
            @click="selectGift(gift)"
          >
            <!-- 礼物图片 -->
            <div class="gift-image">
              <img :src="gift.image" :alt="gift.name" />
              
              <!-- 限量标识 -->
              <div v-if="gift.limited" class="limited-badge">
                限量 {{ gift.stock }}
              </div>
              
              <!-- 热门标识 -->
              <div v-if="gift.popular" class="popular-badge">
                🔥 热门
              </div>
              
              <!-- 新品标识 -->
              <div v-if="gift.isNew" class="new-badge">
                新品
              </div>
            </div>
            
            <!-- 礼物信息 -->
            <div class="gift-info">
              <div class="gift-name">{{ gift.name }}</div>
              <div class="gift-price">
                <Icon name="lucide:coins" />
                <span>{{ gift.price }}</span>
              </div>
            </div>
            
            <!-- 选中指示器 -->
            <div v-if="selectedGift?.id === gift.id" class="selected-indicator">
              <Icon name="lucide:check" />
            </div>
          </div>
        </div>
      </div>
      
      <!-- 选中礼物详情 -->
      <div v-if="selectedGift" class="selected-gift-detail">
        <div class="detail-info">
          <img :src="selectedGift.image" :alt="selectedGift.name" class="detail-image" />
          <div class="detail-text">
            <div class="detail-name">{{ selectedGift.name }}</div>
            <div class="detail-description">{{ selectedGift.description }}</div>
            <div class="detail-effect" v-if="selectedGift.specialEffect">
              <Icon name="lucide:sparkles" />
              <span>{{ selectedGift.specialEffect }}</span>
            </div>
          </div>
        </div>
        
        <!-- 数量选择 -->
        <div class="quantity-selector">
          <span class="quantity-label">数量:</span>
          <div class="quantity-controls">
            <button 
              class="quantity-btn" 
              @click="decreaseQuantity"
              :disabled="giftQuantity <= 1"
            >
              <Icon name="lucide:minus" />
            </button>
            <input 
              v-model.number="giftQuantity" 
              type="number" 
              min="1" 
              :max="maxQuantity"
              class="quantity-input"
              @input="validateQuantity"
            />
            <button 
              class="quantity-btn" 
              @click="increaseQuantity"
              :disabled="giftQuantity >= maxQuantity"
            >
              <Icon name="lucide:plus" />
            </button>
          </div>
          <div class="total-price">
            总计: {{ totalPrice }} 💰
          </div>
        </div>
      </div>
      
      <!-- 快捷数量选择 -->
      <div v-if="selectedGift" class="quick-quantity">
        <span class="quick-label">快捷:</span>
        <button
          v-for="quick in quickQuantities"
          :key="quick"
          class="quick-btn"
          :class="{ 'active': giftQuantity === quick }"
          :disabled="quick > maxQuantity"
          @click="setQuantity(quick)"
        >
          {{ quick }}
        </button>
      </div>
      
      <!-- 抽屉底部操作 -->
      <div class="drawer-footer">
        <div class="footer-stats">
          <div class="stat-item">
            <Icon name="lucide:users" />
            <span>{{ recipient?.name || '选择接收者' }}</span>
          </div>
          <div v-if="selectedGift" class="stat-item">
            <Icon name="lucide:heart" />
            <span>好感度 +{{ selectedGift.favorability * giftQuantity }}</span>
          </div>
        </div>
        
        <div class="footer-actions">
          <button class="cancel-btn" @click="closeDrawer">
            取消
          </button>
          <button 
            class="send-btn" 
            :class="{ 'loading': isSending }"
            :disabled="!canSendGift"
            @click="sendGift"
          >
            <Icon v-if="isSending" name="lucide:loader-2" class="animate-spin" />
            <Icon v-else name="lucide:send" />
            <span>{{ isSending ? '发送中...' : '发送礼物' }}</span>
          </button>
        </div>
      </div>
      
      <!-- 礼物效果预览 -->
      <div v-if="showPreview && selectedGift" class="gift-preview">
        <HeartParticles 
          :auto-emit="true"
          :particle-count="10"
          :color="selectedGift.effectColor || '#ff69b4'"
          :emit-rate="500"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import HeartParticles from './HeartParticles.vue'

// 礼物接口定义
interface Gift {
  id: string
  name: string
  description: string
  image: string
  price: number
  category: string
  favorability: number // 好感度加成
  specialEffect?: string // 特殊效果描述
  effectColor?: string // 效果颜色
  limited?: boolean // 是否限量
  stock?: number // 库存数量
  popular?: boolean // 是否热门
  isNew?: boolean // 是否新品
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
}

interface GiftCategory {
  id: string
  name: string
  icon: string
}

interface Recipient {
  id: string
  name: string
  avatar?: string
}

interface Props {
  visible: boolean
  userBalance: number
  recipient?: Recipient
  gifts?: Gift[]
  showPreview?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  userBalance: 1000,
  gifts: () => [],
  showPreview: true
})

// Emits
const emit = defineEmits<{
  'close': []
  'send-gift': [gift: Gift, quantity: number, totalPrice: number]
  'gift-select': [gift: Gift]
  'insufficient-balance': [required: number, current: number]
}>()

// 响应式数据
const selectedGift = ref<Gift | null>(null)
const giftQuantity = ref(1)
const currentCategory = ref('all')
const isSending = ref(false)

// 礼物分类
const giftCategories: GiftCategory[] = [
  { id: 'all', name: '全部', icon: 'lucide:grid-3x3' },
  { id: 'flowers', name: '鲜花', icon: 'lucide:flower' },
  { id: 'jewelry', name: '珠宝', icon: 'lucide:gem' },
  { id: 'luxury', name: '奢侈品', icon: 'lucide:crown' },
  { id: 'food', name: '美食', icon: 'lucide:cake' },
  { id: 'digital', name: '数码', icon: 'lucide:smartphone' },
  { id: 'special', name: '特殊', icon: 'lucide:star' }
]

// 快捷数量选择
const quickQuantities = [1, 5, 10, 20, 50, 99]

// 默认礼物数据
const defaultGifts: Gift[] = [
  {
    id: 'rose',
    name: '玫瑰花',
    description: '经典的爱情象征，表达真挚情感',
    image: '/images/gifts/rose.png',
    price: 10,
    category: 'flowers',
    favorability: 5,
    specialEffect: '花瓣飞舞',
    effectColor: '#ff69b4',
    rarity: 'common'
  },
  {
    id: 'diamond-ring',
    name: '钻石戒指',
    description: '璀璨夺目的钻石戒指，承载永恒承诺',
    image: '/images/gifts/diamond-ring.png',
    price: 999,
    category: 'jewelry',
    favorability: 100,
    specialEffect: '钻石闪耀',
    effectColor: '#87ceeb',
    popular: true,
    rarity: 'legendary'
  },
  {
    id: 'chocolate',
    name: '巧克力',
    description: '甜蜜的心意，温暖的关怀',
    image: '/images/gifts/chocolate.png',
    price: 25,
    category: 'food',
    favorability: 15,
    specialEffect: '甜蜜泡泡',
    effectColor: '#8b4513',
    rarity: 'common'
  },
  {
    id: 'crown',
    name: '皇冠',
    description: '尊贵的象征，彰显特殊地位',
    image: '/images/gifts/crown.png',
    price: 2999,
    category: 'luxury',
    favorability: 200,
    specialEffect: '金光闪闪',
    effectColor: '#ffd700',
    limited: true,
    stock: 5,
    rarity: 'legendary'
  },
  {
    id: 'coffee',
    name: '咖啡',
    description: '提神醒脑的香浓咖啡',
    image: '/images/gifts/coffee.png',
    price: 15,
    category: 'food',
    favorability: 8,
    rarity: 'common'
  },
  {
    id: 'perfume',
    name: '香水',
    description: '优雅迷人的香氛',
    image: '/images/gifts/perfume.png',
    price: 199,
    category: 'luxury',
    favorability: 30,
    specialEffect: '香氛缭绕',
    effectColor: '#dda0dd',
    isNew: true,
    rarity: 'rare'
  }
]

// 计算属性
const giftList = computed(() => props.gifts.length > 0 ? props.gifts : defaultGifts)

const filteredGifts = computed(() => {
  if (currentCategory.value === 'all') {
    return giftList.value
  }
  return giftList.value.filter(gift => gift.category === currentCategory.value)
})

const maxQuantity = computed(() => {
  if (!selectedGift.value) return 1
  
  // 根据用户余额计算最大购买数量
  const maxByBalance = Math.floor(props.userBalance / selectedGift.value.price)
  
  // 如果是限量商品，还需要考虑库存
  if (selectedGift.value.limited && selectedGift.value.stock) {
    return Math.min(maxByBalance, selectedGift.value.stock, 99)
  }
  
  return Math.min(maxByBalance, 99)
})

const totalPrice = computed(() => {
  if (!selectedGift.value) return 0
  return selectedGift.value.price * giftQuantity.value
})

const canSendGift = computed(() => {
  return selectedGift.value && 
         giftQuantity.value > 0 && 
         totalPrice.value <= props.userBalance &&
         props.recipient &&
         !isSending.value
})

// 方法
const selectCategory = (categoryId: string) => {
  currentCategory.value = categoryId
}

const selectGift = (gift: Gift) => {
  selectedGift.value = gift
  giftQuantity.value = 1
  emit('gift-select', gift)
}

const increaseQuantity = () => {
  if (giftQuantity.value < maxQuantity.value) {
    giftQuantity.value++
  }
}

const decreaseQuantity = () => {
  if (giftQuantity.value > 1) {
    giftQuantity.value--
  }
}

const setQuantity = (quantity: number) => {
  if (quantity <= maxQuantity.value) {
    giftQuantity.value = quantity
  }
}

const validateQuantity = () => {
  if (giftQuantity.value < 1) {
    giftQuantity.value = 1
  } else if (giftQuantity.value > maxQuantity.value) {
    giftQuantity.value = maxQuantity.value
  }
}

const handleOverlayClick = () => {
  closeDrawer()
}

const closeDrawer = () => {
  emit('close')
  // 重置状态
  selectedGift.value = null
  giftQuantity.value = 1
  currentCategory.value = 'all'
}

const sendGift = async () => {
  if (!canSendGift.value || !selectedGift.value) return
  
  // 检查余额
  if (totalPrice.value > props.userBalance) {
    emit('insufficient-balance', totalPrice.value, props.userBalance)
    return
  }
  
  isSending.value = true
  
  try {
    // 模拟发送延迟
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    emit('send-gift', selectedGift.value, giftQuantity.value, totalPrice.value)
    closeDrawer()
  } catch (error) {
    console.error('发送礼物失败:', error)
  } finally {
    isSending.value = false
  }
}

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    // 抽屉打开时重置状态
    currentCategory.value = 'all'
    selectedGift.value = null
    giftQuantity.value = 1
  }
})

watch(() => selectedGift.value, () => {
  giftQuantity.value = 1
})

// 暴露方法给父组件
defineExpose({
  selectGift,
  clearSelection: () => { selectedGift.value = null },
  getCurrentGift: () => selectedGift.value,
  getQuantity: () => giftQuantity.value,
  getTotalPrice: () => totalPrice.value
})
</script>

<style scoped>
.gift-drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
}

.gift-drawer-overlay.visible {
  opacity: 1;
  visibility: visible;
}

.gift-drawer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  max-height: 80vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px 20px 0 0;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.3);
}

.gift-drawer.open {
  transform: translateY(0);
}

/* 头部 */
.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-size: 18px;
  font-weight: 600;
}

.header-balance {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #ffd700;
  font-size: 14px;
  font-weight: 500;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* 分类标签 */
.gift-categories {
  display: flex;
  gap: 12px;
  padding: 16px 20px;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.gift-categories::-webkit-scrollbar {
  display: none;
}

.category-tab {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  flex-shrink: 0;
}

.category-tab:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.category-tab.active {
  background: white;
  color: #667eea;
  font-weight: 600;
}

/* 礼物网格 */
.gift-grid-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px;
}

.gift-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 12px;
  padding-bottom: 16px;
}

.gift-item {
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 2px solid transparent;
}

.gift-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.gift-item.selected {
  border-color: #ffd700;
  background: rgba(255, 215, 0, 0.2);
}

.gift-item.affordable {
  /* 可购买状态默认样式 */
}

.gift-item:not(.affordable) {
  opacity: 0.5;
  cursor: not-allowed;
}

.gift-item.limited {
  border-color: #ff6b6b;
}

.gift-image {
  position: relative;
  width: 60px;
  height: 60px;
  margin: 0 auto 8px;
  border-radius: 8px;
  overflow: hidden;
}

.gift-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.limited-badge,
.popular-badge,
.new-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 600;
  line-height: 1;
}

.limited-badge {
  background: #ff6b6b;
  color: white;
}

.popular-badge {
  background: #ff8c00;
  color: white;
}

.new-badge {
  background: #4ecdc4;
  color: white;
}

.gift-info {
  text-align: center;
}

.gift-name {
  font-size: 12px;
  font-weight: 500;
  color: white;
  margin-bottom: 4px;
  line-height: 1.2;
}

.gift-price {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 11px;
  color: #ffd700;
  font-weight: 600;
}

.selected-indicator {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 20px;
  height: 20px;
  background: #ffd700;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  font-size: 12px;
}

/* 选中礼物详情 */
.selected-gift-detail {
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.1);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.detail-info {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.detail-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
}

.detail-text {
  flex: 1;
}

.detail-name {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.detail-description {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
  margin-bottom: 6px;
}

.detail-effect {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #ffd700;
}

/* 数量选择 */
.quantity-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.quantity-label {
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.quantity-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quantity-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-input {
  width: 60px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
}

.quantity-input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.3);
}

.total-price {
  color: #ffd700;
  font-size: 14px;
  font-weight: 600;
}

/* 快捷数量 */
.quick-quantity {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 20px 16px;
}

.quick-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

.quick-btn {
  padding: 4px 12px;
  border: none;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quick-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.quick-btn.active {
  background: white;
  color: #667eea;
  font-weight: 600;
}

.quick-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 底部操作 */
.drawer-footer {
  padding: 16px 20px;
  background: rgba(0, 0, 0, 0.2);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.footer-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

.footer-actions {
  display: flex;
  gap: 12px;
}

.cancel-btn,
.send-btn {
  flex: 1;
  height: 44px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.cancel-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.cancel-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.send-btn {
  background: linear-gradient(135deg, #ffd700, #ffb347);
  color: #333;
}

.send-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(255, 215, 0, 0.4);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.send-btn.loading {
  pointer-events: none;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 礼物效果预览 */
.gift-preview {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .gift-drawer {
    max-height: 85vh;
  }
  
  .gift-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 8px;
  }
  
  .gift-image {
    width: 50px;
    height: 50px;
  }
  
  .gift-name {
    font-size: 11px;
  }
  
  .gift-price {
    font-size: 10px;
  }
  
  .detail-info {
    flex-direction: column;
    gap: 8px;
  }
  
  .quantity-selector {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .footer-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .drawer-header {
    padding: 12px 16px;
  }
  
  .gift-categories {
    padding: 12px 16px;
  }
  
  .gift-grid-container {
    padding: 0 16px;
  }
  
  .selected-gift-detail {
    padding: 12px 16px;
  }
  
  .drawer-footer {
    padding: 12px 16px;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .gift-item {
    border: 1px solid rgba(255, 255, 255, 0.5);
  }
  
  .gift-item.selected {
    border-color: #ffd700;
    border-width: 2px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .gift-drawer-overlay,
  .gift-drawer,
  .gift-item,
  .category-tab,
  .quantity-btn,
  .send-btn {
    transition: none;
  }
  
  .animate-spin {
    animation: none;
  }
}
</style>