<template>
  <Teleport to="body">
    <div class="friend-request-overlay" :class="{ 'visible': visible }" @click="handleOverlayClick">
      <div class="friend-request-modal" :class="{ 'show': visible }" @click.stop>
        <!-- 模态框头部 -->
        <div class="modal-header">
          <div class="header-title">
            <Icon name="lucide:user-plus" />
            <span>{{ isOutgoing ? '发送好友请求' : '好友请求' }}</span>
          </div>
          <button class="close-btn" @click="closeModal">
            <Icon name="lucide:x" />
          </button>
        </div>
        
        <!-- 用户信息卡片 -->
        <div class="user-card">
          <div class="user-avatar">
            <img v-if="userInfo.avatar" :src="userInfo.avatar" :alt="userInfo.name" />
            <div v-else class="avatar-placeholder">
              {{ userInfo.name?.charAt(0) || '?' }}
            </div>
            
            <!-- 在线状态 -->
            <div class="online-status" :class="{ 'online': userInfo.isOnline }"></div>
            
            <!-- VIP标识 -->
            <div v-if="userInfo.isVip" class="vip-badge">
              <Icon name="lucide:crown" />
            </div>
          </div>
          
          <div class="user-info">
            <div class="user-name">{{ userInfo.name }}</div>
            <div class="user-details">
              <div class="detail-item" v-if="userInfo.level">
                <Icon name="lucide:star" />
                <span>LV{{ userInfo.level }}</span>
              </div>
              <div class="detail-item" v-if="userInfo.location">
                <Icon name="lucide:map-pin" />
                <span>{{ userInfo.location }}</span>
              </div>
              <div class="detail-item" v-if="userInfo.lastActive">
                <Icon name="lucide:clock" />
                <span>{{ formatLastActive(userInfo.lastActive) }}</span>
              </div>
            </div>
            
            <!-- 用户标签 -->
            <div class="user-tags" v-if="userInfo.tags && userInfo.tags.length > 0">
              <span
                v-for="tag in userInfo.tags"
                :key="tag"
                class="tag"
              >
                {{ tag }}
              </span>
            </div>
            
            <!-- 个性签名 -->
            <div class="user-bio" v-if="userInfo.bio">
              <Icon name="lucide:quote" />
              <span>{{ userInfo.bio }}</span>
            </div>
          </div>
        </div>
        
        <!-- 互动历史 -->
        <div class="interaction-history" v-if="interactionHistory.length > 0">
          <div class="history-title">
            <Icon name="lucide:history" />
            <span>互动历史</span>
          </div>
          <div class="history-list">
            <div
              v-for="interaction in interactionHistory"
              :key="interaction.id"
              class="history-item"
            >
              <div class="history-icon">
                <Icon :name="getInteractionIcon(interaction.type)" />
              </div>
              <div class="history-content">
                <div class="history-action">{{ interaction.description }}</div>
                <div class="history-time">{{ formatTime(interaction.timestamp) }}</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 好友请求消息 -->
        <div class="request-message" v-if="!isOutgoing || showMessageInput">
          <div class="message-label">
            <Icon name="lucide:message-square" />
            <span>{{ isOutgoing ? '附加消息 (可选)' : '请求消息' }}</span>
          </div>
          
          <div class="message-content" v-if="!isOutgoing && requestData.message">
            <div class="message-text">{{ requestData.message }}</div>
            <div class="message-time">{{ formatTime(requestData.timestamp) }}</div>
          </div>
          
          <div class="message-input" v-if="isOutgoing">
            <textarea
              v-model="friendRequestMessage"
              placeholder="想说点什么吗？(可选)"
              class="message-textarea"
              :maxlength="200"
              rows="3"
            ></textarea>
            <div class="message-counter">{{ friendRequestMessage.length }}/200</div>
          </div>
        </div>
        
        <!-- 共同好友 -->
        <div class="mutual-friends" v-if="mutualFriends.length > 0">
          <div class="mutual-title">
            <Icon name="lucide:users" />
            <span>共同好友 ({{ mutualFriends.length }})</span>
          </div>
          <div class="mutual-list">
            <div
              v-for="friend in mutualFriends.slice(0, 6)"
              :key="friend.id"
              class="mutual-friend"
              @click="viewFriendProfile(friend)"
            >
              <img v-if="friend.avatar" :src="friend.avatar" :alt="friend.name" />
              <div v-else class="mutual-avatar-placeholder">
                {{ friend.name.charAt(0) }}
              </div>
              <span class="mutual-name">{{ friend.name }}</span>
            </div>
            <div v-if="mutualFriends.length > 6" class="mutual-more">
              +{{ mutualFriends.length - 6 }}
            </div>
          </div>
        </div>
        
        <!-- 隐私设置提醒 -->
        <div class="privacy-notice" v-if="isOutgoing">
          <Icon name="lucide:shield" />
          <div class="notice-content">
            <div class="notice-title">隐私提醒</div>
            <div class="notice-text">
              添加好友后，对方将能够看到您的在线状态和基本信息
            </div>
          </div>
        </div>
        
        <!-- 请求状态指示器 -->
        <div class="request-status" v-if="requestData.status && !isOutgoing">
          <div class="status-indicator" :class="`status-${requestData.status}`">
            <Icon :name="getStatusIcon(requestData.status)" />
            <span>{{ getStatusText(requestData.status) }}</span>
          </div>
        </div>
        
        <!-- 底部操作 -->
        <div class="modal-footer">
          <!-- 发送请求模式 -->
          <div v-if="isOutgoing" class="outgoing-actions">
            <button class="cancel-btn" @click="closeModal" :disabled="isProcessing">
              取消
            </button>
            <button
              class="send-request-btn"
              :class="{ 'loading': isProcessing }"
              :disabled="!canSendRequest"
              @click="sendFriendRequest"
            >
              <Icon v-if="isProcessing" name="lucide:loader-2" class="animate-spin" />
              <Icon v-else name="lucide:user-plus" />
              <span>{{ isProcessing ? '发送中...' : '发送请求' }}</span>
            </button>
          </div>
          
          <!-- 处理请求模式 -->
          <div v-else class="incoming-actions">
            <button
              class="reject-btn"
              :disabled="isProcessing"
              @click="handleRequest('reject')"
            >
              <Icon name="lucide:x" />
              <span>拒绝</span>
            </button>
            <button
              class="ignore-btn"
              :disabled="isProcessing"
              @click="handleRequest('ignore')"
            >
              <Icon name="lucide:minus" />
              <span>忽略</span>
            </button>
            <button
              class="accept-btn"
              :class="{ 'loading': isProcessing }"
              :disabled="isProcessing"
              @click="handleRequest('accept')"
            >
              <Icon v-if="isProcessing" name="lucide:loader-2" class="animate-spin" />
              <Icon v-else name="lucide:check" />
              <span>{{ isProcessing ? '处理中...' : '接受' }}</span>
            </button>
          </div>
        </div>
        
        <!-- 成功动画 -->
        <div v-if="showSuccessAnimation" class="success-animation">
          <div class="success-content">
            <div class="success-icon">
              <Icon name="lucide:user-check" />
            </div>
            <div class="success-text">
              {{ isOutgoing ? '请求已发送！' : '已成为好友！' }}
            </div>
            <HeartParticles 
              :auto-emit="true"
              :particle-count="12"
              :emit-rate="300"
              color="#4caf50"
              :custom-emojis="['🎉', '✨', '💫', '🎊']"
              particle-type="emoji"
            />
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import HeartParticles from './HeartParticles.vue'

// 用户信息接口
interface UserInfo {
  id: string
  name: string
  avatar?: string
  bio?: string
  level?: number
  location?: string
  lastActive?: number
  isOnline?: boolean
  isVip?: boolean
  tags?: string[]
}

// 好友请求数据接口
interface FriendRequestData {
  id: string
  fromUserId: string
  toUserId: string
  message?: string
  timestamp: number
  status: 'pending' | 'accepted' | 'rejected' | 'ignored'
}

// 互动历史接口
interface InteractionHistory {
  id: string
  type: 'chat' | 'like' | 'comment' | 'gift' | 'visit'
  description: string
  timestamp: number
}

// 共同好友接口
interface MutualFriend {
  id: string
  name: string
  avatar?: string
}

interface Props {
  visible: boolean
  userInfo: UserInfo
  isOutgoing?: boolean // true: 发送请求, false: 处理收到的请求
  requestData?: FriendRequestData
  interactionHistory?: InteractionHistory[]
  mutualFriends?: MutualFriend[]
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  isOutgoing: true,
  requestData: () => ({
    id: '',
    fromUserId: '',
    toUserId: '',
    timestamp: Date.now(),
    status: 'pending'
  }),
  interactionHistory: () => [],
  mutualFriends: () => []
})

// Emits
const emit = defineEmits<{
  'close': []
  'send-request': [message: string]
  'handle-request': [action: 'accept' | 'reject' | 'ignore', requestId: string]
  'view-profile': [userId: string]
  'view-mutual-friend': [friendId: string]
}>()

// 响应式数据
const friendRequestMessage = ref('')
const showMessageInput = ref(false)
const isProcessing = ref(false)
const showSuccessAnimation = ref(false)

// 计算属性
const canSendRequest = computed(() => {
  return !isProcessing.value && props.userInfo.id
})

// 方法
const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 24小时内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric'
    })
  }
}

const formatLastActive = (timestamp: number): string => {
  const diff = Date.now() - timestamp
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days > 7) return '很久以前在线'
  if (days > 0) return `${days}天前在线`
  if (hours > 0) return `${hours}小时前在线`
  if (minutes > 0) return `${minutes}分钟前在线`
  return '刚刚在线'
}

const getInteractionIcon = (type: string): string => {
  const iconMap = {
    chat: 'lucide:message-circle',
    like: 'lucide:heart',
    comment: 'lucide:message-square',
    gift: 'lucide:gift',
    visit: 'lucide:eye'
  }
  return iconMap[type] || 'lucide:circle'
}

const getStatusIcon = (status: string): string => {
  const iconMap = {
    pending: 'lucide:clock',
    accepted: 'lucide:check-circle',
    rejected: 'lucide:x-circle',
    ignored: 'lucide:minus-circle'
  }
  return iconMap[status] || 'lucide:circle'
}

const getStatusText = (status: string): string => {
  const textMap = {
    pending: '等待处理',
    accepted: '已接受',
    rejected: '已拒绝',
    ignored: '已忽略'
  }
  return textMap[status] || '未知状态'
}

const handleOverlayClick = () => {
  if (!isProcessing.value) {
    closeModal()
  }
}

const closeModal = () => {
  if (isProcessing.value) return
  
  emit('close')
  // 重置状态
  friendRequestMessage.value = ''
  showMessageInput.value = false
  showSuccessAnimation.value = false
}

const sendFriendRequest = async () => {
  if (!canSendRequest.value) return
  
  isProcessing.value = true
  
  try {
    // 模拟发送延迟
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    emit('send-request', friendRequestMessage.value)
    
    // 显示成功动画
    showSuccessAnimation.value = true
    
    // 延迟关闭
    setTimeout(() => {
      closeModal()
    }, 2000)
    
  } catch (error) {
    console.error('发送好友请求失败:', error)
  } finally {
    isProcessing.value = false
  }
}

const handleRequest = async (action: 'accept' | 'reject' | 'ignore') => {
  isProcessing.value = true
  
  try {
    // 模拟处理延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('handle-request', action, props.requestData.id)
    
    if (action === 'accept') {
      // 显示成功动画
      showSuccessAnimation.value = true
      
      // 延迟关闭
      setTimeout(() => {
        closeModal()
      }, 2000)
    } else {
      closeModal()
    }
    
  } catch (error) {
    console.error('处理好友请求失败:', error)
  } finally {
    isProcessing.value = false
  }
}

const viewFriendProfile = (friend: MutualFriend) => {
  emit('view-mutual-friend', friend.id)
}

// 监听器
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    // 模态框打开时重置状态
    friendRequestMessage.value = ''
    showMessageInput.value = false
    showSuccessAnimation.value = false
    isProcessing.value = false
  }
})

// 暴露方法给父组件
defineExpose({
  showMessageInput: () => { showMessageInput.value = true },
  hideMessageInput: () => { showMessageInput.value = false },
  setMessage: (message: string) => { friendRequestMessage.value = message },
  getMessage: () => friendRequestMessage.value
})
</script>

<style scoped>
.friend-request-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.friend-request-overlay.visible {
  opacity: 1;
  visibility: visible;
}

.friend-request-modal {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 480px;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(0.9) translateY(20px);
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.friend-request-modal.show {
  transform: scale(1) translateY(0);
}

/* 头部 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16px 16px 0 0;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* 用户卡片 */
.user-card {
  display: flex;
  gap: 16px;
  padding: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.user-avatar {
  position: relative;
  width: 80px;
  height: 80px;
  flex-shrink: 0;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
  font-weight: 600;
  text-transform: uppercase;
}

.online-status {
  position: absolute;
  bottom: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #e5e7eb;
  border: 2px solid white;
}

.online-status.online {
  background: #10b981;
}

.vip-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(45deg, #ffd700, #ff8c00);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 20px;
  font-weight: 700;
  color: #333;
  margin-bottom: 8px;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #666;
}

.user-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 12px;
}

.tag {
  padding: 2px 8px;
  background: #f3f4f6;
  color: #374151;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.user-bio {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  font-style: italic;
}

/* 互动历史 */
.interaction-history {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.history-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 12px;
}

.history-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-size: 14px;
}

.history-content {
  flex: 1;
}

.history-action {
  font-size: 14px;
  color: #333;
  margin-bottom: 2px;
}

.history-time {
  font-size: 12px;
  color: #9ca3af;
}

/* 请求消息 */
.request-message {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.message-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.message-content {
  background: #f3f4f6;
  padding: 16px;
  border-radius: 12px;
  border-left: 4px solid #667eea;
}

.message-text {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8px;
}

.message-time {
  font-size: 12px;
  color: #9ca3af;
}

.message-input {
  position: relative;
}

.message-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  min-height: 80px;
  transition: border-color 0.2s ease;
}

.message-textarea:focus {
  outline: none;
  border-color: #667eea;
}

.message-counter {
  position: absolute;
  bottom: 8px;
  right: 12px;
  font-size: 11px;
  color: #9ca3af;
  background: white;
  padding: 2px 4px;
  border-radius: 4px;
}

/* 共同好友 */
.mutual-friends {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.mutual-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.mutual-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.mutual-friend {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.mutual-friend:hover {
  transform: scale(1.05);
}

.mutual-friend img,
.mutual-avatar-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.mutual-avatar-placeholder {
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  font-weight: 600;
}

.mutual-name {
  font-size: 11px;
  color: #666;
  text-align: center;
  max-width: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.mutual-more {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #f3f4f6;
  border-radius: 50%;
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

/* 隐私提醒 */
.privacy-notice {
  display: flex;
  gap: 12px;
  padding: 16px 24px;
  background: #f0f9ff;
  border: 1px solid #e0f2fe;
  border-radius: 12px;
  margin: 0 24px 20px;
}

.privacy-notice > svg {
  color: #0ea5e9;
  margin-top: 2px;
  flex-shrink: 0;
}

.notice-content {
  flex: 1;
}

.notice-title {
  font-size: 14px;
  font-weight: 600;
  color: #0c4a6e;
  margin-bottom: 4px;
}

.notice-text {
  font-size: 12px;
  color: #0369a1;
  line-height: 1.4;
}

/* 请求状态 */
.request-status {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
}

.status-pending {
  background: #fef3c7;
  color: #92400e;
}

.status-accepted {
  background: #d1fae5;
  color: #065f46;
}

.status-rejected {
  background: #fee2e2;
  color: #991b1b;
}

.status-ignored {
  background: #f3f4f6;
  color: #374151;
}

/* 底部操作 */
.modal-footer {
  padding: 20px 24px;
  border-radius: 0 0 16px 16px;
}

.outgoing-actions,
.incoming-actions {
  display: flex;
  gap: 12px;
}

.cancel-btn,
.reject-btn,
.ignore-btn,
.accept-btn,
.send-request-btn {
  flex: 1;
  height: 48px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.cancel-btn,
.ignore-btn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.cancel-btn:hover,
.ignore-btn:hover {
  background: #e5e7eb;
}

.reject-btn {
  background: #fee2e2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.reject-btn:hover {
  background: #fecaca;
}

.accept-btn,
.send-request-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.accept-btn:hover:not(:disabled),
.send-request-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.accept-btn:disabled,
.send-request-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.accept-btn.loading,
.send-request-btn.loading {
  pointer-events: none;
}

/* 成功动画 */
.success-animation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  z-index: 10;
}

.success-content {
  text-align: center;
  position: relative;
}

.success-icon {
  font-size: 60px;
  color: #10b981;
  margin-bottom: 16px;
  animation: success-bounce 0.6s ease-out;
}

.success-text {
  font-size: 18px;
  font-weight: 600;
  color: #10b981;
  animation: success-fade-in 0.8s ease-out 0.3s both;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes success-bounce {
  0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
  40%, 43% { transform: translateY(-20px); }
  70% { transform: translateY(-10px); }
  90% { transform: translateY(-4px); }
}

@keyframes success-fade-in {
  0% { opacity: 0; transform: translateY(10px); }
  100% { opacity: 1; transform: translateY(0); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .friend-request-modal {
    max-width: 100%;
    margin: 0;
    border-radius: 16px 16px 0 0;
  }
  
  .user-card {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 12px;
  }
  
  .user-info {
    width: 100%;
  }
  
  .mutual-list {
    justify-content: center;
  }
  
  .incoming-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .modal-header {
    padding: 16px 20px;
  }
  
  .user-card {
    padding: 20px;
  }
  
  .interaction-history,
  .request-message,
  .mutual-friends {
    padding: 16px 20px;
  }
  
  .modal-footer {
    padding: 16px 20px;
  }
  
  .header-title {
    font-size: 16px;
  }
  
  .user-name {
    font-size: 18px;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .history-item,
  .message-content {
    border: 1px solid #d1d5db;
  }
  
  .tag {
    border: 1px solid #d1d5db;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .friend-request-overlay,
  .friend-request-modal,
  .mutual-friend,
  .accept-btn,
  .send-request-btn {
    transition: none;
  }
  
  .animate-spin,
  .success-icon,
  .success-text {
    animation: none;
  }
}
</style>