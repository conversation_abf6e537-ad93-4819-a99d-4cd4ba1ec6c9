<template>
  <div class="chat-interface" :class="{ 'minimized': isMinimized, 'fullscreen': isFullscreen }">
    <!-- 聊天头部 -->
    <div class="chat-header" @click="toggleMinimize">
      <div class="chat-title">
        <div class="title-content">
          <div class="avatar-container">
            <img v-if="currentActor?.avatar" :src="currentActor.avatar" :alt="currentActor.name" class="avatar" />
            <div v-else class="avatar-fallback">
              <Icon name="lucide:user" />
            </div>
            <div class="online-indicator" :class="{ 'online': isOnline }"></div>
          </div>
          
          <div class="chat-info">
            <div class="name">{{ currentActor?.name || 'Unknown' }}</div>
            <div class="status">{{ getStatusText() }}</div>
          </div>
        </div>
        
        <div class="header-actions">
          <button class="action-btn" @click.stop="toggleFullscreen">
            <Icon :name="isFullscreen ? 'lucide:minimize-2' : 'lucide:maximize-2'" />
          </button>
          <button class="action-btn" @click.stop="openSettings">
            <Icon name="lucide:settings" />
          </button>
          <button class="action-btn minimize-btn" @click.stop="toggleMinimize">
            <Icon :name="isMinimized ? 'lucide:chevron-up' : 'lucide:chevron-down'" />
          </button>
        </div>
      </div>
    </div>
    
    <!-- 聊天内容区 -->
    <Transition name="chat-content">
      <div v-show="!isMinimized" class="chat-content">
        <!-- 消息列表 -->
        <div ref="messagesContainer" class="messages-container" @scroll="handleScroll">
          <div class="messages-list">
            <!-- 加载更多历史消息 -->
            <div v-if="hasMoreHistory" class="load-more" @click="loadMoreHistory">
              <div class="loading-spinner" v-if="isLoadingHistory"></div>
              <span v-else>加载更多消息</span>
            </div>
            
            <!-- 消息项 -->
            <div 
              v-for="message in messages" 
              :key="message.id"
              class="message-item"
              :class="{
                'own-message': message.isOwn,
                'system-message': message.type === 'system',
                'gift-message': message.type === 'gift',
                'scene-change': message.type === 'scene_change'
              }"
            >
              <!-- 时间戳分割 -->
              <div v-if="shouldShowTimestamp(message)" class="timestamp-divider">
                {{ formatTimestamp(message.timestamp) }}
              </div>
              
              <!-- 系统消息 -->
              <div v-if="message.type === 'system'" class="system-content">
                <Icon name="lucide:info" />
                <span>{{ message.content }}</span>
              </div>
              
              <!-- 场景切换消息 -->
              <div v-else-if="message.type === 'scene_change'" class="scene-change-content">
                <Icon name="lucide:map-pin" />
                <span>{{ message.content }}</span>
                <button v-if="message.action" class="scene-action-btn" @click="handleSceneAction(message.action)">
                  {{ message.actionText || '前往' }}
                </button>
              </div>
              
              <!-- 礼物消息 -->
              <div v-else-if="message.type === 'gift'" class="gift-content">
                <div class="gift-info">
                  <img :src="message.giftIcon" :alt="message.giftName" class="gift-icon" />
                  <div class="gift-details">
                    <div class="gift-name">{{ message.giftName }}</div>
                    <div class="gift-from">来自: {{ message.fromName }}</div>
                  </div>
                </div>
                <div class="gift-effect">
                  <HeartParticles 
                    v-if="message.showHearts" 
                    :auto-emit="true" 
                    :particle-count="8"
                    color="#ff69b4"
                  />
                </div>
              </div>
              
              <!-- 普通文本消息 -->
              <div v-else class="text-message">
                <div v-if="!message.isOwn" class="message-avatar">
                  <img :src="message.avatar || currentActor?.avatar" :alt="message.senderName" />
                </div>
                
                <div class="message-content">
                  <div v-if="!message.isOwn && showSenderName" class="sender-name">
                    {{ message.senderName || currentActor?.name }}
                  </div>
                  
                  <div class="message-bubble" :class="getBubbleClass(message)">
                    <!-- 文本内容 -->
                    <div v-if="message.content" class="message-text">
                      {{ message.content }}
                    </div>
                    
                    <!-- 图片内容 -->
                    <div v-if="message.images?.length" class="message-images">
                      <img 
                        v-for="(image, idx) in message.images" 
                        :key="idx"
                        :src="image" 
                        :alt="`Image ${idx + 1}`"
                        class="message-image"
                        @click="previewImage(image, message.images, idx)"
                      />
                    </div>
                    
                    <!-- 语音消息 -->
                    <div v-if="message.audio" class="message-audio">
                      <button class="audio-play-btn" @click="playAudio(message.audio)">
                        <Icon :name="message.isPlaying ? 'lucide:pause' : 'lucide:play'" />
                      </button>
                      <div class="audio-waveform">
                        <div class="waveform-bar" v-for="i in 12" :key="i"></div>
                      </div>
                      <span class="audio-duration">{{ message.audioDuration || '0:00' }}</span>
                    </div>
                    
                    <!-- 消息状态 -->
                    <div class="message-status">
                      <span class="message-time">{{ formatMessageTime(message.timestamp) }}</span>
                      <div v-if="message.isOwn" class="delivery-status">
                        <Icon 
                          :name="getDeliveryIcon(message.deliveryStatus)" 
                          :class="getDeliveryClass(message.deliveryStatus)"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 正在输入指示器 -->
            <div v-if="isTyping" class="typing-indicator">
              <div class="typing-avatar">
                <img :src="currentActor?.avatar" :alt="currentActor?.name" />
              </div>
              <div class="typing-content">
                <div class="typing-bubble">
                  <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 快捷回复建议 -->
        <div v-if="quickReplies.length > 0" class="quick-replies">
          <button 
            v-for="reply in quickReplies"
            :key="reply.id"
            class="quick-reply-btn"
            @click="sendQuickReply(reply)"
          >
            {{ reply.text }}
          </button>
        </div>
        
        <!-- 输入区域 -->
        <div class="input-area">
          <div class="input-tools">
            <button class="tool-btn" @click="selectEmoji" :class="{ 'active': showEmojiPicker }">
              <Icon name="lucide:smile" />
            </button>
            <button class="tool-btn" @click="selectImage">
              <Icon name="lucide:image" />
            </button>
            <button class="tool-btn" @click="startVoiceRecord" :class="{ 'recording': isRecording }">
              <Icon name="lucide:mic" />
            </button>
            <button class="tool-btn" @click="openGiftDrawer">
              <Icon name="lucide:gift" />
            </button>
          </div>
          
          <div class="input-container">
            <textarea
              ref="messageInput"
              v-model="currentMessage"
              :placeholder="inputPlaceholder"
              class="message-input"
              rows="1"
              :maxlength="messageMaxLength"
              @keydown="handleKeydown"
              @input="handleInput"
              @focus="handleInputFocus"
              @blur="handleInputBlur"
            ></textarea>
            
            <button 
              class="send-btn" 
              :class="{ 'can-send': canSend }"
              @click="sendMessage"
              :disabled="!canSend"
            >
              <Icon name="lucide:send" />
            </button>
          </div>
          
          <div class="input-status">
            <span class="char-count" :class="{ 'warning': isNearLimit }">
              {{ currentMessage.length }}/{{ messageMaxLength }}
            </span>
            <div v-if="isRecording" class="recording-status">
              <div class="recording-dot"></div>
              <span class="recording-time">{{ recordingTime }}</span>
            </div>
          </div>
        </div>
      </div>
    </Transition>
    
    <!-- Emoji选择器 -->
    <Teleport to="body">
      <div v-if="showEmojiPicker" class="emoji-picker-overlay" @click="closeEmojiPicker">
        <div class="emoji-picker" @click.stop>
          <div class="emoji-categories">
            <button 
              v-for="category in emojiCategories"
              :key="category.name"
              class="category-btn"
              :class="{ 'active': selectedEmojiCategory === category.name }"
              @click="selectEmojiCategory(category.name)"
            >
              {{ category.icon }}
            </button>
          </div>
          
          <div class="emoji-grid">
            <button 
              v-for="emoji in getCurrentEmojis()"
              :key="emoji"
              class="emoji-btn"
              @click="insertEmoji(emoji)"
            >
              {{ emoji }}
            </button>
          </div>
        </div>
      </div>
    </Teleport>
    
    <!-- 图片预览模态框 -->
    <Teleport to="body">
      <div v-if="showImagePreview" class="image-preview-modal" @click="closeImagePreview">
        <div class="image-preview-container" @click.stop>
          <button class="close-btn" @click="closeImagePreview">
            <Icon name="lucide:x" />
          </button>
          
          <img :src="previewImage.src" :alt="previewImage.alt" class="preview-image" />
          
          <div v-if="previewImage.images && previewImage.images.length > 1" class="image-navigation">
            <button 
              class="nav-btn prev" 
              @click="previousPreviewImage"
              :disabled="previewImage.index === 0"
            >
              <Icon name="lucide:chevron-left" />
            </button>
            
            <span class="image-counter">
              {{ previewImage.index + 1 }} / {{ previewImage.images.length }}
            </span>
            
            <button 
              class="nav-btn next" 
              @click="nextPreviewImage"
              :disabled="previewImage.index === previewImage.images.length - 1"
            >
              <Icon name="lucide:chevron-right" />
            </button>
          </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useChat4Store } from '~/stores/chat4'
// import HeartParticles from './HeartParticles.vue' // 将在下一步创建

// 接口定义
interface ChatMessage {
  id: string
  content: string
  timestamp: string
  isOwn: boolean
  type: 'text' | 'system' | 'gift' | 'scene_change' | 'audio' | 'image'
  senderName?: string
  avatar?: string
  images?: string[]
  audio?: string
  audioDuration?: string
  isPlaying?: boolean
  deliveryStatus?: 'sending' | 'sent' | 'delivered' | 'read'
  giftIcon?: string
  giftName?: string
  fromName?: string
  showHearts?: boolean
  action?: string
  actionText?: string
}

interface QuickReply {
  id: string
  text: string
  type?: string
}

interface Props {
  // 基础配置
  currentActor?: {
    id: string
    name: string
    avatar?: string
  }
  isOnline?: boolean
  showSenderName?: boolean
  
  // 功能开关
  enableVoice?: boolean
  enableImage?: boolean
  enableGift?: boolean
  enableEmoji?: boolean
  
  // 样式配置
  theme?: 'light' | 'dark' | 'auto'
  position?: 'bottom' | 'right' | 'center'
  maxHeight?: number
  
  // 消息配置
  messageMaxLength?: number
  autoScroll?: boolean
  showTimestamp?: boolean
  
  // 初始状态
  initialMinimized?: boolean
  initialMessages?: ChatMessage[]
}

// Props
const props = withDefaults(defineProps<Props>(), {
  isOnline: true,
  showSenderName: true,
  enableVoice: true,
  enableImage: true,
  enableGift: true,
  enableEmoji: true,
  theme: 'auto',
  position: 'bottom',
  maxHeight: 600,
  messageMaxLength: 500,
  autoScroll: true,
  showTimestamp: true,
  initialMinimized: false,
  initialMessages: () => []
})

// Emits
const emit = defineEmits<{
  'message-sent': [message: ChatMessage]
  'quick-reply': [reply: QuickReply]
  'gift-sent': [giftData: any]
  'scene-action': [action: string]
  'minimize-change': [minimized: boolean]
  'fullscreen-change': [fullscreen: boolean]
  'typing-start': []
  'typing-stop': []
}>()

// Store
const chat4Store = useChat4Store()

// 响应式数据
const isMinimized = ref(props.initialMinimized)
const isFullscreen = ref(false)
const currentMessage = ref('')
const messages = ref<ChatMessage[]>([...props.initialMessages])
const isTyping = ref(false)
const isRecording = ref(false)
const recordingTime = ref('0:00')
const showEmojiPicker = ref(false)
const selectedEmojiCategory = ref('smileys')
const showImagePreview = ref(false)
const previewImage = ref({
  src: '',
  alt: '',
  images: [] as string[],
  index: 0
})

// DOM引用
const messagesContainer = ref<HTMLElement>()
const messageInput = ref<HTMLTextAreaElement>()

// 历史消息加载
const hasMoreHistory = ref(true)
const isLoadingHistory = ref(false)

// 快捷回复
const quickReplies = ref<QuickReply[]>([
  { id: '1', text: '好的 😊' },
  { id: '2', text: '我知道了' },
  { id: '3', text: '谢谢你' },
  { id: '4', text: '等等我' },
])

// Emoji数据
const emojiCategories = [
  { name: 'smileys', icon: '😊', emojis: ['😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '🙃', '😉', '😊', '😇', '🥰', '😍', '🤩', '😘', '😗', '😚', '😙'] },
  { name: 'gestures', icon: '👋', emojis: ['👋', '🤚', '🖐️', '✋', '🖖', '👌', '🤏', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇', '☝️', '👍', '👎'] },
  { name: 'hearts', icon: '❤️', emojis: ['❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔', '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '♥️'] },
  { name: 'activities', icon: '⚽', emojis: ['⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱', '🪀', '🏓', '🏸', '🏒', '🏑', '🥍', '🏏', '🪃', '🥅', '⛳'] },
]

// 计算属性
const canSend = computed(() => {
  return currentMessage.value.trim().length > 0 && !isRecording.value
})

const isNearLimit = computed(() => {
  return currentMessage.value.length > props.messageMaxLength * 0.8
})

const inputPlaceholder = computed(() => {
  if (isRecording.value) return '正在录音...'
  return '输入消息...'
})

// 方法
const toggleMinimize = () => {
  isMinimized.value = !isMinimized.value
  emit('minimize-change', isMinimized.value)
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  emit('fullscreen-change', isFullscreen.value)
}

const openSettings = () => {
  // 打开设置面板
  console.log('Open chat settings')
}

const getStatusText = () => {
  if (isTyping.value) return '正在输入...'
  return props.isOnline ? '在线' : '离线'
}

const shouldShowTimestamp = (message: ChatMessage) => {
  if (!props.showTimestamp) return false
  
  const messageIndex = messages.value.findIndex(m => m.id === message.id)
  if (messageIndex === 0) return true
  
  const prevMessage = messages.value[messageIndex - 1]
  const currentTime = new Date(message.timestamp).getTime()
  const prevTime = new Date(prevMessage.timestamp).getTime()
  
  return currentTime - prevTime > 5 * 60 * 1000 // 5分钟间隔显示时间戳
}

const formatTimestamp = (timestamp: string) => {
  const date = new Date(timestamp)
  const now = new Date()
  
  if (date.toDateString() === now.toDateString()) {
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else {
    return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' })
  }
}

const formatMessageTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
}

const getBubbleClass = (message: ChatMessage) => {
  return {
    'own-bubble': message.isOwn,
    'other-bubble': !message.isOwn,
    'has-images': message.images?.length > 0,
    'has-audio': !!message.audio
  }
}

const getDeliveryIcon = (status: string) => {
  switch (status) {
    case 'sending': return 'lucide:clock'
    case 'sent': return 'lucide:check'
    case 'delivered': return 'lucide:check-check'
    case 'read': return 'lucide:check-check'
    default: return 'lucide:clock'
  }
}

const getDeliveryClass = (status: string) => {
  return {
    'delivery-sending': status === 'sending',
    'delivery-sent': status === 'sent',
    'delivery-delivered': status === 'delivered',
    'delivery-read': status === 'read'
  }
}

const handleSceneAction = (action: string) => {
  emit('scene-action', action)
}

const sendMessage = async () => {
  if (!canSend.value) return
  
  const messageText = currentMessage.value.trim()
  const newMessage: ChatMessage = {
    id: Date.now().toString(),
    content: messageText,
    timestamp: new Date().toISOString(),
    isOwn: true,
    type: 'text',
    deliveryStatus: 'sending'
  }
  
  messages.value.push(newMessage)
  currentMessage.value = ''
  
  // 发送消息事件
  emit('message-sent', newMessage)
  
  // 滚动到底部
  if (props.autoScroll) {
    await nextTick()
    scrollToBottom()
  }
  
  // 模拟消息状态更新
  setTimeout(() => {
    newMessage.deliveryStatus = 'sent'
  }, 1000)
  
  setTimeout(() => {
    newMessage.deliveryStatus = 'delivered'
  }, 2000)
}

const sendQuickReply = (reply: QuickReply) => {
  currentMessage.value = reply.text
  emit('quick-reply', reply)
  sendMessage()
}

const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

const handleInput = () => {
  // 自动调整textarea高度
  if (messageInput.value) {
    messageInput.value.style.height = 'auto'
    messageInput.value.style.height = Math.min(messageInput.value.scrollHeight, 120) + 'px'
  }
  
  // 输入状态管理
  if (!isTyping.value && currentMessage.value.length > 0) {
    isTyping.value = true
    emit('typing-start')
  }
  
  // 清除之前的计时器
  if (window.typingTimer) {
    clearTimeout(window.typingTimer)
  }
  
  // 设置新的计时器
  window.typingTimer = setTimeout(() => {
    isTyping.value = false
    emit('typing-stop')
  }, 1000)
}

const handleInputFocus = () => {
  // 输入框获得焦点时的处理
}

const handleInputBlur = () => {
  // 输入框失去焦点时的处理
}

const handleScroll = () => {
  if (!messagesContainer.value) return
  
  const { scrollTop } = messagesContainer.value
  if (scrollTop === 0 && hasMoreHistory.value && !isLoadingHistory.value) {
    // 到达顶部，可以加载更多历史消息
  }
}

const loadMoreHistory = async () => {
  if (isLoadingHistory.value) return
  
  isLoadingHistory.value = true
  // 模拟加载历史消息
  setTimeout(() => {
    // 添加一些模拟的历史消息
    const historyMessages: ChatMessage[] = [
      {
        id: `history-${Date.now()}`,
        content: '这是历史消息',
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        isOwn: false,
        type: 'text',
        senderName: props.currentActor?.name
      }
    ]
    
    messages.value.unshift(...historyMessages)
    isLoadingHistory.value = false
    
    // 如果没有更多历史消息了
    if (messages.value.length > 50) {
      hasMoreHistory.value = false
    }
  }, 1000)
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// Emoji相关方法
const selectEmoji = () => {
  showEmojiPicker.value = !showEmojiPicker.value
}

const closeEmojiPicker = () => {
  showEmojiPicker.value = false
}

const selectEmojiCategory = (category: string) => {
  selectedEmojiCategory.value = category
}

const getCurrentEmojis = () => {
  const category = emojiCategories.find(c => c.name === selectedEmojiCategory.value)
  return category ? category.emojis : []
}

const insertEmoji = (emoji: string) => {
  currentMessage.value += emoji
  showEmojiPicker.value = false
  
  // 重新聚焦到输入框
  nextTick(() => {
    messageInput.value?.focus()
  })
}

// 图片相关方法
const selectImage = () => {
  // 创建文件选择器
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'image/*'
  input.multiple = true
  
  input.onchange = (event) => {
    const files = (event.target as HTMLInputElement).files
    if (files) {
      handleImageUpload(Array.from(files))
    }
  }
  
  input.click()
}

const handleImageUpload = async (files: File[]) => {
  // 模拟图片上传
  const imageUrls = files.map((_, index) => 
    `https://picsum.photos/300/200?random=${Date.now() + index}`
  )
  
  const imageMessage: ChatMessage = {
    id: Date.now().toString(),
    content: '',
    timestamp: new Date().toISOString(),
    isOwn: true,
    type: 'image',
    images: imageUrls,
    deliveryStatus: 'sending'
  }
  
  messages.value.push(imageMessage)
  emit('message-sent', imageMessage)
  
  if (props.autoScroll) {
    await nextTick()
    scrollToBottom()
  }
}

const previewImage = (src: string, images: string[], index: number) => {
  previewImage.value = {
    src,
    alt: `Image ${index + 1}`,
    images,
    index
  }
  showImagePreview.value = true
}

const closeImagePreview = () => {
  showImagePreview.value = false
}

const previousPreviewImage = () => {
  if (previewImage.value.index > 0) {
    previewImage.value.index--
    previewImage.value.src = previewImage.value.images[previewImage.value.index]
  }
}

const nextPreviewImage = () => {
  if (previewImage.value.index < previewImage.value.images.length - 1) {
    previewImage.value.index++
    previewImage.value.src = previewImage.value.images[previewImage.value.index]
  }
}

// 语音相关方法
const startVoiceRecord = async () => {
  if (isRecording.value) {
    stopVoiceRecord()
    return
  }
  
  try {
    // 请求麦克风权限
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
    isRecording.value = true
    
    // 开始录音计时
    let seconds = 0
    const timer = setInterval(() => {
      seconds++
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      recordingTime.value = `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
    }, 1000)
    
    // 模拟录音停止
    setTimeout(() => {
      clearInterval(timer)
      stopVoiceRecord()
    }, 5000)
    
  } catch (error) {
    console.error('Failed to start recording:', error)
  }
}

const stopVoiceRecord = () => {
  isRecording.value = false
  recordingTime.value = '0:00'
  
  // 创建语音消息
  const audioMessage: ChatMessage = {
    id: Date.now().toString(),
    content: '',
    timestamp: new Date().toISOString(),
    isOwn: true,
    type: 'audio',
    audio: 'mock-audio-url',
    audioDuration: recordingTime.value,
    deliveryStatus: 'sending'
  }
  
  messages.value.push(audioMessage)
  emit('message-sent', audioMessage)
  
  if (props.autoScroll) {
    nextTick(() => scrollToBottom())
  }
}

const playAudio = (audioUrl: string) => {
  // 播放语音消息
  console.log('Play audio:', audioUrl)
}

const openGiftDrawer = () => {
  emit('gift-sent', { type: 'open_drawer' })
}

// 添加模拟消息的方法（用于测试）
const addMockMessage = (content: string, isOwn = false, type: ChatMessage['type'] = 'text') => {
  const message: ChatMessage = {
    id: Date.now().toString(),
    content,
    timestamp: new Date().toISOString(),
    isOwn,
    type,
    senderName: isOwn ? undefined : props.currentActor?.name,
    avatar: isOwn ? undefined : props.currentActor?.avatar
  }
  
  messages.value.push(message)
  
  if (props.autoScroll) {
    nextTick(() => scrollToBottom())
  }
}

// 暴露方法给父组件
defineExpose({
  addMessage: addMockMessage,
  scrollToBottom,
  focus: () => messageInput.value?.focus(),
  minimize: () => { isMinimized.value = true },
  maximize: () => { isMinimized.value = false },
  toggleMinimize
})

// 生命周期
onMounted(() => {
  // 初始滚动到底部
  if (props.autoScroll && messages.value.length > 0) {
    nextTick(() => scrollToBottom())
  }
})

onUnmounted(() => {
  // 清理定时器
  if (window.typingTimer) {
    clearTimeout(window.typingTimer)
  }
})

// 监听器
watch(() => messages.value.length, () => {
  if (props.autoScroll && !isMinimized.value) {
    nextTick(() => scrollToBottom())
  }
})

// 添加一些模拟数据用于演示
onMounted(() => {
  if (messages.value.length === 0) {
    setTimeout(() => {
      addMockMessage('你好！欢迎来到聊天室 😊', false)
    }, 1000)
    
    setTimeout(() => {
      addMockMessage('很高兴见到你！', false)
    }, 2000)
  }
})
</script>

<style scoped>
.chat-interface {
  position: relative;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.chat-interface.minimized {
  height: 60px;
}

.chat-interface.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  border-radius: 0;
  z-index: 1000;
}

/* 聊天头部 */
.chat-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 20px;
  cursor: pointer;
  user-select: none;
}

.chat-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.avatar-container {
  position: relative;
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-fallback {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.online-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ccc;
  border: 2px solid white;
  transition: background-color 0.3s ease;
}

.online-indicator.online {
  background: #4ade80;
}

.chat-info {
  display: flex;
  flex-direction: column;
}

.name {
  font-size: 16px;
  font-weight: 600;
  line-height: 1.2;
}

.status {
  font-size: 12px;
  opacity: 0.8;
  line-height: 1.2;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

/* 聊天内容区 */
.chat-content {
  display: flex;
  flex-direction: column;
  height: 500px;
  max-height: 70vh;
}

.chat-content-enter-active,
.chat-content-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chat-content-enter-from,
.chat-content-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* 消息容器 */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  scroll-behavior: smooth;
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 加载更多 */
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #667eea;
  font-size: 14px;
  gap: 8px;
}

.load-more:hover {
  background: rgba(102, 126, 234, 0.2);
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(102, 126, 234, 0.3);
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 消息项 */
.message-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.message-item.own-message {
  align-items: flex-end;
}

.message-item.system-message,
.message-item.scene-change {
  align-items: center;
}

/* 时间戳分割线 */
.timestamp-divider {
  text-align: center;
  color: #666;
  font-size: 12px;
  background: rgba(0, 0, 0, 0.05);
  padding: 4px 12px;
  border-radius: 12px;
  margin: 8px auto;
  max-width: 150px;
}

/* 系统消息 */
.system-content {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(255, 193, 7, 0.1);
  color: #f59e0b;
  padding: 8px 12px;
  border-radius: 16px;
  font-size: 14px;
  max-width: 300px;
  text-align: center;
}

/* 场景切换消息 */
.scene-change-content {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  padding: 12px 16px;
  border-radius: 16px;
  font-size: 14px;
  max-width: 350px;
}

.scene-action-btn {
  background: #10b981;
  color: white;
  border: none;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.scene-action-btn:hover {
  background: #059669;
  transform: translateY(-1px);
}

/* 礼物消息 */
.gift-content {
  position: relative;
  background: linear-gradient(135deg, #ff6b6b, #ff8e53);
  color: white;
  padding: 16px;
  border-radius: 16px;
  max-width: 300px;
}

.gift-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.gift-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  object-fit: cover;
}

.gift-details {
  flex: 1;
}

.gift-name {
  font-weight: 600;
  font-size: 16px;
}

.gift-from {
  font-size: 12px;
  opacity: 0.9;
}

.gift-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* 文本消息 */
.text-message {
  display: flex;
  gap: 8px;
  max-width: 80%;
}

.message-item.own-message .text-message {
  flex-direction: row-reverse;
}

.message-avatar img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.message-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-width: 100%;
}

.sender-name {
  font-size: 12px;
  color: #666;
  margin-left: 12px;
}

.message-item.own-message .sender-name {
  margin-left: 0;
  margin-right: 12px;
  text-align: right;
}

.message-bubble {
  position: relative;
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;
  max-width: 100%;
}

.message-bubble.own-bubble {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-bottom-right-radius: 6px;
}

.message-bubble.other-bubble {
  background: #f1f3f4;
  color: #333;
  border-bottom-left-radius: 6px;
}

.message-text {
  line-height: 1.4;
  font-size: 14px;
  white-space: pre-wrap;
}

/* 图片消息 */
.message-images {
  display: grid;
  gap: 4px;
  margin-top: 8px;
}

.message-images:has(> :nth-child(1):nth-last-child(1)) {
  grid-template-columns: 1fr;
}

.message-images:has(> :nth-child(2)) {
  grid-template-columns: 1fr 1fr;
}

.message-images:has(> :nth-child(3)) {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

.message-image {
  width: 100%;
  max-width: 200px;
  height: 150px;
  object-fit: cover;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.message-image:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* 语音消息 */
.message-audio {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 200px;
}

.audio-play-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: inherit;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
}

.audio-play-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.audio-waveform {
  display: flex;
  align-items: center;
  gap: 2px;
  flex: 1;
}

.waveform-bar {
  width: 3px;
  height: 12px;
  background: currentColor;
  border-radius: 2px;
  opacity: 0.6;
  animation: wave 1.5s ease-in-out infinite;
}

.waveform-bar:nth-child(odd) {
  animation-delay: 0.1s;
}

.waveform-bar:nth-child(even) {
  animation-delay: 0.3s;
}

@keyframes wave {
  0%, 100% { transform: scaleY(0.5); }
  50% { transform: scaleY(1); }
}

.audio-duration {
  font-size: 12px;
  opacity: 0.8;
}

/* 消息状态 */
.message-status {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 4px;
  margin-top: 4px;
}

.message-time {
  font-size: 11px;
  opacity: 0.6;
}

.delivery-status {
  font-size: 12px;
  opacity: 0.7;
}

.delivery-status.delivery-read {
  color: #10b981;
  opacity: 1;
}

.delivery-status.delivery-delivered {
  color: #667eea;
  opacity: 1;
}

/* 正在输入指示器 */
.typing-indicator {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

.typing-avatar img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.typing-bubble {
  background: #f1f3f4;
  padding: 12px 16px;
  border-radius: 18px;
  border-bottom-left-radius: 6px;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #999;
  animation: typing 1.4s ease-in-out infinite;
}

.typing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% { opacity: 0.3; }
  30% { opacity: 1; }
}

/* 快捷回复 */
.quick-replies {
  display: flex;
  gap: 8px;
  padding: 12px 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  overflow-x: auto;
}

.quick-reply-btn {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 1px solid rgba(102, 126, 234, 0.3);
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-reply-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.5);
  transform: translateY(-1px);
}

/* 输入区域 */
.input-area {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.5);
}

.input-tools {
  display: flex;
  gap: 8px;
  padding: 12px 16px 8px;
}

.tool-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  background: transparent;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 18px;
}

.tool-btn:hover,
.tool-btn.active {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  transform: scale(1.05);
}

.tool-btn.recording {
  background: #ff4757;
  color: white;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.input-container {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  padding: 0 16px;
}

.message-input {
  flex: 1;
  min-height: 20px;
  max-height: 120px;
  padding: 10px 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  background: white;
  font-size: 14px;
  line-height: 1.4;
  resize: none;
  outline: none;
  transition: all 0.3s ease;
}

.message-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.send-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: #ccc;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
}

.send-btn.can-send {
  background: linear-gradient(135deg, #667eea, #764ba2);
  transform: scale(1);
}

.send-btn.can-send:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.send-btn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.input-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px 12px;
  font-size: 12px;
  color: #666;
}

.char-count.warning {
  color: #ff4757;
}

.recording-status {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #ff4757;
}

.recording-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ff4757;
  animation: pulse 2s ease-in-out infinite;
}

.recording-time {
  font-weight: 500;
}

/* Emoji选择器 */
.emoji-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.emoji-picker {
  background: white;
  border-radius: 16px;
  padding: 20px;
  max-width: 400px;
  width: 100%;
  max-height: 50vh;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.emoji-categories {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.category-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 8px;
  background: transparent;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-btn:hover,
.category-btn.active {
  background: rgba(102, 126, 234, 0.1);
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.emoji-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 8px;
  background: transparent;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emoji-btn:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: scale(1.1);
}

/* 图片预览模态框 */
.image-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.image-preview-container {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 12px;
}

.image-navigation {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-top: 20px;
  background: rgba(0, 0, 0, 0.5);
  padding: 12px 20px;
  border-radius: 24px;
}

.nav-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 18px;
}

.nav-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.image-counter {
  color: white;
  font-size: 14px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-interface {
    height: 100%;
    border-radius: 0;
  }
  
  .chat-content {
    height: calc(100vh - 60px);
    max-height: none;
  }
  
  .text-message {
    max-width: 90%;
  }
  
  .emoji-picker {
    max-height: 40vh;
  }
  
  .emoji-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}

@media (max-width: 480px) {
  .title-content {
    gap: 8px;
  }
  
  .avatar,
  .avatar-fallback {
    width: 32px;
    height: 32px;
  }
  
  .name {
    font-size: 15px;
  }
  
  .status {
    font-size: 11px;
  }
  
  .header-actions {
    gap: 4px;
  }
  
  .action-btn {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }
  
  .message-bubble {
    padding: 10px 12px;
  }
  
  .message-text {
    font-size: 13px;
  }
  
  .input-tools {
    padding: 8px 12px 6px;
  }
  
  .tool-btn {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }
  
  .input-container {
    padding: 0 12px;
  }
  
  .input-status {
    padding: 6px 12px 8px;
  }
}

/* 滚动条样式 */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: transparent;
}

.messages-container::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.emoji-grid::-webkit-scrollbar {
  width: 6px;
}

.emoji-grid::-webkit-scrollbar-track {
  background: transparent;
}

.emoji-grid::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}
</style>