<template>
  <div class="timer-container" :class="[`timer-${variant}`, { 'timer-warning': isWarning, 'timer-danger': isDanger }]">
    <!-- 圆形进度条模式 -->
    <div v-if="variant === 'circle'" class="circle-timer">
      <div class="circle-progress">
        <svg class="progress-ring" :width="size" :height="size">
          <!-- 背景圆环 -->
          <circle
            class="progress-ring-background"
            :cx="size / 2"
            :cy="size / 2"
            :r="radius"
            fill="transparent"
            :stroke-width="strokeWidth"
          />
          <!-- 进度圆环 -->
          <circle
            class="progress-ring-circle"
            :cx="size / 2"
            :cy="size / 2"
            :r="radius"
            fill="transparent"
            :stroke-width="strokeWidth"
            :stroke-dasharray="circumference"
            :stroke-dashoffset="strokeDashoffset"
            :class="{ 'animated': animated }"
          />
        </svg>
        
        <!-- 中心内容 -->
        <div class="circle-content">
          <div class="time-display">{{ formattedTime }}</div>
          <div v-if="label" class="time-label">{{ label }}</div>
        </div>
      </div>
      
      <!-- 控制按钮 -->
      <div v-if="showControls" class="timer-controls">
        <button class="control-btn" @click="toggleTimer" :disabled="isFinished">
          <Icon :name="isRunning ? 'lucide:pause' : 'lucide:play'" />
        </button>
        <button class="control-btn" @click="resetTimer">
          <Icon name="lucide:rotate-ccw" />
        </button>
      </div>
    </div>
    
    <!-- 线性进度条模式 -->
    <div v-else-if="variant === 'linear'" class="linear-timer">
      <div class="timer-header">
        <div class="time-info">
          <div class="time-display">{{ formattedTime }}</div>
          <div v-if="label" class="time-label">{{ label }}</div>
        </div>
        
        <div v-if="showControls" class="timer-controls">
          <button class="control-btn" @click="toggleTimer" :disabled="isFinished">
            <Icon :name="isRunning ? 'lucide:pause' : 'lucide:play'" />
          </button>
          <button class="control-btn" @click="resetTimer">
            <Icon name="lucide:rotate-ccw" />
          </button>
        </div>
      </div>
      
      <div class="progress-bar">
        <div 
          class="progress-fill" 
          :style="{ width: progressPercent + '%' }"
          :class="{ 'animated': animated }"
        ></div>
      </div>
    </div>
    
    <!-- 数字显示模式 -->
    <div v-else-if="variant === 'digital'" class="digital-timer">
      <div class="digital-display">
        <div class="time-digits">
          <span class="digit-group">{{ hours }}</span>
          <span class="separator" :class="{ 'blink': isRunning && showBlink }">:</span>
          <span class="digit-group">{{ minutes }}</span>
          <span class="separator" :class="{ 'blink': isRunning && showBlink }">:</span>
          <span class="digit-group">{{ seconds }}</span>
        </div>
        <div v-if="label" class="time-label">{{ label }}</div>
      </div>
      
      <div v-if="showControls" class="timer-controls">
        <button class="control-btn" @click="toggleTimer" :disabled="isFinished">
          <Icon :name="isRunning ? 'lucide:pause' : 'lucide:play'" />
        </button>
        <button class="control-btn" @click="resetTimer">
          <Icon name="lucide:rotate-ccw" />
        </button>
      </div>
    </div>
    
    <!-- 文本模式 -->
    <div v-else class="text-timer">
      <div class="time-content">
        <Icon v-if="icon" :name="icon" class="timer-icon" />
        <div class="time-text">
          <span class="time-display">{{ formattedTime }}</span>
          <span v-if="label" class="time-label">{{ label }}</span>
        </div>
      </div>
      
      <div v-if="showControls" class="timer-controls">
        <button class="control-btn" @click="toggleTimer" :disabled="isFinished">
          <Icon :name="isRunning ? 'lucide:pause' : 'lucide:play'" />
        </button>
        <button class="control-btn" @click="resetTimer">
          <Icon name="lucide:rotate-ccw" />
        </button>
      </div>
    </div>
    
    <!-- 警告和完成效果 -->
    <div v-if="showEffects" class="timer-effects">
      <!-- 警告脉冲 -->
      <div v-if="isWarning && pulseEffect" class="warning-pulse"></div>
      
      <!-- 完成庆祝 -->
      <Teleport to="body">
        <div v-if="showCelebration" class="celebration-overlay">
          <HeartParticles 
            ref="celebrationParticles"
            :auto-emit="true"
            :particle-count="20"
            :emit-rate="200"
            color="#ff6b6b"
            :custom-emojis="['🎉', '✨', '🎊', '🏆', '💫']"
            particle-type="emoji"
            :lifetime="[3000, 5000]"
            emit-from-edges
          />
          <div class="celebration-message">
            <h2>时间到！</h2>
            <p>{{ completionMessage }}</p>
          </div>
        </div>
      </Teleport>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import HeartParticles from './HeartParticles.vue'

interface Props {
  // 时间配置
  duration?: number // 总时长（毫秒）
  initialTime?: number // 初始时间（毫秒）
  countDown?: boolean // 倒计时模式
  autoStart?: boolean // 自动开始
  
  // 显示配置
  variant?: 'circle' | 'linear' | 'digital' | 'text'
  size?: number // 圆形计时器大小
  strokeWidth?: number // 圆环宽度
  label?: string // 标签文本
  icon?: string // 图标
  format?: 'HH:MM:SS' | 'MM:SS' | 'SS' | 'auto' // 时间格式
  
  // 功能配置
  showControls?: boolean // 显示控制按钮
  showEffects?: boolean // 显示效果
  animated?: boolean // 动画效果
  pulseEffect?: boolean // 脉冲效果
  
  // 警告配置
  warningTime?: number // 警告时间（毫秒）
  dangerTime?: number // 危险时间（毫秒）
  warningSound?: boolean // 警告声音
  
  // 完成配置
  completionMessage?: string // 完成消息
  showCelebration?: boolean // 显示庆祝效果
  autoReset?: boolean // 自动重置
  repeatCount?: number // 重复次数（-1为无限）
  
  // 样式配置
  theme?: 'default' | 'primary' | 'success' | 'warning' | 'danger'
  gradient?: boolean // 渐变效果
}

const props = withDefaults(defineProps<Props>(), {
  duration: 60000, // 1分钟
  initialTime: 0,
  countDown: true,
  autoStart: false,
  variant: 'circle',
  size: 120,
  strokeWidth: 8,
  label: '',
  icon: '',
  format: 'auto',
  showControls: true,
  showEffects: true,
  animated: true,
  pulseEffect: true,
  warningTime: 10000, // 10秒
  dangerTime: 5000, // 5秒
  warningSound: false,
  completionMessage: '倒计时结束',
  showCelebration: false,
  autoReset: false,
  repeatCount: 0,
  theme: 'default',
  gradient: true
})

// Emits
const emit = defineEmits<{
  'start': []
  'pause': []
  'resume': []
  'reset': []
  'finish': []
  'warning': []
  'danger': []
  'tick': [remainingTime: number]
  'repeat': [count: number]
}>()

// 响应式数据
const currentTime = ref(props.countDown ? props.duration : props.initialTime)
const isRunning = ref(false)
const isFinished = ref(false)
const intervalId = ref<number>()
const currentRepeat = ref(0)

// 庆祝效果
const showCelebrationEffect = ref(false)
const celebrationParticles = ref()

// 数字闪烁效果
const showBlink = ref(true)
const blinkIntervalId = ref<number>()

// 计算属性
const remainingTime = computed(() => {
  if (props.countDown) {
    return Math.max(0, currentTime.value)
  } else {
    return currentTime.value
  }
})

const progressPercent = computed(() => {
  if (props.countDown) {
    return ((props.duration - remainingTime.value) / props.duration) * 100
  } else {
    return (currentTime.value / props.duration) * 100
  }
})

const isWarning = computed(() => {
  return props.countDown && remainingTime.value <= props.warningTime && remainingTime.value > props.dangerTime
})

const isDanger = computed(() => {
  return props.countDown && remainingTime.value <= props.dangerTime && remainingTime.value > 0
})

// 圆形进度条相关
const radius = computed(() => (props.size - props.strokeWidth) / 2)
const circumference = computed(() => 2 * Math.PI * radius.value)
const strokeDashoffset = computed(() => {
  const progress = progressPercent.value / 100
  return props.countDown 
    ? circumference.value * progress
    : circumference.value * (1 - progress)
})

// 时间格式化
const formatTime = (time: number): { hours: string; minutes: string; seconds: string; formatted: string } => {
  const totalSeconds = Math.ceil(time / 1000)
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const seconds = totalSeconds % 60
  
  const pad = (num: number) => num.toString().padStart(2, '0')
  
  let formatted = ''
  if (props.format === 'HH:MM:SS' || (props.format === 'auto' && hours > 0)) {
    formatted = `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`
  } else if (props.format === 'MM:SS' || (props.format === 'auto' && hours === 0)) {
    formatted = `${pad(minutes)}:${pad(seconds)}`
  } else if (props.format === 'SS') {
    formatted = pad(totalSeconds)
  } else {
    formatted = `${pad(minutes)}:${pad(seconds)}`
  }
  
  return {
    hours: pad(hours),
    minutes: pad(minutes),
    seconds: pad(seconds),
    formatted
  }
}

const formattedTime = computed(() => formatTime(remainingTime.value).formatted)
const hours = computed(() => formatTime(remainingTime.value).hours)
const minutes = computed(() => formatTime(remainingTime.value).minutes)
const seconds = computed(() => formatTime(remainingTime.value).seconds)

// 计时器控制方法
const startTimer = () => {
  if (isRunning.value || isFinished.value) return
  
  isRunning.value = true
  emit('start')
  
  intervalId.value = window.setInterval(() => {
    if (props.countDown) {
      currentTime.value = Math.max(0, currentTime.value - 100)
    } else {
      currentTime.value += 100
    }
    
    emit('tick', remainingTime.value)
    
    // 检查警告状态
    if (isWarning.value && !isDanger.value) {
      emit('warning')
      if (props.warningSound) {
        playWarningSound()
      }
    }
    
    if (isDanger.value) {
      emit('danger')
    }
    
    // 检查完成条件
    if ((props.countDown && currentTime.value <= 0) || 
        (!props.countDown && currentTime.value >= props.duration)) {
      finishTimer()
    }
  }, 100)
  
  // 启动闪烁效果
  if (props.variant === 'digital') {
    startBlinkEffect()
  }
}

const pauseTimer = () => {
  if (!isRunning.value) return
  
  isRunning.value = false
  if (intervalId.value) {
    clearInterval(intervalId.value)
  }
  stopBlinkEffect()
  emit('pause')
}

const resumeTimer = () => {
  if (isRunning.value || isFinished.value) return
  
  emit('resume')
  startTimer()
}

const toggleTimer = () => {
  if (isRunning.value) {
    pauseTimer()
  } else if (isFinished.value) {
    resetTimer()
  } else {
    startTimer()
  }
}

const resetTimer = () => {
  pauseTimer()
  currentTime.value = props.countDown ? props.duration : props.initialTime
  isFinished.value = false
  showCelebrationEffect.value = false
  emit('reset')
}

const finishTimer = () => {
  pauseTimer()
  isFinished.value = true
  emit('finish')
  
  // 显示庆祝效果
  if (props.showCelebration) {
    showCelebrationEffect.value = true
    if (celebrationParticles.value) {
      celebrationParticles.value.burst()
    }
    
    // 3秒后隐藏庆祝效果
    setTimeout(() => {
      showCelebrationEffect.value = false
    }, 3000)
  }
  
  // 处理重复
  if (props.repeatCount !== 0) {
    currentRepeat.value++
    emit('repeat', currentRepeat.value)
    
    if (props.repeatCount === -1 || currentRepeat.value < props.repeatCount) {
      setTimeout(() => {
        resetTimer()
        if (props.autoStart) {
          startTimer()
        }
      }, props.autoReset ? 1000 : 0)
    }
  } else if (props.autoReset) {
    setTimeout(resetTimer, 1000)
  }
}

// 闪烁效果
const startBlinkEffect = () => {
  blinkIntervalId.value = window.setInterval(() => {
    showBlink.value = !showBlink.value
  }, 500)
}

const stopBlinkEffect = () => {
  if (blinkIntervalId.value) {
    clearInterval(blinkIntervalId.value)
  }
  showBlink.value = true
}

// 警告声音
const playWarningSound = () => {
  try {
    const audio = new Audio()
    audio.src = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFApGn+DyvmUeCSGS1/LNeSsFJHfG8N2QQAoUXrTp66hVFA'
    audio.play().catch(() => {
      // 静默处理音频播放失败
    })
  } catch (error) {
    // 静默处理音频创建失败
  }
}

// 暴露方法给父组件
defineExpose({
  start: startTimer,
  pause: pauseTimer,
  resume: resumeTimer,
  reset: resetTimer,
  toggle: toggleTimer,
  getRemainingTime: () => remainingTime.value,
  getProgress: () => progressPercent.value,
  isRunning: () => isRunning.value,
  isFinished: () => isFinished.value
})

// 监听器
watch(() => props.autoStart, (newVal) => {
  if (newVal && !isRunning.value && !isFinished.value) {
    startTimer()
  }
})

// 生命周期
onMounted(() => {
  if (props.autoStart) {
    startTimer()
  }
})

onUnmounted(() => {
  pauseTimer()
  stopBlinkEffect()
})
</script>

<style scoped>
.timer-container {
  position: relative;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 圆形计时器 */
.circle-timer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.circle-progress {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-ring {
  transform: rotate(-90deg);
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.1));
}

.progress-ring-background {
  stroke: rgba(0, 0, 0, 0.1);
  stroke-linecap: round;
}

.progress-ring-circle {
  stroke: #667eea;
  stroke-linecap: round;
  transition: stroke-dashoffset 0.3s ease;
}

.progress-ring-circle.animated {
  transition: stroke-dashoffset 0.1s linear;
}

.circle-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
}

.time-display {
  font-size: 18px;
  font-weight: 700;
  color: #333;
  line-height: 1;
  font-variant-numeric: tabular-nums;
}

.time-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  font-weight: 500;
}

/* 线性计时器 */
.linear-timer {
  width: 100%;
  max-width: 300px;
}

.timer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.time-info {
  display: flex;
  flex-direction: column;
}

.linear-timer .time-display {
  font-size: 20px;
  font-weight: 700;
  color: #333;
  font-variant-numeric: tabular-nums;
}

.linear-timer .time-label {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-fill.animated {
  transition: width 0.1s linear;
}

/* 数字显示计时器 */
.digital-timer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.digital-display {
  background: #1a1a1a;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.3);
}

.time-digits {
  display: flex;
  align-items: center;
  font-family: 'Courier New', monospace;
  font-size: 32px;
  font-weight: 700;
  color: #00ff88;
  text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
  letter-spacing: 2px;
}

.digit-group {
  min-width: 2ch;
  text-align: center;
}

.separator {
  margin: 0 4px;
  opacity: 1;
  transition: opacity 0.2s ease;
}

.separator.blink {
  animation: blink 1s ease-in-out infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

.digital-timer .time-label {
  color: #00ff88;
  font-size: 12px;
  text-align: center;
  margin-top: 4px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* 文本计时器 */
.text-timer {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.time-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.timer-icon {
  font-size: 20px;
  color: #667eea;
}

.time-text {
  display: flex;
  flex-direction: column;
}

.text-timer .time-display {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  font-variant-numeric: tabular-nums;
}

.text-timer .time-label {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

/* 控制按钮 */
.timer-controls {
  display: flex;
  gap: 8px;
}

.control-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.control-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
}

.control-btn:active {
  transform: translateY(0);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* 状态样式 */
.timer-warning {
  animation: warning-pulse 2s ease-in-out infinite;
}

.timer-danger {
  animation: danger-pulse 1s ease-in-out infinite;
}

@keyframes warning-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

@keyframes danger-pulse {
  0%, 100% { transform: scale(1); }
  25%, 75% { transform: scale(1.05); }
  50% { transform: scale(1.02); }
}

/* 警告颜色 */
.timer-warning .progress-ring-circle,
.timer-warning .progress-fill {
  stroke: #f59e0b;
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.timer-danger .progress-ring-circle,
.timer-danger .progress-fill {
  stroke: #ef4444;
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

/* 警告脉冲效果 */
.warning-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120%;
  height: 120%;
  border: 2px solid #f59e0b;
  border-radius: 50%;
  animation: pulse-ring 2s ease-out infinite;
}

@keyframes pulse-ring {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0;
  }
}

/* 庆祝效果 */
.celebration-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  animation: celebration-appear 0.5s ease-out;
}

.celebration-message {
  text-align: center;
  color: white;
  z-index: 1001;
}

.celebration-message h2 {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
  animation: celebration-bounce 0.6s ease-out;
}

.celebration-message p {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
  animation: celebration-fade-in 0.8s ease-out 0.2s both;
}

@keyframes celebration-appear {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes celebration-bounce {
  0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
  40%, 43% { transform: translateY(-20px); }
  70% { transform: translateY(-10px); }
  90% { transform: translateY(-4px); }
}

@keyframes celebration-fade-in {
  0% { opacity: 0; transform: translateY(20px); }
  100% { opacity: 0.9; transform: translateY(0); }
}

/* 主题变体 */
.timer-primary .progress-ring-circle,
.timer-primary .progress-fill {
  stroke: #3b82f6;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.timer-primary .control-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.timer-success .progress-ring-circle,
.timer-success .progress-fill {
  stroke: #10b981;
  background: linear-gradient(90deg, #10b981, #059669);
}

.timer-success .control-btn {
  background: linear-gradient(135deg, #10b981, #059669);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .circle-timer {
    gap: 12px;
  }
  
  .time-display {
    font-size: 16px;
  }
  
  .linear-timer .time-display {
    font-size: 18px;
  }
  
  .time-digits {
    font-size: 28px;
  }
  
  .control-btn {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .time-display {
    font-size: 14px;
  }
  
  .linear-timer .time-display {
    font-size: 16px;
  }
  
  .time-digits {
    font-size: 24px;
  }
  
  .text-timer {
    padding: 10px 12px;
    gap: 12px;
  }
  
  .celebration-message h2 {
    font-size: 24px;
  }
  
  .celebration-message p {
    font-size: 14px;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .progress-ring-background {
    stroke: rgba(0, 0, 0, 0.3);
  }
  
  .time-display {
    color: #000;
  }
  
  .time-label {
    color: #333;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .progress-ring-circle,
  .progress-fill,
  .control-btn {
    transition: none;
  }
  
  .timer-warning,
  .timer-danger,
  .warning-pulse,
  .celebration-message h2,
  .celebration-message p {
    animation: none;
  }
  
  .separator.blink {
    animation: none;
    opacity: 1;
  }
}
</style>