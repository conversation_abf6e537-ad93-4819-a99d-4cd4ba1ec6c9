<template>
  <div 
    class="danmaku-container" 
    ref="containerRef"
    :class="{ 
      'danmaku-disabled': !enabled,
      'danmaku-paused': isPaused 
    }"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- 弹幕项 -->
    <div
      v-for="danmaku in visibleDanmaku"
      :key="danmaku.id"
      class="danmaku-item"
      :class="[
        `danmaku-${danmaku.type}`,
        { 'danmaku-paused-item': danmaku.isPaused }
      ]"
      :style="danmaku.style"
      :data-type="danmaku.type"
      @click="handleDanmakuClick(danmaku)"
      @mouseenter="handleDanmakuHover(danmaku)"
    >
      <!-- VIP标识 -->
      <span v-if="danmaku.type === 'vip'" class="vip-prefix">👑</span>
      
      <!-- 礼物图标 -->
      <span v-if="danmaku.type === 'gift'" class="gift-prefix">🎁</span>
      
      <!-- 关注图标 -->
      <span v-if="danmaku.type === 'follow'" class="follow-prefix">💖</span>
      
      <!-- 弹幕内容 -->
      <span class="danmaku-text">{{ danmaku.content }}</span>
      
      <!-- 特殊效果 -->
      <div v-if="showSpecialEffects && danmaku.type !== 'normal'" class="danmaku-effects">
        <div class="sparkle-effect"></div>
      </div>
    </div>
    
    <!-- 控制面板 -->
    <div v-if="showControls" class="danmaku-controls">
      <button class="control-btn" @click="togglePause">
        <Icon :name="isPaused ? 'lucide:play' : 'lucide:pause'" />
      </button>
      <button class="control-btn" @click="clearAll">
        <Icon name="lucide:x" />
      </button>
      <button class="control-btn" @click="toggleEnabled">
        <Icon :name="enabled ? 'lucide:eye' : 'lucide:eye-off'" />
      </button>
    </div>
    
    <!-- 统计信息 -->
    <div v-if="showStats" class="danmaku-stats">
      <span>弹幕: {{ activeDanmaku.length }}</span>
      <span>FPS: {{ currentFPS }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { LiveComment } from '~/types/chat4'

interface Props {
  messages: LiveComment[]
  speed?: number // 弹幕速度 (px/s)
  maxLines?: number // 最大弹幕行数
  fontSize?: number // 字体大小
  enabled?: boolean // 是否启用弹幕
  opacity?: number // 弹幕透明度
  pauseOnHover?: boolean // 悬停暂停
  showSpecialEffects?: boolean // 显示特殊效果
  filterBadWords?: boolean // 过滤敏感词
  colorful?: boolean // 彩色弹幕
  maxLength?: number // 最大弹幕长度
  collision?: boolean // 碰撞检测
}

const props = withDefaults(defineProps<Props>(), {
  speed: 100,
  maxLines: 3,
  fontSize: 16,
  enabled: true,
  opacity: 0.9,
  pauseOnHover: false,
  showSpecialEffects: true,
  filterBadWords: true,
  colorful: true,
  maxLength: 30,
  collision: true
})

interface DanmakuItem {
  id: string
  content: string
  originalMessage: LiveComment
  style: {
    top: string
    left: string
    fontSize: string
    animationDuration: string
    animationName: string
    color: string
    opacity: string
    zIndex: string
  }
  startTime: number
  duration: number
  line: number
  width: number
  isPaused: boolean
  type: 'normal' | 'gift' | 'follow' | 'vip'
}

// Emits
const emit = defineEmits<{
  'danmaku-click': [danmaku: DanmakuItem]
  'danmaku-hover': [danmaku: DanmakuItem]
  'pause': []
  'resume': []
  'clear': []
}>()

const containerRef = ref<HTMLElement>()
const activeDanmaku = ref<DanmakuItem[]>([])
const danmakuLines = ref<number[]>([]) // 记录每行的占用情况
const isPaused = ref(false)
const showControls = ref(false)
const showStats = ref(false)
const currentFPS = ref(60)

// 彩色弹幕颜色池
const colorPool = [
  '#FFFFFF', '#FF6B6B', '#4ECDC4', '#45B7D1', 
  '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8',
  '#F7DC6F', '#BB8FCE', '#85C1E9', '#F8C471'
]

// 计算属性
const visibleDanmaku = computed(() => {
  if (!props.enabled) return []
  return activeDanmaku.value
})

// 工具函数
const getRandomColor = () => {
  if (!props.colorful) return '#FFFFFF'
  return colorPool[Math.floor(Math.random() * colorPool.length)]
}

const getDanmakuType = (message: LiveComment): DanmakuItem['type'] => {
  if (message.type === 'gift') return 'gift'
  if (message.type === 'follow') return 'follow'
  if (message.isVip) return 'vip'
  return 'normal'
}

const truncateContent = (content: string): string => {
  if (content.length <= props.maxLength) return content
  return content.substring(0, props.maxLength - 3) + '...'
}

const calculateTextWidth = (text: string, fontSize: number): number => {
  // 中文字符宽度约为字体大小，英文字符约为字体大小的0.6倍
  const chineseChars = text.match(/[\u4e00-\u9fa5]/g)?.length || 0
  const otherChars = text.length - chineseChars
  return chineseChars * fontSize + otherChars * fontSize * 0.6
}

// 初始化弹幕行
const initDanmakuLines = () => {
  danmakuLines.value = new Array(props.maxLines).fill(0)
}

// 获取可用的弹幕行
const getAvailableLine = (): number => {
  const now = Date.now()
  
  // 找到最早可用的行
  let minTime = Infinity
  let availableLine = 0
  
  for (let i = 0; i < danmakuLines.value.length; i++) {
    if (danmakuLines.value[i] <= now) {
      return i // 找到立即可用的行
    }
    
    if (danmakuLines.value[i] < minTime) {
      minTime = danmakuLines.value[i]
      availableLine = i
    }
  }
  
  return availableLine
}

// 创建弹幕项
const createDanmaku = (message: LiveComment) => {
  if (!containerRef.value || !props.enabled) return
  
  const containerWidth = containerRef.value.clientWidth
  const lineHeight = props.fontSize * 1.8
  const line = getAvailableLine()
  const type = getDanmakuType(message)
  const content = truncateContent(message.content)
  
  // 精确计算文本宽度
  const textWidth = calculateTextWidth(content, props.fontSize)
  const duration = (containerWidth + textWidth) / props.speed * 1000 // 转换为毫秒
  
  // 碰撞检测：如果启用了碰撞检测，需要检查是否会重叠
  if (props.collision) {
    const lineEndTime = danmakuLines.value[line]
    const currentTime = Date.now()
    if (lineEndTime > currentTime) {
      // 延迟发射，避免碰撞
      const delay = lineEndTime - currentTime
      setTimeout(() => createDanmaku(message), Math.min(delay, 2000))
      return
    }
  }
  
  // 更新行占用时间
  danmakuLines.value[line] = Date.now() + duration
  
  // 特殊类型的动画调整
  let animationName = 'danmaku-move'
  let fontSize = props.fontSize
  let zIndex = 10
  
  if (type === 'gift') {
    animationName = 'danmaku-move-gift'
    fontSize = props.fontSize * 1.2
    zIndex = 15
  } else if (type === 'follow') {
    fontSize = props.fontSize * 1.1
    zIndex = 12
  } else if (type === 'vip') {
    zIndex = 13
  }
  
  const danmaku: DanmakuItem = {
    id: message.id,
    content,
    originalMessage: message,
    style: {
      top: `${line * lineHeight + 10}px`,
      left: '100%',
      fontSize: `${fontSize}px`,
      animationDuration: `${duration}ms`,
      animationName,
      color: getRandomColor(),
      opacity: props.opacity.toString(),
      zIndex: zIndex.toString()
    },
    startTime: Date.now(),
    duration,
    line,
    width: textWidth,
    isPaused: false,
    type
  }
  
  activeDanmaku.value.push(danmaku)
  
  // 动画结束后移除弹幕
  setTimeout(() => {
    const index = activeDanmaku.value.findIndex(d => d.id === danmaku.id)
    if (index > -1) {
      activeDanmaku.value.splice(index, 1)
    }
  }, duration)
}

// 监听消息变化，创建新弹幕
watch(
  () => props.messages,
  (newMessages, oldMessages) => {
    if (newMessages.length > (oldMessages?.length || 0)) {
      // 获取新增的消息
      const newCount = newMessages.length - (oldMessages?.length || 0)
      const newDanmaku = newMessages.slice(-newCount)
      
      newDanmaku.forEach((message, index) => {
        // 错开发射时间，避免重叠
        setTimeout(() => {
          createDanmaku(message)
        }, index * 200)
      })
    }
  },
  { deep: true }
)

// 交互方法
const handleMouseEnter = () => {
  if (props.pauseOnHover) {
    pauseAll()
  }
}

const handleMouseLeave = () => {
  if (props.pauseOnHover) {
    resumeAll()
  }
}

const handleDanmakuClick = (danmaku: DanmakuItem) => {
  emit('danmaku-click', danmaku)
}

const handleDanmakuHover = (danmaku: DanmakuItem) => {
  emit('danmaku-hover', danmaku)
}

const togglePause = () => {
  if (isPaused.value) {
    resumeAll()
  } else {
    pauseAll()
  }
}

const pauseAll = () => {
  isPaused.value = true
  activeDanmaku.value.forEach(danmaku => {
    danmaku.isPaused = true
  })
  emit('pause')
}

const resumeAll = () => {
  isPaused.value = false
  activeDanmaku.value.forEach(danmaku => {
    danmaku.isPaused = false
  })
  emit('resume')
}

const clearAll = () => {
  activeDanmaku.value = []
  emit('clear')
}

const toggleEnabled = () => {
  // 这个应该通过父组件的prop来控制，这里只是触发事件
  emit('toggle-enabled')
}

// 清理过期弹幕
const cleanupExpiredDanmaku = () => {
  const now = Date.now()
  activeDanmaku.value = activeDanmaku.value.filter(
    danmaku => now - danmaku.startTime < danmaku.duration
  )
}

// 定期清理
let cleanupInterval: NodeJS.Timeout

onMounted(() => {
  initDanmakuLines()
  
  // 每秒清理一次过期弹幕
  cleanupInterval = setInterval(cleanupExpiredDanmaku, 1000)
})

onUnmounted(() => {
  if (cleanupInterval) {
    clearInterval(cleanupInterval)
  }
})

// 响应式调整
const handleResize = () => {
  // 窗口大小变化时重新计算
  initDanmakuLines()
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// FPS监控
let frameCount = 0
let lastFPSUpdate = Date.now()

const updateFPS = () => {
  frameCount++
  const now = Date.now()
  if (now - lastFPSUpdate >= 1000) {
    currentFPS.value = frameCount
    frameCount = 0
    lastFPSUpdate = now
  }
  requestAnimationFrame(updateFPS)
}

// 暴露方法给父组件
defineExpose({
  pause: pauseAll,
  resume: resumeAll,
  clear: clearAll,
  getDanmakuCount: () => activeDanmaku.value.length,
  isPaused: () => isPaused.value,
  toggleControls: () => { showControls.value = !showControls.value },
  toggleStats: () => { showStats.value = !showStats.value }
})

onMounted(() => {
  updateFPS()
})
</script>

<style scoped>
.danmaku-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: auto;
  z-index: 10;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.danmaku-container.danmaku-disabled {
  pointer-events: none;
  opacity: 0;
}

.danmaku-container.danmaku-paused .danmaku-item {
  animation-play-state: paused;
}

.danmaku-item {
  position: absolute;
  white-space: nowrap;
  color: white;
  font-weight: 600;
  text-shadow: 
    1px 1px 2px rgba(0, 0, 0, 0.8),
    -1px -1px 2px rgba(0, 0, 0, 0.8),
    1px -1px 2px rgba(0, 0, 0, 0.8),
    -1px 1px 2px rgba(0, 0, 0, 0.8);
  animation-timing-function: linear;
  animation-fill-mode: forwards;
  user-select: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
  pointer-events: auto;
}

.danmaku-item:hover {
  transform: scale(1.05);
  filter: brightness(1.2);
}

.danmaku-item.danmaku-paused-item {
  animation-play-state: paused;
}

/* 弹幕移动动画 */
@keyframes danmaku-move {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-calc(100vw + 100%));
  }
}

/* 不同颜色的弹幕 */
.danmaku-item:nth-child(4n+1) {
  color: #fff;
}

.danmaku-item:nth-child(4n+2) {
  color: var(--chat4-secondary-color);
}

.danmaku-item:nth-child(4n+3) {
  color: #ff6b6b;
}

.danmaku-item:nth-child(4n+4) {
  color: #4ecdc4;
}

/* 响应式字体大小 */
@media (max-width: 768px) {
  .danmaku-item {
    font-size: 14px !important;
    text-shadow: 
      1px 1px 1px rgba(0, 0, 0, 0.8),
      -1px -1px 1px rgba(0, 0, 0, 0.8);
  }
}

@media (max-width: 480px) {
  .danmaku-item {
    font-size: 12px !important;
  }
}

/* 弹幕类型样式 */
.danmaku-normal {
  /* 普通弹幕默认样式 */
}

.danmaku-vip {
  filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.6));
  font-weight: 700;
}

.danmaku-gift {
  color: #ffd700;
  font-size: 1.2em !important;
  animation-name: danmaku-move-gift;
  filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.8));
}

.danmaku-follow {
  color: #ff69b4;
  font-size: 1.1em !important;
  filter: drop-shadow(0 0 8px rgba(255, 105, 180, 0.6));
}

/* 前缀图标样式 */
.vip-prefix {
  filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.8));
  animation: crown-glow 2s ease-in-out infinite alternate;
}

.gift-prefix {
  animation: gift-bounce 1s ease-in-out infinite;
}

.follow-prefix {
  animation: heart-pulse 1.5s ease-in-out infinite;
}

.danmaku-text {
  flex: 1;
}

/* 特殊效果 */
.danmaku-effects {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.sparkle-effect {
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 1px, transparent 1px);
  background-size: 20px 20px;
  animation: sparkle 2s linear infinite;
}

/* 控制面板 */
.danmaku-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 8px;
  background: rgba(0, 0, 0, 0.5);
  padding: 8px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.control-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* 统计信息 */
.danmaku-stats {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  display: flex;
  gap: 12px;
  backdrop-filter: blur(10px);
}

/* 动画定义 */
@keyframes crown-glow {
  0% { filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.8)); }
  100% { filter: drop-shadow(0 0 8px rgba(255, 215, 0, 1)); }
}

@keyframes gift-bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-2px); }
}

@keyframes heart-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

@keyframes sparkle {
  0% { background-position: 0 0; }
  100% { background-position: 20px 20px; }
}

@keyframes danmaku-move-gift {
  0% {
    transform: translateX(0) scale(1);
  }
  20% {
    transform: translateX(-20vw) scale(1.1);
  }
  80% {
    transform: translateX(-80vw) scale(1.1);
  }
  100% {
    transform: translateX(-calc(100vw + 100%)) scale(1);
  }
}

/* 防止弹幕过密集的淡入效果 */
.danmaku-item {
  animation: danmaku-move linear, danmaku-fade-in 0.3s ease-out;
}

@keyframes danmaku-fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
</style>