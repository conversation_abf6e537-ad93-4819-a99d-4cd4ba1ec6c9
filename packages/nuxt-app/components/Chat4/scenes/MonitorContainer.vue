<template>
  <div class="monitor-container">
    <!-- 增强背景 -->
    <EnhancedBackground
      :video-url="backgroundVideoUrl"
      :image-url="backgroundImageUrl"
      :blur="isMonitoring"
      transition-mode="fade"
      :transition-duration="600"
    />
    
    <!-- 监控界面 -->
    <div class="monitor-interface">
      <!-- 顶部状态栏 -->
      <div class="status-bar">
        <div class="monitor-status">
          <div class="status-indicator" :class="monitorStatusClass">
            <Icon :name="statusIcon" />
            <span class="status-text">{{ statusText }}</span>
          </div>
          <div class="time-display">
            {{ currentTime }}
          </div>
        </div>
        
        <!-- 网络状态 -->
        <div class="network-status">
          <div class="signal-strength" :class="signalStrengthClass">
            <div class="signal-bar" v-for="i in 4" :key="i"></div>
          </div>
          <span class="network-type">{{ networkType }}</span>
        </div>
      </div>
      
      <!-- 监控画面区域 -->
      <div class="monitor-display">
        <!-- 主监控画面 -->
        <div class="main-monitor" :class="{ 'recording': isRecording }">
          <div class="monitor-frame">
            <!-- 角色画面 -->
            <div class="character-view">
              <div class="character-avatar">
                <img v-if="currentActor?.avatar" :src="currentActor.avatar" :alt="currentActor.name" />
                <div v-else class="avatar-fallback">
                  <Icon name="lucide:user" />
                </div>
              </div>
              
              <!-- 监控效果 -->
              <div class="monitor-effects" v-if="isMonitoring">
                <div class="scan-lines"></div>
                <div class="camera-overlay">
                  <div class="recording-dot" v-if="isRecording"></div>
                  <div class="timestamp">{{ recordingTime }}</div>
                </div>
              </div>
            </div>
            
            <!-- 画面信息 -->
            <div class="monitor-info">
              <div class="camera-label">CAM-01</div>
              <div class="resolution">1920x1080</div>
              <div class="fps">{{ currentFps }} FPS</div>
            </div>
          </div>
          
          <!-- 监控控制按钮 -->
          <div class="monitor-controls">
            <button 
              class="control-btn" 
              :class="{ active: isRecording }" 
              @click="toggleRecording"
            >
              <Icon :name="isRecording ? 'lucide:square' : 'lucide:circle'" />
              <span>{{ isRecording ? '停止录制' : '开始录制' }}</span>
            </button>
            
            <button class="control-btn" @click="takeSnapshot">
              <Icon name="lucide:camera" />
              <span>截图</span>
            </button>
            
            <button class="control-btn" @click="toggleZoom">
              <Icon :name="isZoomed ? 'lucide:zoom-out' : 'lucide:zoom-in'" />
              <span>{{ isZoomed ? '缩小' : '放大' }}</span>
            </button>
          </div>
        </div>
        
        <!-- 小画面监控 -->
        <div class="mini-monitors">
          <div 
            v-for="(camera, index) in cameras" 
            :key="camera.id"
            class="mini-monitor"
            :class="{ active: activeCameraId === camera.id }"
            @click="switchCamera(camera.id)"
          >
            <div class="mini-frame">
              <div class="mini-content">
                <Icon :name="camera.icon" />
                <span class="mini-label">{{ camera.label }}</span>
              </div>
              <div class="mini-status" :class="camera.status">
                <div class="status-dot"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 底部操作面板 -->
      <div class="operation-panel">
        <!-- 监控模式切换 -->
        <div class="mode-controls">
          <button 
            v-for="mode in monitorModes" 
            :key="mode.id"
            class="mode-btn"
            :class="{ active: currentMode === mode.id }"
            @click="switchMode(mode.id)"
          >
            <Icon :name="mode.icon" />
            <span>{{ mode.name }}</span>
          </button>
        </div>
        
        <!-- 快捷操作 -->
        <div class="quick-actions">
          <button class="action-btn" @click="toggleNightVision">
            <Icon :name="isNightVision ? 'lucide:eye-off' : 'lucide:eye'" />
            <span>{{ isNightVision ? '关闭夜视' : '开启夜视' }}</span>
          </button>
          
          <button class="action-btn" @click="showSettings">
            <Icon name="lucide:settings" />
            <span>设置</span>
          </button>
          
          <button class="action-btn end-monitor" @click="endMonitoring">
            <Icon name="lucide:power" />
            <span>结束监控</span>
          </button>
        </div>
      </div>
    </div>
    
    <!-- 互动元素 -->
    <div class="interaction-elements" v-if="isMonitoring">
      <!-- 检测到运动提示 -->
      <Transition name="slide-up">
        <div v-if="motionDetected" class="motion-alert">
          <Icon name="lucide:activity" />
          <span>检测到运动</span>
        </div>
      </Transition>
      
      <!-- 安全提示 -->
      <div class="security-tips" v-if="showSecurityTips">
        <div class="tip-item" v-for="tip in securityTips" :key="tip.id">
          <Icon :name="tip.icon" />
          <span>{{ tip.text }}</span>
        </div>
      </div>
    </div>
    
    <!-- 设置弹窗 -->
    <Transition name="fade">
      <div v-if="showSettingsModal" class="settings-modal-overlay" @click="hideSettings">
        <div class="settings-modal" @click.stop>
          <div class="modal-header">
            <h3>监控设置</h3>
            <button class="close-btn" @click="hideSettings">
              <Icon name="lucide:x" />
            </button>
          </div>
          
          <div class="modal-content">
            <!-- 画质设置 -->
            <div class="setting-group">
              <h4>画质设置</h4>
              <div class="setting-options">
                <label v-for="quality in qualityOptions" :key="quality.value">
                  <input 
                    type="radio" 
                    :value="quality.value" 
                    v-model="currentQuality"
                    @change="updateQuality"
                  />
                  <span>{{ quality.label }}</span>
                </label>
              </div>
            </div>
            
            <!-- 录制设置 -->
            <div class="setting-group">
              <h4>录制设置</h4>
              <div class="setting-item">
                <label>
                  <input type="checkbox" v-model="autoRecord" />
                  <span>自动录制</span>
                </label>
              </div>
              <div class="setting-item">
                <label>
                  <input type="checkbox" v-model="motionDetection" />
                  <span>运动检测</span>
                </label>
              </div>
            </div>
            
            <!-- 存储设置 -->
            <div class="setting-group">
              <h4>存储设置</h4>
              <div class="storage-info">
                <div class="storage-bar">
                  <div class="storage-used" :style="{ width: storageUsedPercent + '%' }"></div>
                </div>
                <span class="storage-text">已使用 {{ storageUsed }}GB / {{ storageTotal }}GB</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
    
    <!-- 状态提示 -->
    <Transition name="fade">
      <div v-if="showStatusTip" class="status-tip-overlay">
        <div class="status-tip">
          <Icon :name="statusTipIcon" />
          <span>{{ statusTipText }}</span>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
// 显式导入组件
import EnhancedBackground from '~/components/common/EnhancedBackground.vue'
import { useChat4State } from '~/composables/chat4/useChat4State'
import { useChat4Events } from '~/composables/chat4/useChat4Events'

// 监控状态类型
type MonitorStatus = 'offline' | 'connecting' | 'online' | 'recording' | 'error'
type MonitorMode = 'normal' | 'night' | 'motion' | 'security'

// 摄像头类型
interface Camera {
  id: string
  label: string
  icon: string
  status: 'online' | 'offline' | 'recording'
}

// 监控模式类型
interface MonitorModeOption {
  id: MonitorMode
  name: string
  icon: string
}

// Chat4 状态管理
const { state, sceneNavigation } = useChat4State()
const { currentActor } = storeToRefs(state)

// 事件管理
const { emit: emitEvent, interactionEvents } = useChat4Events()

// 监控状态
const monitorStatus = ref<MonitorStatus>('offline')
const isMonitoring = computed(() => ['online', 'recording'].includes(monitorStatus.value))
const isRecording = ref(false)
const isZoomed = ref(false)
const isNightVision = ref(false)

// 模式和设置
const currentMode = ref<MonitorMode>('normal')
const activeCameraId = ref('cam-01')
const currentQuality = ref('1080p')
const autoRecord = ref(false)
const motionDetection = ref(true)

// UI状态
const showSettingsModal = ref(false)
const showSecurityTips = ref(false)
const motionDetected = ref(false)
const showStatusTip = ref(false)
const statusTipText = ref('')
const statusTipIcon = ref('lucide:info')

// 背景状态
const backgroundVideoUrl = ref('')
const backgroundImageUrl = ref('https://picsum.photos/1920/1080?random=200')

// 时间显示
const currentTime = ref('')
const recordingTime = ref('00:00:00')
const currentFps = ref(30)

// 网络状态
const networkType = ref('WiFi')
const signalStrength = ref(4)

// 存储信息
const storageUsed = ref(45)
const storageTotal = ref(128)

// 定时器
let timeUpdateTimer: ReturnType<typeof setInterval> | null = null
let recordingTimer: ReturnType<typeof setInterval> | null = null
let recordingSeconds = 0

// 摄像头列表
const cameras: Camera[] = [
  { id: 'cam-01', label: 'CAM-01', icon: 'lucide:video', status: 'online' },
  { id: 'cam-02', label: 'CAM-02', icon: 'lucide:camera', status: 'online' },
  { id: 'cam-03', label: 'CAM-03', icon: 'lucide:webcam', status: 'offline' },
  { id: 'cam-04', label: 'CAM-04', icon: 'lucide:monitor', status: 'recording' }
]

// 监控模式选项
const monitorModes: MonitorModeOption[] = [
  { id: 'normal', name: '普通', icon: 'lucide:monitor' },
  { id: 'night', name: '夜视', icon: 'lucide:moon' },
  { id: 'motion', name: '运动', icon: 'lucide:activity' },
  { id: 'security', name: '安全', icon: 'lucide:shield' }
]

// 画质选项
const qualityOptions = [
  { value: '720p', label: '720P' },
  { value: '1080p', label: '1080P' },
  { value: '4k', label: '4K' }
]

// 安全提示
const securityTips = [
  { id: 1, icon: 'lucide:lock', text: '系统安全运行中' },
  { id: 2, icon: 'lucide:wifi', text: '网络连接正常' },
  { id: 3, icon: 'lucide:shield-check', text: '防护系统已启用' }
]

// 计算属性
const monitorStatusClass = computed(() => ({
  'offline': monitorStatus.value === 'offline',
  'connecting': monitorStatus.value === 'connecting',
  'online': monitorStatus.value === 'online',
  'recording': monitorStatus.value === 'recording',
  'error': monitorStatus.value === 'error'
}))

const statusIcon = computed(() => {
  switch (monitorStatus.value) {
    case 'offline': return 'lucide:wifi-off'
    case 'connecting': return 'lucide:loader'
    case 'online': return 'lucide:wifi'
    case 'recording': return 'lucide:record'
    case 'error': return 'lucide:alert-circle'
    default: return 'lucide:monitor'
  }
})

const statusText = computed(() => {
  switch (monitorStatus.value) {
    case 'offline': return '离线'
    case 'connecting': return '连接中'
    case 'online': return '在线'
    case 'recording': return '录制中'
    case 'error': return '错误'
    default: return '未知'
  }
})

const signalStrengthClass = computed(() => ({
  'strength-1': signalStrength.value === 1,
  'strength-2': signalStrength.value === 2,
  'strength-3': signalStrength.value === 3,
  'strength-4': signalStrength.value === 4
}))

const storageUsedPercent = computed(() => (storageUsed.value / storageTotal.value) * 100)

// 监控控制方法
const startMonitoring = async () => {
  monitorStatus.value = 'connecting'
  showStatusTip('正在连接监控系统...')
  
  setTimeout(() => {
    monitorStatus.value = 'online'
    hideStatusTip()
    startTimeUpdate()
    showStatusTip('监控系统已启动')
    setTimeout(hideStatusTip, 2000)
  }, 2000)
}

const endMonitoring = () => {
  if (isRecording.value) {
    stopRecording()
  }
  
  monitorStatus.value = 'offline'
  stopTimeUpdate()
  showStatusTip('监控已结束')
  setTimeout(() => {
    hideStatusTip()
    sceneNavigation.goToChat()
  }, 2000)
}

const toggleRecording = async () => {
  if (isRecording.value) {
    stopRecording()
  } else {
    startRecording()
  }
}

const startRecording = () => {
  isRecording.value = true
  monitorStatus.value = 'recording'
  recordingSeconds = 0
  
  recordingTimer = setInterval(() => {
    recordingSeconds++
    const hours = Math.floor(recordingSeconds / 3600)
    const minutes = Math.floor((recordingSeconds % 3600) / 60)
    const seconds = recordingSeconds % 60
    recordingTime.value = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }, 1000)
  
  showStatusTip('开始录制')
  setTimeout(hideStatusTip, 1500)
}

const stopRecording = () => {
  isRecording.value = false
  monitorStatus.value = 'online'
  
  if (recordingTimer) {
    clearInterval(recordingTimer)
    recordingTimer = null
  }
  
  recordingTime.value = '00:00:00'
  showStatusTip('录制已停止')
  setTimeout(hideStatusTip, 1500)
}

// 其他控制方法
const takeSnapshot = () => {
  showStatusTip('截图已保存')
  setTimeout(hideStatusTip, 1500)
}

const toggleZoom = () => {
  isZoomed.value = !isZoomed.value
  showStatusTip(isZoomed.value ? '已放大' : '已缩小')
  setTimeout(hideStatusTip, 1500)
}

const switchCamera = (cameraId: string) => {
  if (activeCameraId.value === cameraId) return
  
  activeCameraId.value = cameraId
  showStatusTip(`切换到 ${cameraId.toUpperCase()}`)
  setTimeout(hideStatusTip, 1500)
}

const switchMode = (mode: MonitorMode) => {
  if (currentMode.value === mode) return
  
  currentMode.value = mode
  
  // 处理特殊模式
  if (mode === 'night') {
    isNightVision.value = true
  } else {
    isNightVision.value = false
  }
  
  const modeName = monitorModes.find(m => m.id === mode)?.name || mode
  showStatusTip(`切换到${modeName}模式`)
  setTimeout(hideStatusTip, 1500)
}

const toggleNightVision = () => {
  isNightVision.value = !isNightVision.value
  showStatusTip(isNightVision.value ? '夜视模式已开启' : '夜视模式已关闭')
  setTimeout(hideStatusTip, 1500)
}

// 设置相关
const showSettings = () => {
  showSettingsModal.value = true
}

const hideSettings = () => {
  showSettingsModal.value = false
}

const updateQuality = () => {
  showStatusTip(`画质已设置为 ${currentQuality.value}`)
  setTimeout(hideStatusTip, 1500)
}

// 工具方法
const startTimeUpdate = () => {
  const updateTime = () => {
    const now = new Date()
    currentTime.value = now.toLocaleTimeString('zh-CN', { hour12: false })
  }
  
  updateTime()
  timeUpdateTimer = setInterval(updateTime, 1000)
}

const stopTimeUpdate = () => {
  if (timeUpdateTimer) {
    clearInterval(timeUpdateTimer)
    timeUpdateTimer = null
  }
}

const showStatusTip = (text: string, icon: string = 'lucide:info') => {
  statusTipText.value = text
  statusTipIcon.value = icon
  showStatusTip.value = true
}

const hideStatusTip = () => {
  showStatusTip.value = false
}

// 模拟运动检测
const simulateMotionDetection = () => {
  if (motionDetection.value && isMonitoring.value) {
    motionDetected.value = true
    setTimeout(() => {
      motionDetected.value = false
    }, 3000)
  }
}

// 生命周期
onMounted(() => {
  // 自动启动监控
  setTimeout(startMonitoring, 1000)
  
  // 显示安全提示
  setTimeout(() => {
    showSecurityTips.value = true
  }, 3000)
  
  // 模拟运动检测
  setTimeout(() => {
    simulateMotionDetection()
  }, 10000)
})

onBeforeUnmount(() => {
  stopTimeUpdate()
  if (recordingTimer) {
    clearInterval(recordingTimer)
  }
})
</script>

<style scoped>
.monitor-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background: #000;
  overflow: hidden;
  user-select: none;
  font-family: 'Courier New', monospace;
}

/* 监控界面 */
.monitor-interface {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  z-index: 2;
}

/* 状态栏 */
.status-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.9) 0%, transparent 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  z-index: 10;
  color: #00ff00;
}

.monitor-status {
  display: flex;
  align-items: center;
  gap: 20px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
}

.status-indicator.offline {
  color: #666;
}

.status-indicator.connecting {
  color: #fbbf24;
}

.status-indicator.online {
  color: #10b981;
}

.status-indicator.recording {
  color: #ef4444;
  animation: pulse 1s infinite;
}

.status-indicator.error {
  color: #ef4444;
}

.time-display {
  font-size: 16px;
  font-weight: 600;
  color: #00ff00;
}

.network-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #00ff00;
}

.signal-strength {
  display: flex;
  gap: 2px;
}

.signal-bar {
  width: 3px;
  height: 12px;
  background: #333;
  border-radius: 1px;
}

.signal-strength.strength-1 .signal-bar:nth-child(1) {
  background: #00ff00;
}

.signal-strength.strength-2 .signal-bar:nth-child(-n+2) {
  background: #00ff00;
}

.signal-strength.strength-3 .signal-bar:nth-child(-n+3) {
  background: #00ff00;
}

.signal-strength.strength-4 .signal-bar {
  background: #00ff00;
}

/* 监控显示区域 */
.monitor-display {
  flex: 1;
  display: flex;
  padding: 80px 20px 160px;
  gap: 20px;
}

.main-monitor {
  flex: 1;
  background: #111;
  border: 2px solid #333;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

.main-monitor.recording {
  border-color: #ef4444;
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
}

.monitor-frame {
  width: 100%;
  height: 100%;
  position: relative;
  background: #000;
}

.character-view {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.character-avatar {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #00ff00;
}

.character-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-fallback {
  width: 100%;
  height: 100%;
  background: rgba(0, 255, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 80px;
  color: #00ff00;
}

/* 监控效果 */
.monitor-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.scan-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: repeating-linear-gradient(
    0deg,
    transparent,
    transparent 2px,
    rgba(0, 255, 0, 0.03) 2px,
    rgba(0, 255, 0, 0.03) 4px
  );
  animation: scanlines 2s linear infinite;
}

@keyframes scanlines {
  0% { transform: translateY(0px); }
  100% { transform: translateY(4px); }
}

.camera-overlay {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
  color: #00ff00;
  font-size: 12px;
}

.recording-dot {
  width: 12px;
  height: 12px;
  background: #ef4444;
  border-radius: 50%;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.timestamp {
  background: rgba(0, 0, 0, 0.8);
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
}

/* 画面信息 */
.monitor-info {
  position: absolute;
  bottom: 20px;
  left: 20px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  color: #00ff00;
  font-size: 12px;
  background: rgba(0, 0, 0, 0.8);
  padding: 8px 12px;
  border-radius: 4px;
}

/* 监控控制按钮 */
.monitor-controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  gap: 8px;
}

.control-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid #333;
  border-radius: 6px;
  padding: 8px 12px;
  color: #00ff00;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background: rgba(0, 255, 0, 0.1);
  border-color: #00ff00;
}

.control-btn.active {
  background: rgba(239, 68, 68, 0.2);
  border-color: #ef4444;
  color: #ef4444;
}

/* 小画面监控 */
.mini-monitors {
  width: 120px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.mini-monitor {
  height: 80px;
  background: #111;
  border: 1px solid #333;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.mini-monitor:hover {
  border-color: #00ff00;
}

.mini-monitor.active {
  border-color: #00ff00;
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
}

.mini-frame {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #00ff00;
  font-size: 10px;
  position: relative;
}

.mini-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.mini-status {
  position: absolute;
  top: 4px;
  right: 4px;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #666;
}

.mini-status.online .status-dot {
  background: #10b981;
}

.mini-status.recording .status-dot {
  background: #ef4444;
  animation: pulse 1s infinite;
}

/* 操作面板 */
.operation-panel {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.9) 0%, transparent 100%);
  padding: 30px 20px;
  z-index: 10;
}

.mode-controls {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 20px;
}

.mode-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid #333;
  border-radius: 8px;
  padding: 12px 16px;
  color: #00ff00;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 60px;
}

.mode-btn:hover {
  background: rgba(0, 255, 0, 0.1);
  border-color: #00ff00;
}

.mode-btn.active {
  background: rgba(0, 255, 0, 0.2);
  border-color: #00ff00;
}

.quick-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid #333;
  border-radius: 8px;
  padding: 12px 16px;
  color: #00ff00;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 70px;
}

.action-btn:hover {
  background: rgba(0, 255, 0, 0.1);
  border-color: #00ff00;
}

.action-btn.end-monitor {
  border-color: #ef4444;
  color: #ef4444;
}

.action-btn.end-monitor:hover {
  background: rgba(239, 68, 68, 0.1);
  border-color: #ef4444;
}

/* 互动元素 */
.interaction-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 6;
}

.motion-alert {
  position: absolute;
  top: 100px;
  right: 20px;
  background: rgba(239, 68, 68, 0.9);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  backdrop-filter: blur(10px);
  animation: pulse 2s infinite;
}

.security-tips {
  position: absolute;
  left: 20px;
  top: 100px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tip-item {
  background: rgba(0, 0, 0, 0.8);
  color: #00ff00;
  border: 1px solid #333;
  border-radius: 6px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

/* 设置弹窗 */
.settings-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

.settings-modal {
  background: #111;
  border: 2px solid #333;
  border-radius: 12px;
  width: 400px;
  max-width: 90vw;
  color: #00ff00;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #333;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
}

.close-btn {
  background: none;
  border: none;
  color: #00ff00;
  cursor: pointer;
}

.modal-content {
  padding: 20px;
}

.setting-group {
  margin-bottom: 24px;
}

.setting-group h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #ccc;
}

.setting-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting-options label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.setting-item {
  margin-bottom: 8px;
}

.setting-item label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.storage-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.storage-bar {
  height: 8px;
  background: #333;
  border-radius: 4px;
  overflow: hidden;
}

.storage-used {
  height: 100%;
  background: #00ff00;
  transition: width 0.3s ease;
}

.storage-text {
  font-size: 12px;
  color: #ccc;
}

/* 状态提示 */
.status-tip-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 15;
}

.status-tip {
  background: rgba(0, 0, 0, 0.9);
  color: #00ff00;
  padding: 12px 20px;
  border-radius: 8px;
  border: 1px solid #333;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  backdrop-filter: blur(10px);
}

/* 动画 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.slide-up-enter-active {
  transition: all 0.3s ease;
}

.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(20px);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-bar {
    padding: 0 16px;
    font-size: 12px;
  }
  
  .monitor-display {
    padding: 70px 16px 140px;
    gap: 12px;
  }
  
  .mini-monitors {
    width: 80px;
  }
  
  .mini-monitor {
    height: 60px;
  }
  
  .character-avatar {
    width: 120px;
    height: 120px;
  }
  
  .mode-controls {
    gap: 8px;
    margin-bottom: 16px;
  }
  
  .mode-btn {
    padding: 8px 10px;
    min-width: 50px;
    font-size: 10px;
  }
  
  .quick-actions {
    gap: 12px;
  }
  
  .action-btn {
    padding: 8px 12px;
    min-width: 60px;
    font-size: 10px;
  }
  
  .settings-modal {
    width: 350px;
  }
}
</style>