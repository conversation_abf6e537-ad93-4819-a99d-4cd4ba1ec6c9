<template>
  <div class="moment-container">
    <!-- 增强背景 -->
    <EnhancedBackground
      :video-url="backgroundVideoUrl"
      :image-url="backgroundImageUrl"
      :blur="showModal"
      transition-mode="fade"
      :transition-duration="600"
    />
    
    <!-- 朋友圈界面 -->
    <div class="moment-interface">
      <!-- 顶部导航栏 -->
      <div class="top-navigation">
        <div class="nav-left">
          <button class="nav-btn" @click="goBack">
            <Icon name="lucide:arrow-left" />
          </button>
          <h1 class="page-title">朋友圈</h1>
        </div>
        
        <div class="nav-right">
          <button class="nav-btn" @click="showCreateMoment">
            <Icon name="lucide:plus" />
          </button>
          <button class="nav-btn" @click="refreshMoments">
            <Icon name="lucide:refresh-cw" />
          </button>
        </div>
      </div>
      
      <!-- 个人资料卡片 -->
      <div class="profile-header">
        <div class="profile-background">
          <img src="https://picsum.photos/400/200?random=700" alt="Profile Background" />
          <div class="profile-overlay"></div>
        </div>
        
        <div class="profile-content">
          <div class="profile-avatar">
            <img v-if="currentActor?.avatar" :src="currentActor.avatar" :alt="currentActor.name" />
            <div v-else class="avatar-fallback">
              <Icon name="lucide:user" />
            </div>
          </div>
          
          <div class="profile-info">
            <h2 class="profile-name">{{ currentActor?.name || 'User' }}</h2>
            <div class="profile-stats">
              <span class="stat-item">
                <Icon name="lucide:heart" />
                {{ totalLikes }} 获赞
              </span>
              <span class="stat-item">
                <Icon name="lucide:message-circle" />
                {{ totalComments }} 评论
              </span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 朋友圈内容 -->
      <div class="moments-feed">
        <div 
          v-for="moment in moments" 
          :key="moment.id"
          class="moment-card"
          :class="{ 'own-moment': moment.isOwn }"
        >
          <!-- 用户信息 -->
          <div class="moment-header">
            <div class="user-info">
              <div class="user-avatar">
                <img :src="moment.user.avatar" :alt="moment.user.name" />
              </div>
              <div class="user-details">
                <div class="user-name">{{ moment.user.name }}</div>
                <div class="moment-time">{{ formatTime(moment.timestamp) }}</div>
              </div>
            </div>
            
            <div class="moment-actions">
              <button v-if="moment.isOwn" class="action-btn" @click="editMoment(moment)">
                <Icon name="lucide:edit" />
              </button>
              <button class="action-btn" @click="showMomentMenu(moment)">
                <Icon name="lucide:more-horizontal" />
              </button>
            </div>
          </div>
          
          <!-- 动态内容 -->
          <div class="moment-content">
            <div v-if="moment.text" class="moment-text">{{ moment.text }}</div>
            
            <!-- 图片内容 -->
            <div v-if="moment.images.length > 0" class="moment-images">
              <div 
                class="image-grid"
                :class="{
                  'grid-1': moment.images.length === 1,
                  'grid-2': moment.images.length === 2,
                  'grid-3': moment.images.length === 3,
                  'grid-many': moment.images.length > 3
                }"
              >
                <div 
                  v-for="(image, index) in moment.images.slice(0, 9)" 
                  :key="index"
                  class="image-item"
                  @click="previewImage(moment.images, index)"
                >
                  <img :src="image" :alt="`Image ${index + 1}`" />
                  <div v-if="index === 8 && moment.images.length > 9" class="more-images">
                    +{{ moment.images.length - 9 }}
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 视频内容 -->
            <div v-if="moment.video" class="moment-video">
              <video :src="moment.video" controls playsinline />
            </div>
            
            <!-- 位置信息 -->
            <div v-if="moment.location" class="moment-location">
              <Icon name="lucide:map-pin" />
              <span>{{ moment.location.name }}</span>
            </div>
          </div>
          
          <!-- 互动统计 -->
          <div class="moment-stats">
            <div class="stats-left">
              <div v-if="moment.likes > 0" class="stat-item">
                <Icon name="lucide:heart" />
                <span>{{ moment.likes }}</span>
              </div>
              <div v-if="moment.comments.length > 0" class="stat-item">
                <Icon name="lucide:message-circle" />
                <span>{{ moment.comments.length }}</span>
              </div>
            </div>
            <div class="stats-right">
              <span v-if="moment.views > 0" class="view-count">{{ moment.views }} 次浏览</span>
            </div>
          </div>
          
          <!-- 互动按钮 -->
          <div class="moment-interactions">
            <button 
              class="interaction-btn"
              :class="{ 'liked': moment.isLiked }"
              @click="toggleLike(moment)"
            >
              <Icon :name="moment.isLiked ? 'lucide:heart' : 'lucide:heart'" 
                    :class="{ 'filled': moment.isLiked }" />
              <span>{{ moment.isLiked ? '已赞' : '点赞' }}</span>
            </button>
            
            <button class="interaction-btn" @click="showComments(moment)">
              <Icon name="lucide:message-circle" />
              <span>评论</span>
            </button>
            
            <button class="interaction-btn" @click="shareMoment(moment)">
              <Icon name="lucide:share" />
              <span>分享</span>
            </button>
          </div>
          
          <!-- 评论预览 -->
          <div v-if="moment.comments.length > 0" class="comments-preview">
            <div 
              v-for="comment in moment.comments.slice(0, 2)" 
              :key="comment.id"
              class="comment-item"
            >
              <span class="comment-user">{{ comment.user.name }}:</span>
              <span class="comment-text">{{ comment.text }}</span>
            </div>
            <button 
              v-if="moment.comments.length > 2" 
              class="view-all-comments"
              @click="showComments(moment)"
            >
              查看全部 {{ moment.comments.length }} 条评论
            </button>
          </div>
        </div>
        
        <!-- 加载更多 -->
        <div v-if="hasMore" class="load-more">
          <button class="load-more-btn" @click="loadMoreMoments" :disabled="isLoading">
            <Icon v-if="isLoading" name="lucide:loader" class="spinning" />
            <span>{{ isLoading ? '加载中...' : '查看更多' }}</span>
          </button>
        </div>
      </div>
    </div>
    
    <!-- 创建动态弹窗 -->
    <Transition name="slide-up">
      <div v-if="showCreateModal" class="create-modal-overlay" @click="hideCreateMoment">
        <div class="create-modal" @click.stop>
          <div class="modal-header">
            <h3>发表动态</h3>
            <button class="close-btn" @click="hideCreateMoment">
              <Icon name="lucide:x" />
            </button>
          </div>
          
          <div class="modal-content">
            <div class="create-form">
              <textarea 
                v-model="newMomentText"
                placeholder="分享你的想法..."
                class="moment-input"
                rows="4"
              ></textarea>
              
              <!-- 图片选择 -->
              <div class="media-selector">
                <div class="selected-images">
                  <div 
                    v-for="(image, index) in newMomentImages" 
                    :key="index"
                    class="selected-image"
                  >
                    <img :src="image" :alt="`Selected ${index + 1}`" />
                    <button class="remove-image" @click="removeImage(index)">
                      <Icon name="lucide:x" />
                    </button>
                  </div>
                  
                  <button 
                    v-if="newMomentImages.length < 9"
                    class="add-image-btn"
                    @click="addImage"
                  >
                    <Icon name="lucide:plus" />
                  </button>
                </div>
              </div>
              
              <!-- 位置选择 -->
              <div class="location-selector">
                <button class="location-btn" @click="selectLocation">
                  <Icon name="lucide:map-pin" />
                  <span>{{ newMomentLocation?.name || '添加位置' }}</span>
                </button>
              </div>
              
              <!-- 隐私设置 -->
              <div class="privacy-selector">
                <label>谁可以看到</label>
                <select v-model="newMomentPrivacy" class="privacy-select">
                  <option value="public">所有人</option>
                  <option value="friends">仅朋友</option>
                  <option value="private">仅自己</option>
                </select>
              </div>
            </div>
          </div>
          
          <div class="modal-actions">
            <button class="modal-btn secondary" @click="hideCreateMoment">
              取消
            </button>
            <button 
              class="modal-btn primary" 
              :disabled="!canPublish"
              @click="publishMoment"
            >
              发表
            </button>
          </div>
        </div>
      </div>
    </Transition>
    
    <!-- 评论弹窗 -->
    <Transition name="slide-up">
      <div v-if="showCommentsModal" class="comments-modal-overlay" @click="hideComments">
        <div class="comments-modal" @click.stop>
          <div class="modal-header">
            <h3>评论</h3>
            <button class="close-btn" @click="hideComments">
              <Icon name="lucide:x" />
            </button>
          </div>
          
          <div class="modal-content">
            <div class="comments-list">
              <div 
                v-for="comment in selectedMoment?.comments" 
                :key="comment.id"
                class="comment-detail"
              >
                <div class="comment-avatar">
                  <img :src="comment.user.avatar" :alt="comment.user.name" />
                </div>
                <div class="comment-content">
                  <div class="comment-header">
                    <span class="comment-user-name">{{ comment.user.name }}</span>
                    <span class="comment-time">{{ formatTime(comment.timestamp) }}</span>
                  </div>
                  <div class="comment-text">{{ comment.text }}</div>
                </div>
              </div>
            </div>
            
            <!-- 评论输入 -->
            <div class="comment-input-area">
              <div class="comment-input-box">
                <input 
                  v-model="newCommentText"
                  type="text"
                  placeholder="写评论..."
                  class="comment-input"
                  @keyup.enter="addComment"
                />
                <button 
                  class="send-comment-btn"
                  :disabled="!newCommentText.trim()"
                  @click="addComment"
                >
                  <Icon name="lucide:send" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
    
    <!-- 图片预览弹窗 -->
    <Transition name="fade">
      <div v-if="showImagePreview" class="image-preview-overlay" @click="hideImagePreview">
        <div class="image-preview-container">
          <button class="preview-close" @click="hideImagePreview">
            <Icon name="lucide:x" />
          </button>
          
          <div class="preview-image">
            <img :src="previewImages[currentPreviewIndex]" :alt="`Preview ${currentPreviewIndex + 1}`" />
          </div>
          
          <div v-if="previewImages.length > 1" class="preview-navigation">
            <button 
              class="nav-btn prev"
              :disabled="currentPreviewIndex === 0"
              @click="prevImage"
            >
              <Icon name="lucide:chevron-left" />
            </button>
            <button 
              class="nav-btn next"
              :disabled="currentPreviewIndex === previewImages.length - 1"
              @click="nextImage"
            >
              <Icon name="lucide:chevron-right" />
            </button>
          </div>
          
          <div class="preview-indicator">
            {{ currentPreviewIndex + 1 }} / {{ previewImages.length }}
          </div>
        </div>
      </div>
    </Transition>
    
    <!-- 状态提示 -->
    <Transition name="fade">
      <div v-if="showStatusTip" class="status-tip-overlay">
        <div class="status-tip">
          <Icon :name="statusTipIcon" />
          <span>{{ statusTipText }}</span>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
// 显式导入组件
import EnhancedBackground from '~/components/common/EnhancedBackground.vue'
import { useChat4State } from '~/composables/chat4/useChat4State'
import { useChat4Events } from '~/composables/chat4/useChat4Events'

// 用户类型
interface User {
  id: string
  name: string
  avatar: string
}

// 评论类型
interface Comment {
  id: string
  user: User
  text: string
  timestamp: number
}

// 位置类型
interface Location {
  id: string
  name: string
  address: string
}

// 朋友圈动态类型
interface Moment {
  id: string
  user: User
  text: string
  images: string[]
  video?: string
  location?: Location
  timestamp: number
  likes: number
  isLiked: boolean
  comments: Comment[]
  views: number
  isOwn: boolean
  privacy: 'public' | 'friends' | 'private'
}

// Chat4 状态管理
const { state, sceneNavigation } = useChat4State()
const { currentActor } = storeToRefs(state)

// 事件管理
const { emit: emitEvent, interactionEvents } = useChat4Events()

// 界面状态
const showCreateModal = ref(false)
const showCommentsModal = ref(false)
const showImagePreview = ref(false)
const showStatusTip = ref(false)
const statusTipText = ref('')
const statusTipIcon = ref('lucide:info')

// 数据状态
const moments = ref<Moment[]>([])
const isLoading = ref(false)
const hasMore = ref(true)
const selectedMoment = ref<Moment | null>(null)

// 创建动态状态
const newMomentText = ref('')
const newMomentImages = ref<string[]>([])
const newMomentLocation = ref<Location | null>(null)
const newMomentPrivacy = ref<'public' | 'friends' | 'private'>('public')

// 评论状态
const newCommentText = ref('')

// 图片预览状态
const previewImages = ref<string[]>([])
const currentPreviewIndex = ref(0)

// 背景状态
const backgroundVideoUrl = ref('')
const backgroundImageUrl = ref('https://picsum.photos/1920/1080?random=800')

// 计算属性
const showModal = computed(() => 
  showCreateModal.value || showCommentsModal.value || showImagePreview.value
)

const totalLikes = computed(() => 
  moments.value.filter(m => m.isOwn).reduce((sum, m) => sum + m.likes, 0)
)

const totalComments = computed(() => 
  moments.value.filter(m => m.isOwn).reduce((sum, m) => sum + m.comments.length, 0)
)

const canPublish = computed(() => 
  newMomentText.value.trim() || newMomentImages.value.length > 0
)

// 模拟数据
const mockUsers: User[] = [
  {
    id: 'user-1',
    name: '小美',
    avatar: 'https://picsum.photos/100/100?random=1'
  },
  {
    id: 'user-2', 
    name: '阿强',
    avatar: 'https://picsum.photos/100/100?random=2'
  },
  {
    id: 'user-3',
    name: '琳琳',
    avatar: 'https://picsum.photos/100/100?random=3'
  }
]

const mockLocations: Location[] = [
  {
    id: 'loc-1',
    name: '星巴克咖啡',
    address: '市中心商业街123号'
  },
  {
    id: 'loc-2',
    name: '中央公园',
    address: '城市中央绿化带'
  },
  {
    id: 'loc-3',
    name: '海边餐厅',
    address: '海滨大道888号'
  }
]

// 工具方法
const formatTime = (timestamp: number) => {
  const now = Date.now()
  const diff = now - timestamp
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  
  return new Date(timestamp).toLocaleDateString('zh-CN')
}

const generateMockMoment = (isOwn = false): Moment => {
  const user = isOwn ? {
    id: 'current-user',
    name: currentActor.value?.name || 'Me',
    avatar: currentActor.value?.avatar || 'https://picsum.photos/100/100?random=0'
  } : mockUsers[Math.floor(Math.random() * mockUsers.length)]
  
  const texts = [
    '今天天气真好，心情也很棒！',
    '和朋友们一起度过了愉快的时光',
    '分享一些美好的瞬间给大家',
    '生活中的小确幸',
    '感谢生活中遇到的每一个美好'
  ]
  
  const imageCount = Math.random() < 0.7 ? Math.floor(Math.random() * 4) + 1 : 0
  const images = Array.from({ length: imageCount }, (_, i) => 
    `https://picsum.photos/400/300?random=${Date.now() + i}`
  )
  
  return {
    id: `moment-${Date.now()}-${Math.random()}`,
    user,
    text: Math.random() < 0.9 ? texts[Math.floor(Math.random() * texts.length)] : '',
    images,
    location: Math.random() < 0.3 ? mockLocations[Math.floor(Math.random() * mockLocations.length)] : undefined,
    timestamp: Date.now() - Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000),
    likes: Math.floor(Math.random() * 50),
    isLiked: Math.random() < 0.3,
    comments: [],
    views: Math.floor(Math.random() * 100),
    isOwn,
    privacy: 'public'
  }
}

// 动态管理
const loadMoments = async () => {
  isLoading.value = true
  
  // 模拟网络请求
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  const newMoments = Array.from({ length: 5 }, (_, i) => {
    // 第一个动态是自己的
    return generateMockMoment(i === 0 && moments.value.length === 0)
  })
  
  // 为每个动态添加一些评论
  newMoments.forEach(moment => {
    const commentCount = Math.floor(Math.random() * 3)
    moment.comments = Array.from({ length: commentCount }, (_, i) => ({
      id: `comment-${Date.now()}-${i}`,
      user: mockUsers[Math.floor(Math.random() * mockUsers.length)],
      text: ['太棒了！', '很赞！', '好美啊', '羡慕~', '点赞'][Math.floor(Math.random() * 5)],
      timestamp: moment.timestamp + i * 60000
    }))
  })
  
  moments.value = [...moments.value, ...newMoments]
  isLoading.value = false
}

const loadMoreMoments = async () => {
  await loadMoments()
  
  // 模拟没有更多数据
  if (moments.value.length >= 20) {
    hasMore.value = false
  }
}

const refreshMoments = async () => {
  moments.value = []
  hasMore.value = true
  await loadMoments()
  showStatusTip('已刷新')
  setTimeout(hideStatusTip, 1500)
}

// 互动操作
const toggleLike = (moment: Moment) => {
  moment.isLiked = !moment.isLiked
  moment.likes += moment.isLiked ? 1 : -1
  
  if (moment.isLiked) {
    showStatusTip('已点赞')
    // 触发点赞动画效果
    interactionEvents.emitCharacterInteraction({
      type: 'like',
      response: '感谢你的点赞！'
    })
  } else {
    showStatusTip('已取消点赞')
  }
  
  setTimeout(hideStatusTip, 1000)
}

const showComments = (moment: Moment) => {
  selectedMoment.value = moment
  showCommentsModal.value = true
}

const hideComments = () => {
  showCommentsModal.value = false
  selectedMoment.value = null
  newCommentText.value = ''
}

const addComment = () => {
  if (!newCommentText.value.trim() || !selectedMoment.value) return
  
  const newComment: Comment = {
    id: `comment-${Date.now()}`,
    user: {
      id: 'current-user',
      name: currentActor.value?.name || 'Me',
      avatar: currentActor.value?.avatar || 'https://picsum.photos/100/100?random=0'
    },
    text: newCommentText.value.trim(),
    timestamp: Date.now()
  }
  
  selectedMoment.value.comments.push(newComment)
  newCommentText.value = ''
  
  showStatusTip('评论已发表')
  setTimeout(hideStatusTip, 1500)
}

const shareMoment = (moment: Moment) => {
  showStatusTip('已分享到其他平台')
  setTimeout(hideStatusTip, 1500)
}

// 创建动态
const showCreateMoment = () => {
  showCreateModal.value = true
}

const hideCreateMoment = () => {
  showCreateModal.value = false
  newMomentText.value = ''
  newMomentImages.value = []
  newMomentLocation.value = null
  newMomentPrivacy.value = 'public'
}

const addImage = () => {
  // 模拟选择图片
  const newImage = `https://picsum.photos/400/300?random=${Date.now()}`
  newMomentImages.value.push(newImage)
}

const removeImage = (index: number) => {
  newMomentImages.value.splice(index, 1)
}

const selectLocation = () => {
  // 模拟选择位置
  const randomLocation = mockLocations[Math.floor(Math.random() * mockLocations.length)]
  newMomentLocation.value = newMomentLocation.value ? null : randomLocation
}

const publishMoment = async () => {
  if (!canPublish.value) return
  
  const newMoment: Moment = {
    id: `moment-${Date.now()}`,
    user: {
      id: 'current-user',
      name: currentActor.value?.name || 'Me',
      avatar: currentActor.value?.avatar || 'https://picsum.photos/100/100?random=0'
    },
    text: newMomentText.value.trim(),
    images: [...newMomentImages.value],
    location: newMomentLocation.value || undefined,
    timestamp: Date.now(),
    likes: 0,
    isLiked: false,
    comments: [],
    views: 0,
    isOwn: true,
    privacy: newMomentPrivacy.value
  }
  
  // 插入到列表开头
  moments.value.unshift(newMoment)
  
  hideCreateMoment()
  showStatusTip('动态已发表')
  setTimeout(hideStatusTip, 2000)
  
  // 触发事件
  interactionEvents.emitSceneEvent('moment_published', {
    moment: newMoment
  })
}

// 图片预览
const previewImage = (images: string[], startIndex: number) => {
  previewImages.value = images
  currentPreviewIndex.value = startIndex
  showImagePreview.value = true
}

const hideImagePreview = () => {
  showImagePreview.value = false
  previewImages.value = []
  currentPreviewIndex.value = 0
}

const prevImage = () => {
  if (currentPreviewIndex.value > 0) {
    currentPreviewIndex.value--
  }
}

const nextImage = () => {
  if (currentPreviewIndex.value < previewImages.value.length - 1) {
    currentPreviewIndex.value++
  }
}

// 其他操作
const editMoment = (moment: Moment) => {
  showStatusTip('编辑功能开发中')
  setTimeout(hideStatusTip, 2000)
}

const showMomentMenu = (moment: Moment) => {
  showStatusTip('更多操作')
  setTimeout(hideStatusTip, 1500)
}

// 导航控制
const goBack = () => {
  sceneNavigation.goToChat()
}

// 工具方法
const showStatusTip = (text: string, icon: string = 'lucide:info') => {
  statusTipText.value = text
  statusTipIcon.value = icon
  showStatusTip.value = true
}

const hideStatusTip = () => {
  showStatusTip.value = false
}

// 生命周期
onMounted(() => {
  // 初始加载动态
  loadMoments()
})
</script>

<style scoped>
.moment-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background: #f8fafc;
  overflow: hidden;
}

/* 朋友圈界面 */
.moment-interface {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  z-index: 2;
}

/* 顶部导航栏 */
.top-navigation {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  z-index: 10;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-btn {
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.05);
  border: none;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  background: rgba(0, 0, 0, 0.1);
}

.page-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

/* 个人资料卡片 */
.profile-header {
  position: relative;
  height: 200px;
  margin-bottom: 20px;
}

.profile-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 150px;
  overflow: hidden;
}

.profile-background img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.3) 100%);
}

.profile-content {
  position: absolute;
  bottom: 0;
  left: 20px;
  right: 20px;
  display: flex;
  align-items: flex-end;
  gap: 16px;
}

.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-fallback {
  width: 100%;
  height: 100%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: #9ca3af;
}

.profile-info {
  flex: 1;
  padding-bottom: 8px;
}

.profile-name {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.profile-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 朋友圈内容 */
.moments-feed {
  flex: 1;
  padding: 0 20px 20px;
  overflow-y: auto;
}

.moment-card {
  background: white;
  border-radius: 12px;
  margin-bottom: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f1f5f9;
}

.moment-card.own-moment {
  border-left: 4px solid #3b82f6;
}

/* 动态头部 */
.moment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.moment-time {
  font-size: 12px;
  color: #9ca3af;
}

.moment-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  border-radius: 50%;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background: #f3f4f6;
}

/* 动态内容 */
.moment-content {
  margin-bottom: 12px;
}

.moment-text {
  font-size: 16px;
  line-height: 1.5;
  color: #1f2937;
  margin-bottom: 12px;
}

/* 图片网格 */
.moment-images {
  margin-bottom: 12px;
}

.image-grid {
  display: grid;
  gap: 4px;
  border-radius: 8px;
  overflow: hidden;
}

.image-grid.grid-1 {
  grid-template-columns: 1fr;
  max-width: 300px;
}

.image-grid.grid-2 {
  grid-template-columns: 1fr 1fr;
}

.image-grid.grid-3 {
  grid-template-columns: 1fr 1fr 1fr;
}

.image-grid.grid-many {
  grid-template-columns: repeat(3, 1fr);
}

.image-item {
  position: relative;
  aspect-ratio: 1;
  cursor: pointer;
  overflow: hidden;
}

.image-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.image-item:hover img {
  transform: scale(1.05);
}

.more-images {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 600;
}

/* 视频内容 */
.moment-video {
  margin-bottom: 12px;
}

.moment-video video {
  width: 100%;
  max-height: 300px;
  border-radius: 8px;
}

/* 位置信息 */
.moment-location {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 12px;
}

/* 互动统计 */
.moment-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  border-top: 1px solid #f1f5f9;
  border-bottom: 1px solid #f1f5f9;
  margin-bottom: 8px;
}

.stats-left {
  display: flex;
  gap: 16px;
}

.stats-left .stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #6b7280;
}

.view-count {
  font-size: 12px;
  color: #9ca3af;
}

/* 互动按钮 */
.moment-interactions {
  display: flex;
  justify-content: space-around;
  margin-bottom: 8px;
}

.interaction-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: none;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.interaction-btn:hover {
  background: #f3f4f6;
}

.interaction-btn.liked {
  color: #ef4444;
}

.interaction-btn .filled {
  color: #ef4444;
}

/* 评论预览 */
.comments-preview {
  background: #f9fafb;
  border-radius: 8px;
  padding: 12px;
}

.comment-item {
  margin-bottom: 4px;
  font-size: 14px;
  line-height: 1.4;
}

.comment-user {
  color: #3b82f6;
  font-weight: 500;
}

.comment-text {
  color: #4b5563;
}

.view-all-comments {
  background: none;
  border: none;
  color: #6b7280;
  font-size: 12px;
  cursor: pointer;
  margin-top: 4px;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 20px;
}

.load-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px 24px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0 auto;
}

.load-more-btn:hover:not(:disabled) {
  background: #f9fafb;
}

.load-more-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 弹窗样式 */
.create-modal-overlay,
.comments-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 20;
}

.create-modal,
.comments-modal {
  background: white;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
}

.modal-content {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

/* 创建表单 */
.moment-input {
  width: 100%;
  min-height: 100px;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 16px;
  resize: vertical;
  outline: none;
  margin-bottom: 16px;
}

.moment-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.media-selector {
  margin-bottom: 16px;
}

.selected-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-image {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
}

.selected-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-image {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 20px;
  height: 20px;
  background: rgba(0, 0, 0, 0.6);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.add-image-btn {
  width: 80px;
  height: 80px;
  background: #f9fafb;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  color: #9ca3af;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  transition: all 0.3s ease;
}

.add-image-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.location-selector {
  margin-bottom: 16px;
}

.location-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 10px 12px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  text-align: left;
}

.location-btn:hover {
  background: #f3f4f6;
}

.privacy-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.privacy-selector label {
  font-size: 14px;
  color: #4b5563;
}

.privacy-select {
  padding: 6px 10px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: white;
  cursor: pointer;
}

/* 评论列表 */
.comments-list {
  margin-bottom: 16px;
  max-height: 300px;
  overflow-y: auto;
}

.comment-detail {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.comment-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.comment-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.comment-user-name {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;  
}

.comment-time {
  font-size: 12px;
  color: #9ca3af;
}

.comment-detail .comment-text {
  font-size: 14px;
  color: #4b5563;
  line-height: 1.4;
}

/* 评论输入 */
.comment-input-area {
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
}

.comment-input-box {
  display: flex;
  gap: 8px;
  align-items: center;
}

.comment-input {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 20px;
  outline: none;
  font-size: 14px;
}

.comment-input:focus {
  border-color: #3b82f6;
}

.send-comment-btn {
  width: 32px;
  height: 32px;
  background: #3b82f6;
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.send-comment-btn:hover:not(:disabled) {
  background: #2563eb;
}

.send-comment-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 图片预览 */
.image-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 30;
}

.image-preview-container {
  position: relative;
  width: 90%;
  height: 90%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-close {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  z-index: 31;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.preview-navigation {
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
}

.nav-btn {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  transition: all 0.3s ease;
}

.nav-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
}

.nav-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.preview-indicator {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 8px 16px;
  border-radius: 16px;
  font-size: 14px;
}

.modal-actions {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e5e7eb;
}

.modal-btn {
  flex: 1;
  height: 44px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modal-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.modal-btn.secondary {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  color: #6b7280;
}

.modal-btn.primary {
  background: #3b82f6;
  border: 1px solid #3b82f6;
  color: white;
}

/* 状态提示 */
.status-tip-overlay {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 25;
}

.status-tip {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  backdrop-filter: blur(10px);
}

/* 动画 */
.slide-up-enter-active {
  transition: all 0.3s ease-out;
}

.slide-up-leave-active {
  transition: all 0.3s ease-in;
}

.slide-up-enter-from {
  transform: translateY(100%);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(100%);
  opacity: 0;
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .top-navigation {
    padding: 0 16px;
  }
  
  .profile-content {
    left: 16px;
    right: 16px;
  }
  
  .moments-feed {
    padding: 0 16px 20px;
  }
  
  .moment-card {
    padding: 12px;
  }
  
  .user-avatar {
    width: 36px;
    height: 36px;
  }
  
  .user-name {
    font-size: 14px;
  }
  
  .moment-text {
    font-size: 15px;
  }
  
  .image-grid.grid-1 {
    max-width: 100%;
  }
  
  .profile-avatar {
    width: 70px;
    height: 70px;
  }
  
  .profile-name {
    font-size: 18px;
  }
  
  .create-modal,
  .comments-modal {
    width: 100%;
    max-width: none;
  }
  
  .modal-content {
    padding: 16px;
  }
  
  .selected-image {
    width: 70px;
    height: 70px;
  }
  
  .add-image-btn {
    width: 70px;
    height: 70px;
  }
}
</style>