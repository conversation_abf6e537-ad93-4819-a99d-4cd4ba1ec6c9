<template>
  <div class="chat-room-container">
    <!-- 顶部状态栏 -->
    <div class="chat-header">
      <div class="character-info">
        <div class="avatar-container">
          <img 
            :src="currentActor?.avatar_url || '/default-avatar.png'" 
            :alt="currentActor?.name || 'Character'"
            class="character-avatar"
          >
          <div class="online-status"></div>
        </div>
        
        <div class="character-details">
          <h3 class="character-name">{{ currentActor?.name || 'Character' }}</h3>
          <p class="character-status">在线 • 正在输入...</p>
        </div>
      </div>
      
      <div class="chat-actions">
        <button class="action-btn" @click="handleVideoCall">
          <Icon name="lucide:video" />
        </button>
        <button class="action-btn" @click="goBackToLive">
          <Icon name="lucide:tv" />
        </button>
      </div>
    </div>
    
    <!-- 聊天消息区域 -->
    <div class="chat-messages" ref="messagesRef">
      <div class="messages-list">
        <div
          v-for="message in displayMessages"
          :key="message.id"
          class="message-item"
          :class="{
            'message-sent': message.sender === 'user',
            'message-received': message.sender !== 'user',
            'message-system': message.type === 'system'
          }"
        >
          <!-- 系统消息 -->
          <div v-if="message.type === 'system'" class="system-message">
            <Icon name="lucide:info" />
            <span>{{ message.content }}</span>
          </div>
          
          <!-- 普通消息 -->
          <div v-else class="message-bubble">
            <div class="message-content">
              <!-- 图片消息 -->
              <img 
                v-if="message.type === 'image' && message.imageUrl"
                :src="message.imageUrl"
                :alt="message.content"
                class="message-image"
                @click="previewImage(message.imageUrl)"
              >
              
              <!-- 礼物消息 -->
              <div v-else-if="message.type === 'gift'" class="gift-message">
                <Icon name="lucide:gift" />
                <span>{{ message.content }}</span>
                <div v-if="message.gift" class="gift-details">
                  <span class="gift-name">{{ message.gift.title }}</span>
                  <span class="gift-quantity">x{{ message.gift.quantity }}</span>
                </div>
              </div>
              
              <!-- 表情消息 -->
              <div v-else-if="message.type === 'emoji'" class="emoji-message">
                {{ message.content }}
              </div>
              
              <!-- 文本消息 -->
              <div v-else class="text-message">
                {{ message.content }}
              </div>
            </div>
            
            <div class="message-meta">
              <span class="message-time">{{ formatTime(message.timestamp) }}</span>
              <Icon 
                v-if="message.sender === 'user'" 
                name="lucide:check" 
                class="message-status"
              />
            </div>
          </div>
        </div>
        
        <!-- 正在输入指示器 -->
        <div v-if="isTyping" class="typing-indicator">
          <div class="typing-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
          <span class="typing-text">{{ currentActor?.name }} 正在输入...</span>
        </div>
      </div>
    </div>
    
    <!-- 底部输入区域 -->
    <div class="chat-input-area">
      <!-- 快捷功能栏 -->
      <div class="quick-actions">
        <button class="quick-btn" @click="openEmojiPanel">
          <Icon name="lucide:smile" />
        </button>
        <button class="quick-btn" @click="openImagePicker">
          <Icon name="lucide:image" />
        </button>
        <button class="quick-btn" @click="openGiftPanel">
          <Icon name="lucide:gift" />
        </button>
      </div>
      
      <!-- 消息输入框 -->
      <div class="input-container">
        <div class="input-wrapper">
          <textarea
            v-model="messageText"
            ref="inputRef"
            placeholder="输入消息..."
            class="message-input"
            rows="1"
            maxlength="500"
            @keydown="handleKeyDown"
            @input="handleInput"
          ></textarea>
          
          <button 
            class="send-button"
            @click="sendMessage"
            :disabled="!messageText.trim()"
            :class="{ active: messageText.trim() }"
          >
            <Icon name="lucide:send" />
          </button>
        </div>
        
        <div class="input-footer">
          <span class="char-count">{{ messageText.length }}/500</span>
        </div>
      </div>
    </div>
    
    <!-- 表情面板 -->
    <EmojiPanel 
      v-if="showEmojiPanel"
      @emoji-select="insertEmoji"
      @close="showEmojiPanel = false"
    />
  </div>
</template>

<script setup lang="ts">
import type { ChatMessage } from '~/types/chat4'

// 显式导入UI组件
import EmojiPanel from '~/components/Chat4/ui/EmojiPanel.vue'

// 显式导入composables
import { useChat4State } from '~/composables/chat4/useChat4State'
import { useChat4Messages } from '~/composables/chat4/useChat4Messages'
import { useChat4Events } from '~/composables/chat4/useChat4Events'

// Props
interface Props {
  characterId?: string
  storyId?: string
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'scene-change', scene: string): void
}

const emit = defineEmits<Emits>()

// Chat4状态管理
const { 
  state, 
  sceneNavigation,
  uiControls 
} = useChat4State()

const { 
  sendMessage: sendChatMessage, 
  messageData 
} = useChat4Messages()

const { 
  emit: emitEvent 
} = useChat4Events()

// 响应式数据
const messageText = ref('')
const isTyping = ref(false)
const showEmojiPanel = ref(false)
const messagesRef = ref<HTMLElement>()
const inputRef = ref<HTMLTextAreaElement>()

// 计算属性
const currentActor = computed(() => state.value.currentActor)
const messages = computed(() => messageData.value.messages)

// 显示的消息（过滤掉不适合聊天的消息类型）
const displayMessages = computed(() => 
  messages.value.filter(msg => 
    ['text', 'image', 'emoji', 'gift', 'system'].includes(msg.type)
  )
)

// 格式化时间
const formatTime = (timestamp: number) => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

// 自动调整输入框高度
const adjustInputHeight = () => {
  if (inputRef.value) {
    inputRef.value.style.height = 'auto'
    inputRef.value.style.height = Math.min(inputRef.value.scrollHeight, 120) + 'px'
  }
}

// 处理输入
const handleInput = () => {
  adjustInputHeight()
  
  // 模拟输入状态
  if (!isTyping.value && messageText.value.trim()) {
    isTyping.value = true
    // 3秒后停止输入状态
    setTimeout(() => {
      isTyping.value = false
    }, 3000)
  }
}

// 处理键盘事件
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

// 发送消息
const sendMessage = () => {
  const text = messageText.value.trim()
  if (!text) return
  
  // 发送用户消息
  sendChatMessage.text(text)
  
  // 清空输入
  messageText.value = ''
  adjustInputHeight()
  
  // 滚动到底部
  scrollToBottom()
  
  // 模拟AI回复
  simulateAIReply(text)
  
  console.log('💬 发送聊天消息:', text)
}

// 模拟AI回复
const simulateAIReply = (userMessage: string) => {
  const responses = [
    '听起来很有趣呢！',
    '我也是这么想的~',
    '你说得对！',
    '哈哈哈，太好笑了！',
    '真的吗？告诉我更多！',
    '我很想听你继续说下去',
    '你真的很懂我呢',
    '这让我想起了...',
    '我们聊得很开心呢！'
  ]
  
  setTimeout(() => {
    const response = responses[Math.floor(Math.random() * responses.length)]
    sendChatMessage.text(response, 'text')
    scrollToBottom()
  }, 1000 + Math.random() * 2000)
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (messagesRef.value) {
      messagesRef.value.scrollTop = messagesRef.value.scrollHeight
    }
  })
}

// 插入表情
const insertEmoji = (emoji: string) => {
  messageText.value += emoji
  showEmojiPanel.value = false
  inputRef.value?.focus()
}

// 打开表情面板
const openEmojiPanel = () => {
  showEmojiPanel.value = !showEmojiPanel.value
}

// 打开图片选择器
const openImagePicker = () => {
  // 创建隐藏的文件输入
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'image/*'
  input.onchange = (e) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (file) {
      // 这里应该上传图片并获取URL
      // 暂时使用占位符
      const imageUrl = URL.createObjectURL(file)
      sendChatMessage.image(imageUrl, '发送了一张图片')
      scrollToBottom()
    }
  }
  input.click()
}

// 打开礼物面板
const openGiftPanel = () => {
  uiControls.openGiftModal()
  emitEvent('modal:open', { modal: 'gift' })
}

// 预览图片
const previewImage = (imageUrl: string) => {
  // 这里应该打开图片预览模态框
  console.log('预览图片:', imageUrl)
}

// 视频通话
const handleVideoCall = () => {
  emit('scene-change', 'Video')
  emitEvent('scene:change', { 
    from: 'Phone', 
    to: 'Video', 
    timestamp: Date.now() 
  })
}

// 返回直播
const goBackToLive = () => {
  emit('scene-change', 'Living')
  emitEvent('scene:change', { 
    from: 'Phone', 
    to: 'Living', 
    timestamp: Date.now() 
  })
}

// 监听消息变化，自动滚动
watch(
  () => messages.value.length,
  () => {
    scrollToBottom()
  }
)

// 组件挂载时初始化
onMounted(() => {
  console.log('💬 聊天场景初始化')
  
  // 发送欢迎消息
  nextTick(() => {
    sendChatMessage.system('聊天已连接，开始对话吧！')
    
    setTimeout(() => {
      sendChatMessage.text('嗨！很高兴和你聊天~', 'text')
    }, 1000)
  })
  
  // 自动聚焦输入框
  inputRef.value?.focus()
  scrollToBottom()
})

onUnmounted(() => {
  console.log('💬 聊天场景卸载')
})
</script>

<style scoped>
.chat-room-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--chat4-bg-gradient);
  color: white;
}

/* 顶部状态栏 */
.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.character-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.avatar-container {
  position: relative;
  width: 48px;
  height: 48px;
}

.character-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--chat4-secondary-color);
}

.online-status {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #4ade80;
  border: 2px solid white;
}

.character-details {
  flex: 1;
}

.character-name {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: var(--chat4-secondary-color);
}

.character-status {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

.chat-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

/* 聊天消息区域 */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  scroll-behavior: smooth;
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message-item {
  display: flex;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-sent {
  justify-content: flex-end;
}

.message-received {
  justify-content: flex-start;
}

.message-system {
  justify-content: center;
}

.system-message {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  padding: 8px 16px;
  border-radius: 16px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.message-bubble {
  max-width: 80%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 18px;
  padding: 12px 16px;
  position: relative;
}

.message-sent .message-bubble {
  background: var(--chat4-secondary-color);
  color: var(--chat4-primary-color);
}

.message-content {
  margin-bottom: 4px;
}

.message-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: 12px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.message-image:hover {
  transform: scale(1.02);
}

.gift-message {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ffd700;
  font-weight: 600;
}

.gift-details {
  display: flex;
  flex-direction: column;
  font-size: 12px;
}

.emoji-message {
  font-size: 32px;
  text-align: center;
}

.text-message {
  line-height: 1.4;
  word-wrap: break-word;
}

.message-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 4px;
}

.message-time {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
}

.message-status {
  width: 12px;
  height: 12px;
  color: rgba(255, 255, 255, 0.5);
}

/* 正在输入指示器 */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 18px;
  max-width: 200px;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  animation: bounce 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0.6);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.typing-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

/* 底部输入区域 */
.chat-input-area {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 16px;
}

.quick-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.quick-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 8px 12px;
}

.message-input {
  flex: 1;
  background: transparent;
  border: none;
  color: white;
  font-size: 14px;
  line-height: 1.4;
  resize: none;
  outline: none;
  min-height: 20px;
  max-height: 120px;
}

.message-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.send-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.5);
}

.send-button.active {
  background: var(--chat4-secondary-color);
  color: var(--chat4-primary-color);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.input-footer {
  display: flex;
  justify-content: flex-end;
}

.char-count {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
}

/* 自定义滚动条 */
.chat-messages::-webkit-scrollbar {
  width: 4px;
}

.chat-messages::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.chat-messages::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-header {
    padding: 12px;
  }
  
  .character-name {
    font-size: 14px;
  }
  
  .avatar-container {
    width: 40px;
    height: 40px;
  }
  
  .chat-messages {
    padding: 12px;
  }
  
  .message-bubble {
    max-width: 85%;
    padding: 10px 14px;
  }
}
</style>