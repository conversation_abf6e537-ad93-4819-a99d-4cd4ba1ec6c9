<template>
  <div class="dancing-container">
    <!-- 增强背景 -->
    <EnhancedBackground
      :video-url="backgroundVideoUrl"
      :image-url="backgroundImageUrl"
      :blur="false"
      transition-mode="crossfade"
      :transition-duration="1000"
    />
    
    <!-- 舞蹈界面 -->
    <div class="dancing-interface">
      <!-- 顶部信息栏 -->
      <div class="info-bar">
        <div class="dance-info">
          <div class="current-song">
            <Icon name="lucide:music" />
            <span>{{ currentSong.title }} - {{ currentSong.artist }}</span>
          </div>
          <div class="score-display">
            <Icon name="lucide:star" />
            <span>{{ currentScore }}</span>
          </div>
        </div>
        
        <div class="dance-controls">
          <button class="control-btn" @click="toggleMusic">
            <Icon :name="isPlaying ? 'lucide:pause' : 'lucide:play'" />
          </button>
          <button class="control-btn" @click="showSongSelection">
            <Icon name="lucide:list-music" />
          </button>
        </div>
      </div>
      
      <!-- 舞台区域 -->
      <div class="dance-stage">
        <!-- 角色舞蹈区 -->
        <div class="dancer-area">
          <!-- 主角色 -->
          <div class="main-dancer" :class="{ 'dancing': isDancing, 'perfect': showPerfectEffect }">
            <div class="dancer-avatar">
              <img v-if="currentActor?.avatar" :src="currentActor.avatar" :alt="currentActor.name" />
              <div v-else class="avatar-fallback">
                <Icon name="lucide:user" />
              </div>
              
              <!-- 舞蹈动作效果 -->
              <div v-if="isDancing" class="dance-effects">
                <div class="rhythm-rings"></div>
                <div class="movement-trail"></div>
              </div>
            </div>
            
            <div class="dancer-name">{{ currentActor?.name || 'Dancer' }}</div>
            <div class="combo-counter" v-if="currentCombo > 0">
              <span class="combo-text">COMBO</span>
              <span class="combo-number">{{ currentCombo }}</span>
            </div>
          </div>
          
          <!-- 舞伴 (如果有) -->
          <div v-if="showPartner" class="partner-dancer" :class="{ 'dancing': isDancing }">
            <div class="dancer-avatar">
              <div class="avatar-fallback partner">
                <Icon name="lucide:users" />
              </div>
            </div>
            <div class="dancer-name">Dance Partner</div>
          </div>
        </div>
        
        <!-- 节拍指示器 -->
        <div class="beat-indicators">
          <div 
            v-for="(beat, index) in beatIndicators" 
            :key="index"
            class="beat-dot"
            :class="{ 
              'active': beat.active, 
              'perfect': beat.perfect,
              'good': beat.good,
              'miss': beat.miss 
            }"
          ></div>
        </div>
        
        <!-- 舞蹈指令区 */
        <div class="dance-commands" v-if="isDancing">
          <div 
            v-for="command in upcomingCommands" 
            :key="command.id"
            class="command-arrow"
            :class="command.direction"
            :style="{ transform: `translateY(${command.position}px)` }"
          >
            <Icon :name="getArrowIcon(command.direction)" />
          </div>
        </div>
      </div>
      
      <!-- 舞蹈评价区 */
      <div class="performance-feedback" v-if="lastPerformance">
        <div class="feedback-popup" :class="lastPerformance.grade">
          <div class="grade-text">{{ getGradeText(lastPerformance.grade) }}</div>
          <div class="score-gained">+{{ lastPerformance.score }}</div>
        </div>
      </div>
      
      <!-- 底部控制面板 */
      <div class="control-panel">
        <!-- 舞蹈模式选择 -->
        <div class="mode-selection">
          <button 
            v-for="mode in danceModes" 
            :key="mode.id"
            class="mode-btn"
            :class="{ active: currentMode === mode.id }"
            @click="switchMode(mode.id)"
          >
            <Icon :name="mode.icon" />
            <span>{{ mode.name }}</span>
          </button>
        </div>
        
        <!-- 主要操作按钮 */
        <div class="main-controls">
          <button class="control-button secondary" @click="showSettings">
            <Icon name="lucide:settings" />
            <span>设置</span>
          </button>
          
          <button 
            class="control-button primary"
            :class="{ 'stop': isDancing }"
            @click="toggleDancing"
          >
            <Icon :name="isDancing ? 'lucide:square' : 'lucide:play'" />
            <span>{{ isDancing ? '停止舞蹈' : '开始舞蹈' }}</span>
          </button>
          
          <button class="control-button secondary" @click="goBack">
            <Icon name="lucide:home" />
            <span>返回</span>
          </button>
        </div>
      </div>
    </div>
    
    <!-- 歌曲选择弹窗 -->
    <Transition name="fade">
      <div v-if="showSongModal" class="song-modal-overlay" @click="hideSongSelection">
        <div class="song-modal" @click.stop>
          <div class="modal-header">
            <h3>选择音乐</h3>
            <button class="close-btn" @click="hideSongSelection">
              <Icon name="lucide:x" />
            </button>
          </div>
          
          <div class="modal-content">
            <div class="song-list">
              <div 
                v-for="song in availableSongs" 
                :key="song.id"
                class="song-item"
                :class="{ 
                  selected: selectedSongId === song.id,
                  locked: song.locked 
                }"
                @click="selectSong(song)"
              >
                <div class="song-icon">
                  <Icon :name="song.locked ? 'lucide:lock' : 'lucide:music'" />
                </div>
                
                <div class="song-details">
                  <div class="song-title">{{ song.title }}</div>
                  <div class="song-artist">{{ song.artist }}</div>
                  <div class="song-info">
                    <span class="song-duration">{{ song.duration }}</span>
                    <span class="song-difficulty" :class="song.difficulty.toLowerCase()">
                      {{ getDifficultyText(song.difficulty) }}
                    </span>
                  </div>
                </div>
                
                <div class="song-actions">
                  <button 
                    v-if="!song.locked" 
                    class="preview-btn"
                    @click.stop="previewSong(song)"
                  >
                    <Icon name="lucide:play" />
                  </button>
                </div>
              </div>
            </div>
            
            <div class="modal-actions">
              <button class="modal-btn secondary" @click="hideSongSelection">
                取消
              </button>
              <button 
                class="modal-btn primary" 
                :disabled="!selectedSongId || getSongById(selectedSongId)?.locked"
                @click="confirmSongSelection"
              >
                确认选择
              </button>
            </div>
          </div>
        </div>
      </div>
    </Transition>
    
    <!-- 设置弹窗 -->
    <Transition name="fade">
      <div v-if="showSettingsModal" class="settings-modal-overlay" @click="hideSettings">
        <div class="settings-modal" @click.stop>
          <div class="modal-header">
            <h3>舞蹈设置</h3>
            <button class="close-btn" @click="hideSettings">
              <Icon name="lucide:x" />
            </button>
          </div>
          
          <div class="modal-content">
            <div class="setting-group">
              <h4>难度设置</h4>
              <div class="difficulty-options">
                <button 
                  v-for="difficulty in difficultyLevels" 
                  :key="difficulty.id"
                  class="difficulty-btn"
                  :class="{ active: currentDifficulty === difficulty.id }"
                  @click="setDifficulty(difficulty.id)"
                >
                  {{ difficulty.name }}
                </button>
              </div>
            </div>
            
            <div class="setting-group">
              <h4>舞蹈效果</h4>
              <div class="effect-toggles">
                <label class="toggle-item">
                  <input type="checkbox" v-model="showParticles" />
                  <span>粒子效果</span>
                </label>
                <label class="toggle-item">
                  <input type="checkbox" v-model="showTrails" />
                  <span>动作轨迹</span>
                </label>
                <label class="toggle-item">
                  <input type="checkbox" v-model="enableVibration" />
                  <span>触觉反馈</span>
                </label>
              </div>
            </div>
            
            <div class="setting-group">
              <h4>音效设置</h4>
              <div class="volume-controls">
                <div class="volume-item">
                  <label>音乐音量</label>
                  <input 
                    type="range" 
                    min="0" 
                    max="100" 
                    v-model="musicVolume"
                    class="volume-slider"
                  />
                  <span>{{ musicVolume }}%</span>
                </div>
                <div class="volume-item">
                  <label>音效音量</label>
                  <input 
                    type="range" 
                    min="0" 
                    max="100" 
                    v-model="effectVolume"
                    class="volume-slider"
                  />
                  <span>{{ effectVolume }}%</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="modal-actions">
            <button class="modal-btn secondary" @click="resetSettings">
              重置
            </button>
            <button class="modal-btn primary" @click="hideSettings">
              确定
            </button>
          </div>
        </div>
      </div>
    </Transition>
    
    <!-- 状态提示 -->
    <Transition name="fade">
      <div v-if="showStatusTip" class="status-tip-overlay">
        <div class="status-tip">
          <Icon :name="statusTipIcon" />
          <span>{{ statusTipText }}</span>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
// 显式导入组件
import EnhancedBackground from '~/components/common/EnhancedBackground.vue'
import { useChat4State } from '~/composables/chat4/useChat4State'
import { useChat4Events } from '~/composables/chat4/useChat4Events'

// 歌曲类型
interface Song {
  id: string
  title: string
  artist: string
  duration: string
  difficulty: 'EASY' | 'NORMAL' | 'HARD' | 'EXPERT'
  bpm: number
  locked: boolean
  previewUrl?: string
}

// 舞蹈指令类型
interface DanceCommand {
  id: string
  direction: 'up' | 'down' | 'left' | 'right'
  timing: number
  position: number
}

// 节拍指示器类型
interface BeatIndicator {
  id: number
  active: boolean
  perfect: boolean
  good: boolean
  miss: boolean
}

// 表演评价类型
interface Performance {
  grade: 'perfect' | 'good' | 'ok' | 'miss'
  score: number
  timing: number
}

// 舞蹈模式类型
interface DanceMode {
  id: string
  name: string
  icon: string
  description: string
}

// Chat4 状态管理
const { state, sceneNavigation } = useChat4State()
const { currentActor } = storeToRefs(state)

// 事件管理
const { emit: emitEvent, interactionEvents } = useChat4Events()

// 界面状态
const showSongModal = ref(false)
const showSettingsModal = ref(false)
const showStatusTip = ref(false)
const statusTipText = ref('')
const statusTipIcon = ref('lucide:info')

// 舞蹈状态
const isDancing = ref(false)
const isPlaying = ref(false)
const currentMode = ref('freestyle')
const currentDifficulty = ref('NORMAL')
const selectedSongId = ref('song-1')

// 游戏状态
const currentScore = ref(0)
const currentCombo = ref(0)
const showPerfectEffect = ref(false)
const showPartner = ref(false)

// 表演评价
const lastPerformance = ref<Performance | null>(null)

// 设置状态
const showParticles = ref(true)
const showTrails = ref(true)
const enableVibration = ref(true)
const musicVolume = ref(80)
const effectVolume = ref(70)

// 背景状态
const backgroundVideoUrl = ref('')
const backgroundImageUrl = ref('https://picsum.photos/1920/1080?random=500')

// 节拍指示器
const beatIndicators = ref<BeatIndicator[]>([
  { id: 1, active: false, perfect: false, good: false, miss: false },
  { id: 2, active: false, perfect: false, good: false, miss: false },
  { id: 3, active: false, perfect: false, good: false, miss: false },
  { id: 4, active: false, perfect: false, good: false, miss: false }
])

// 舞蹈指令
const upcomingCommands = ref<DanceCommand[]>([])

// 定时器
let danceTimer: ReturnType<typeof setInterval> | null = null
let beatTimer: ReturnType<typeof setInterval> | null = null
let commandGeneratorTimer: ReturnType<typeof setInterval> | null = null

// 可用歌曲
const availableSongs: Song[] = [
  {
    id: 'song-1',
    title: 'Electric Dreams',
    artist: 'Digital Hearts',
    duration: '3:42',
    difficulty: 'EASY',
    bpm: 120,
    locked: false
  },
  {
    id: 'song-2',
    title: 'Neon Nights',
    artist: 'Cyber Dance',
    duration: '4:15',
    difficulty: 'NORMAL',
    bpm: 140,
    locked: false
  },
  {
    id: 'song-3',
    title: 'Rhythm Revolution',
    artist: 'Beat Masters',
    duration: '3:58',
    difficulty: 'HARD',
    bpm: 160,
    locked: false
  },
  {
    id: 'song-4',
    title: 'Digital Symphony',
    artist: 'Future Sound',
    duration: '5:22',
    difficulty: 'EXPERT',
    bpm: 180,
    locked: true
  }
]

// 舞蹈模式
const danceModes: DanceMode[] = [
  {
    id: 'freestyle',
    name: '自由舞',
    icon: 'lucide:zap',
    description: '自由发挥的舞蹈模式'
  },
  {
    id: 'choreography',
    name: '编舞模式',
    icon: 'lucide:target',
    description: '跟随编舞动作'
  },
  {
    id: 'partner',
    name: '双人舞',
    icon: 'lucide:users',
    description: '与舞伴一起舞蹈'
  },
  {
    id: 'battle',
    name: '舞蹈对战',
    icon: 'lucide:sword',
    description: '竞技舞蹈模式'
  }
]

// 难度等级
const difficultyLevels = [
  { id: 'EASY', name: '简单' },
  { id: 'NORMAL', name: '普通' },
  { id: 'HARD', name: '困难' },
  { id: 'EXPERT', name: '专家' }
]

// 计算属性
const currentSong = computed(() => 
  availableSongs.find(song => song.id === selectedSongId.value) || availableSongs[0]
)

// 工具方法
const getSongById = (id: string) => availableSongs.find(song => song.id === id)

const getArrowIcon = (direction: string) => {
  switch (direction) {
    case 'up': return 'lucide:arrow-up'
    case 'down': return 'lucide:arrow-down'
    case 'left': return 'lucide:arrow-left'
    case 'right': return 'lucide:arrow-right'
    default: return 'lucide:circle'
  }
}

const getDifficultyText = (difficulty: string) => {
  switch (difficulty) {
    case 'EASY': return '简单'
    case 'NORMAL': return '普通'
    case 'HARD': return '困难'
    case 'EXPERT': return '专家'
    default: return '未知'
  }
}

const getGradeText = (grade: string) => {
  switch (grade) {
    case 'perfect': return 'PERFECT!'
    case 'good': return 'GOOD!'
    case 'ok': return 'OK'
    case 'miss': return 'MISS'
    default: return ''
  }
}

// 音乐控制
const toggleMusic = () => {
  isPlaying.value = !isPlaying.value
  
  if (isPlaying.value) {
    showStatusTip('音乐播放中')
    startBeatTimer()
  } else {
    showStatusTip('音乐已暂停')
    stopBeatTimer()
  }
  
  setTimeout(hideStatusTip, 1500)
}

const showSongSelection = () => {
  showSongModal.value = true
}

const hideSongSelection = () => {
  showSongModal.value = false
}

const selectSong = (song: Song) => {
  if (song.locked) {
    showStatusTip('此歌曲尚未解锁')
    setTimeout(hideStatusTip, 2000)
    return
  }
  
  selectedSongId.value = song.id
}

const previewSong = (song: Song) => {
  showStatusTip(`预览: ${song.title}`)
  setTimeout(hideStatusTip, 2000)
}

const confirmSongSelection = () => {
  if (currentSong.value) {
    showStatusTip(`已选择: ${currentSong.value.title}`)
    setTimeout(hideStatusTip, 1500)
  }
  hideSongSelection()
}

// 舞蹈控制
const toggleDancing = () => {
  if (isDancing.value) {
    stopDancing()
  } else {
    startDancing()
  }
}

const startDancing = () => {
  isDancing.value = true
  isPlaying.value = true
  currentScore.value = 0
  currentCombo.value = 0
  
  showStatusTip('开始舞蹈!')
  setTimeout(hideStatusTip, 1500)
  
  // 启动舞蹈逻辑
  startBeatTimer()
  startCommandGenerator()
  
  // 触发事件
  interactionEvents.emitSceneEvent('dance_start', {
    song: currentSong.value,
    mode: currentMode.value,
    difficulty: currentDifficulty.value
  })
}

const stopDancing = () => {
  isDancing.value = false
  isPlaying.value = false
  
  // 停止所有定时器
  stopBeatTimer()
  stopCommandGenerator()
  
  // 清理状态
  upcomingCommands.value = []
  lastPerformance.value = null
  
  showStatusTip(`舞蹈结束! 最终得分: ${currentScore.value}`)
  setTimeout(hideStatusTip, 3000)
  
  // 触发事件
  interactionEvents.emitSceneEvent('dance_end', {
    score: currentScore.value,
    combo: currentCombo.value
  })
}

const switchMode = (modeId: string) => {
  if (isDancing.value) {
    showStatusTip('请先停止舞蹈')
    setTimeout(hideStatusTip, 2000)
    return
  }
  
  currentMode.value = modeId
  const mode = danceModes.find(m => m.id === modeId)
  
  if (mode) {
    showStatusTip(`切换到: ${mode.name}`)
    
    // 特殊模式处理
    if (modeId === 'partner') {
      showPartner.value = true
    } else {
      showPartner.value = false
    }
    
    setTimeout(hideStatusTip, 1500)
  }
}

// 节拍系统
const startBeatTimer = () => {
  if (!currentSong.value) return
  
  const beatInterval = 60000 / currentSong.value.bpm // 根据BPM计算间隔
  
  beatTimer = setInterval(() => {
    // 更新节拍指示器
    beatIndicators.value.forEach((beat, index) => {
      beat.active = (Date.now() % (beatInterval * 4)) < beatInterval * (index + 1)
    })
  }, beatInterval / 4)
}

const stopBeatTimer = () => {
  if (beatTimer) {
    clearInterval(beatTimer)
    beatTimer = null
  }
  
  // 重置节拍指示器
  beatIndicators.value.forEach(beat => {
    beat.active = false
    beat.perfect = false
    beat.good = false
    beat.miss = false
  })
}

// 指令生成系统
const startCommandGenerator = () => {
  if (!isDancing.value) return
  
  const generateCommand = () => {
    const directions = ['up', 'down', 'left', 'right']
    const direction = directions[Math.floor(Math.random() * directions.length)]
    
    const command: DanceCommand = {
      id: Date.now().toString(),
      direction: direction as DanceCommand['direction'],
      timing: Date.now() + 2000, // 2秒后到达判定线
      position: -100 // 从屏幕顶部开始
    }
    
    upcomingCommands.value.push(command)
  }
  
  // 根据难度调整生成频率
  const intervals = {
    EASY: 1500,
    NORMAL: 1200,
    HARD: 800,
    EXPERT: 600
  }
  
  const interval = intervals[currentDifficulty.value as keyof typeof intervals]
  
  commandGeneratorTimer = setInterval(generateCommand, interval)
  
  // 指令移动动画
  const moveCommands = () => {
    upcomingCommands.value = upcomingCommands.value.map(command => ({
      ...command,
      position: command.position + 5 // 向下移动
    })).filter(command => command.position < 500) // 移除超出屏幕的指令
  }
  
  danceTimer = setInterval(moveCommands, 50)
}

const stopCommandGenerator = () => {
  if (commandGeneratorTimer) {
    clearInterval(commandGeneratorTimer)
    commandGeneratorTimer = null
  }
  
  if (danceTimer) {
    clearInterval(danceTimer)
    danceTimer = null
  }
}

// 输入处理 (模拟)
const handleDanceInput = (direction: string) => {
  if (!isDancing.value) return
  
  // 找到最接近判定线的指令
  const targetCommand = upcomingCommands.value
    .filter(cmd => cmd.direction === direction && Math.abs(cmd.position - 300) < 100)
    .sort((a, b) => Math.abs(a.position - 300) - Math.abs(b.position - 300))[0]
  
  if (targetCommand) {
    // 计算判定
    const distance = Math.abs(targetCommand.position - 300)
    let grade: Performance['grade']
    let score = 0
    
    if (distance < 20) {
      grade = 'perfect'
      score = 100
      currentCombo.value++
      showPerfectEffect.value = true
      setTimeout(() => showPerfectEffect.value = false, 500)
    } else if (distance < 40) {
      grade = 'good'
      score = 70
      currentCombo.value++
    } else if (distance < 60) {
      grade = 'ok'
      score = 40
      currentCombo.value = 0
    } else {
      grade = 'miss'
      score = 0
      currentCombo.value = 0
    }
    
    // 更新分数
    currentScore.value += score + (currentCombo.value * 10)
    
    // 显示评价
    lastPerformance.value = { grade, score, timing: Date.now() }
    setTimeout(() => lastPerformance.value = null, 1000)
    
    // 移除已判定的指令
    upcomingCommands.value = upcomingCommands.value.filter(cmd => cmd.id !== targetCommand.id)
    
    // 触觉反馈
    if (enableVibration.value && navigator.vibrate) {
      navigator.vibrate(grade === 'perfect' ? 50 : 25)
    }
  }
}

// 设置控制
const showSettings = () => {
  showSettingsModal.value = true
}

const hideSettings = () => {
  showSettingsModal.value = false
}

const setDifficulty = (difficulty: string) => {
  if (isDancing.value) {
    showStatusTip('请先停止舞蹈')
    setTimeout(hideStatusTip, 2000)
    return
  }
  
  currentDifficulty.value = difficulty
  showStatusTip(`难度设置为: ${getDifficultyText(difficulty)}`)
  setTimeout(hideStatusTip, 1500)
}

const resetSettings = () => {
  showParticles.value = true
  showTrails.value = true
  enableVibration.value = true
  musicVolume.value = 80
  effectVolume.value = 70
  currentDifficulty.value = 'NORMAL'
  
  showStatusTip('设置已重置')
  setTimeout(hideStatusTip, 1500)
}

// 导航控制
const goBack = () => {
  if (isDancing.value) {
    stopDancing()
  }
  sceneNavigation.goToMeetup()
}

// 工具方法
const showStatusTip = (text: string, icon: string = 'lucide:info') => {
  statusTipText.value = text
  statusTipIcon.value = icon
  showStatusTip.value = true
}

const hideStatusTip = () => {
  showStatusTip.value = false
}

// 键盘事件处理
const handleKeyDown = (event: KeyboardEvent) => {
  if (!isDancing.value) return
  
  switch (event.key) {
    case 'ArrowUp':
      handleDanceInput('up')
      break
    case 'ArrowDown':
      handleDanceInput('down')
      break
    case 'ArrowLeft':
      handleDanceInput('left')
      break
    case 'ArrowRight':
      handleDanceInput('right')
      break
  }
}

// 生命周期
onMounted(() => {
  // 监听键盘事件
  document.addEventListener('keydown', handleKeyDown)
  
  // 设置初始背景
  backgroundVideoUrl.value = ''
  backgroundImageUrl.value = 'https://picsum.photos/1920/1080?random=500'
})

onBeforeUnmount(() => {
  // 清理定时器
  stopBeatTimer()
  stopCommandGenerator()
  
  // 移除事件监听
  document.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
.dancing-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

/* 舞蹈界面 */
.dancing-interface {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  z-index: 2;
}

/* 信息栏 */
.info-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, transparent 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  z-index: 10;
}

.dance-info {
  display: flex;
  align-items: center;
  gap: 20px;
  color: white;
}

.current-song {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
}

.score-display {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 16px;
  font-weight: 600;
  color: #fbbf24;
}

.dance-controls {
  display: flex;
  gap: 8px;
}

.control-btn {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 舞台区域 */
.dance-stage {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px 160px;
}

.dancer-area {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 60px;
  margin-bottom: 40px;
}

.main-dancer,
.partner-dancer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  position: relative;
}

.main-dancer.dancing {
  animation: danceMove 0.8s ease-in-out infinite alternate;
}

.main-dancer.perfect {
  animation: perfectGlow 0.5s ease-out;
}

.partner-dancer.dancing {
  animation: partnerDance 1.2s ease-in-out infinite alternate;
}

@keyframes danceMove {
  0% { transform: translateY(0) rotate(-2deg); }
  100% { transform: translateY(-10px) rotate(2deg); }
}

@keyframes partnerDance {
  0% { transform: translateY(0) rotate(2deg); }
  100% { transform: translateY(-8px) rotate(-2deg); }
}

@keyframes perfectGlow {
  0% { 
    transform: scale(1);
    filter: drop-shadow(0 0 0 rgba(255, 215, 0, 0));
  }
  50% { 
    transform: scale(1.1);
    filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8));
  }
  100% { 
    transform: scale(1);
    filter: drop-shadow(0 0 0 rgba(255, 215, 0, 0));
  }
}

.dancer-avatar {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.dancer-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-fallback {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: rgba(255, 255, 255, 0.6);
}

.avatar-fallback.partner {
  background: rgba(139, 92, 246, 0.3);
  color: rgba(139, 92, 246, 0.8);
}

.dance-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.rhythm-rings {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 140px;
  height: 140px;
  border: 3px solid rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  animation: rhythmPulse 0.8s ease-in-out infinite;
}

.movement-trail {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 160px;
  height: 160px;
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 50%;
  animation: trailExpand 1.2s ease-out infinite;
}

@keyframes rhythmPulse {
  0%, 100% { 
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.4;
  }
  50% { 
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.8;
  }
}

@keyframes trailExpand {
  0% { 
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.6;
  }
  100% { 
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

.dancer-name {
  color: white;
  font-size: 16px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.combo-counter {
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  border-radius: 12px;
  padding: 6px 12px;
  font-weight: 600;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: comboFloat 0.5s ease-out;
}

.combo-text {
  font-size: 10px;
  opacity: 0.8;
}

.combo-number {
  font-size: 18px;
  font-weight: 800;
}

@keyframes comboFloat {
  0% { 
    transform: translateX(-50%) translateY(20px) scale(0.5);
    opacity: 0;
  }
  100% { 
    transform: translateX(-50%) translateY(0) scale(1);
    opacity: 1;
  }
}

/* 节拍指示器 */
.beat-indicators {
  display: flex;
  gap: 12px;
  margin-bottom: 40px;
}

.beat-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: all 0.2s ease;
}

.beat-dot.active {
  background: #3b82f6;
  transform: scale(1.3);
  box-shadow: 0 0 16px rgba(59, 130, 246, 0.6);
}

.beat-dot.perfect {
  background: #10b981;
  animation: perfectBeat 0.3s ease-out;
}

.beat-dot.good {
  background: #fbbf24;
  animation: goodBeat 0.3s ease-out;
}

.beat-dot.miss {
  background: #ef4444;
  animation: missBeat 0.3s ease-out;
}

@keyframes perfectBeat {
  0% { transform: scale(1); }
  50% { transform: scale(1.8); }
  100% { transform: scale(1.3); }
}

@keyframes goodBeat {
  0% { transform: scale(1); }
  50% { transform: scale(1.6); }
  100% { transform: scale(1.3); }
}

@keyframes missBeat {
  0% { transform: scale(1); }
  50% { transform: scale(1.4); }
  100% { transform: scale(1.3); }
}

/* 舞蹈指令区 */
.dance-commands {
  position: absolute;
  top: 100px;
  left: 50%;
  transform: translateX(-50%);
  width: 300px;
  height: 400px;
  pointer-events: none;
}

.command-arrow {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.9);
  border: 3px solid #3b82f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #3b82f6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.command-arrow.up { left: 50%; }
.command-arrow.down { left: 50%; }
.command-arrow.left { left: 25%; }
.command-arrow.right { left: 75%; }

/* 表演反馈 */
.performance-feedback {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 5;
  pointer-events: none;
}

.feedback-popup {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 12px;
  padding: 16px 24px;
  text-align: center;
  animation: feedbackAppear 1s ease-out;
}

.feedback-popup.perfect {
  background: linear-gradient(135deg, #10b981, #059669);
  box-shadow: 0 0 24px rgba(16, 185, 129, 0.6);
}

.feedback-popup.good {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  box-shadow: 0 0 24px rgba(251, 191, 36, 0.6);
}

.feedback-popup.ok {
  background: linear-gradient(135deg, #6b7280, #4b5563);
}

.feedback-popup.miss {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  box-shadow: 0 0 24px rgba(239, 68, 68, 0.6);
}

.grade-text {
  font-size: 24px;
  font-weight: 800;
  margin-bottom: 4px;
}

.score-gained {
  font-size: 16px;
  opacity: 0.9;
}

@keyframes feedbackAppear {
  0% { 
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 0;
  }
  30% { 
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 1;
  }
  100% { 
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
}

/* 控制面板 */
.control-panel {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.9) 0%, transparent 100%);
  padding: 30px 20px;
  z-index: 10;
}

.mode-selection {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 20px;
}

.mode-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 12px 16px;
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 70px;
}

.mode-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.mode-btn.active {
  background: rgba(139, 92, 246, 0.3);
  border-color: rgba(139, 92, 246, 0.6);
  color: #c4b5fd;
}

.main-controls {
  display: flex;
  gap: 12px;
}

.control-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.control-button.secondary {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.control-button.secondary:hover {
  background: rgba(255, 255, 255, 0.2);
}

.control-button.primary {
  background: #10b981;
  border: 1px solid #10b981;
  color: white;
}

.control-button.primary:hover {
  background: #059669;
}

.control-button.primary.stop {
  background: #ef4444;
  border-color: #ef4444;
}

.control-button.primary.stop:hover {
  background: #dc2626;
}

/* 弹窗样式 */
.song-modal-overlay,
.settings-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

.song-modal,
.settings-modal {
  background: white;
  border-radius: 16px;
  width: 400px;
  max-width: 90vw;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
}

.modal-content {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

/* 歌曲列表 */
.song-list {
  margin-bottom: 20px;
}

.song-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 10px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.song-item:hover:not(.locked) {
  background: #f9fafb;
}

.song-item.selected {
  background: #eff6ff;
  border-color: #3b82f6;
}

.song-item.locked {
  opacity: 0.5;
  cursor: not-allowed;
}

.song-icon {
  width: 40px;
  height: 40px;
  background: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
}

.song-details {
  flex: 1;
}

.song-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.song-artist {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 6px;
}

.song-info {
  display: flex;
  gap: 12px;
  font-size: 12px;
}

.song-duration {
  color: #9ca3af;
}

.song-difficulty {
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.song-difficulty.easy {
  background: #dcfce7;
  color: #16a34a;
}

.song-difficulty.normal {
  background: #fef3c7;
  color: #d97706;
}

.song-difficulty.hard {
  background: #fee2e2;
  color: #dc2626;
}

.song-difficulty.expert {
  background: #fdf4ff;
  color: #a855f7;
}

.song-actions {
  display: flex;
  gap: 8px;
}

.preview-btn {
  width: 32px;
  height: 32px;
  background: #f3f4f6;
  border: none;
  border-radius: 50%;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.preview-btn:hover {
  background: #e5e7eb;
}

/* 设置内容 */
.setting-group {
  margin-bottom: 24px;
}

.setting-group h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #374151;
  font-weight: 600;
}

.difficulty-options {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
}

.difficulty-btn {
  padding: 8px 12px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.difficulty-btn:hover {
  background: #f3f4f6;
}

.difficulty-btn.active {
  background: #eff6ff;
  border-color: #3b82f6;
  color: #3b82f6;
}

.effect-toggles {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.toggle-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.volume-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.volume-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.volume-item label {
  min-width: 80px;
  font-size: 14px;
  color: #4b5563;
}

.volume-slider {
  flex: 1;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  outline: none;
  cursor: pointer;
}

.volume-slider::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  background: #3b82f6;
  border-radius: 50%;
  cursor: pointer;
}

.modal-actions {
  display: flex;
  gap: 12px;
}

.modal-btn {
  flex: 1;
  height: 44px;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modal-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.modal-btn.secondary {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  color: #6b7280;
}

.modal-btn.primary {
  background: #3b82f6;
  border: 1px solid #3b82f6;
  color: white;
}

/* 状态提示 */
.status-tip-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 15;
}

.status-tip {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  backdrop-filter: blur(10px);
}

/* 动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-bar {
    padding: 0 16px;
  }
  
  .dance-stage {
    padding: 70px 16px 140px;
  }
  
  .dancer-area {
    gap: 40px;
  }
  
  .dancer-avatar {
    width: 100px;
    height: 100px;
  }
  
  .mode-selection {
    gap: 8px;
    margin-bottom: 16px;
  }
  
  .mode-btn {
    padding: 8px 12px;
    min-width: 60px;
    font-size: 10px;
  }
  
  .control-panel {
    padding: 20px 16px;
  }
  
  .song-modal,
  .settings-modal {
    width: 350px;
  }
  
  .difficulty-options {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>