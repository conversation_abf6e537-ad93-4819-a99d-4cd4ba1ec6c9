<template>
  <div class="meetup-container">
    <!-- 增强背景 -->
    <EnhancedBackground
      :video-url="backgroundVideoUrl"
      :image-url="backgroundImageUrl"
      :blur="showMeetupModal"
      transition-mode="crossfade"
      :transition-duration="800"
    />
    
    <!-- 约会界面 -->
    <div class="meetup-interface">
      <!-- 顶部状态栏 -->
      <div class="status-bar">
        <div class="meetup-info">
          <div class="location-badge">
            <Icon name="lucide:map-pin" />
            <span>{{ currentLocation?.name || '未选择地点' }}</span>
          </div>
          <div class="time-badge">
            <Icon name="lucide:clock" />
            <span>{{ meetupTime }}</span>
          </div>
        </div>
        
        <div class="status-actions">
          <button class="status-btn" @click="showLocationPicker">
            <Icon name="lucide:map" />
          </button>
          <button class="status-btn" @click="showTimePicker">
            <Icon name="lucide:calendar" />
          </button>
        </div>
      </div>
      
      <!-- 角色展示区 -->
      <div class="character-display">
        <div class="character-container" :class="{ 'interacting': isInteracting }">
          <!-- 角色头像 -->
          <div class="character-avatar" @click="interactWithCharacter">
            <img v-if="currentActor?.avatar" :src="currentActor.avatar" :alt="currentActor.name" />
            <div v-else class="avatar-fallback">
              <Icon name="lucide:user" />
            </div>
            
            <!-- 互动指示器 -->
            <div v-if="showInteractionHint" class="interaction-hint">
              <div class="hint-icon">👆</div>
              <span>点击互动</span>
            </div>
            
            <!-- 心情状态 -->
            <div class="mood-indicator" :class="currentMood">
              <Icon :name="moodIcon" />
            </div>
          </div>
          
          <!-- 角色信息 -->
          <div class="character-info">
            <h2 class="character-name">{{ currentActor?.name || 'Unknown' }}</h2>
            <div class="character-status">{{ characterStatus }}</div>
            <div class="affection-level">
              <Icon name="lucide:heart" />
              <span>好感度: {{ affectionLevel }}%</span>
            </div>
          </div>
        </div>
        
        <!-- 特效层 */
        <div class="effects-layer">
          <!-- 心形粒子 -->
          <HeartParticles 
            ref="heartParticlesRef"
            :auto-emit="false"
            :particle-count="12"
            color="#ff69b4"
          />
          
          <!-- 互动特效 -->
          <div v-if="showInteractionEffect" class="interaction-effect">
            <div class="effect-ripple"></div>
          </div>
        </div>
      </div>
      
      <!-- 约会活动选择 -->
      <div class="activity-selection">
        <h3 class="section-title">选择约会活动</h3>
        
        <div class="activity-grid">
          <div 
            v-for="activity in activities" 
            :key="activity.id"
            class="activity-card"
            :class="{ 
              'selected': selectedActivityId === activity.id,
              'locked': activity.locked,
              'recommended': activity.recommended 
            }"
            @click="selectActivity(activity)"
          >
            <div class="activity-icon">
              <Icon :name="activity.icon" />
              <div v-if="activity.locked" class="lock-overlay">
                <Icon name="lucide:lock" />
              </div>
            </div>
            
            <div class="activity-info">
              <div class="activity-name">{{ activity.name }}</div>
              <div class="activity-description">{{ activity.description }}</div>
              
              <!-- 活动属性 -->
              <div class="activity-attributes">
                <span class="attribute romance" v-if="activity.romance">
                  <Icon name="lucide:heart" />
                  浪漫
                </span>
                <span class="attribute fun" v-if="activity.fun">
                  <Icon name="lucide:smile" />
                  有趣
                </span>
                <span class="attribute intimate" v-if="activity.intimate">
                  <Icon name="lucide:users" />
                  亲密
                </span>
              </div>
              
              <!-- 推荐标签 -->
              <div v-if="activity.recommended" class="recommended-badge">
                推荐
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 底部操作栏 -->
      <div class="action-bar">
        <!-- 快捷互动 -->
        <div class="quick-interactions">
          <button 
            v-for="interaction in quickInteractions" 
            :key="interaction.id"
            class="interaction-btn"
            @click="performInteraction(interaction)"
          >
            <Icon :name="interaction.icon" />
            <span>{{ interaction.name }}</span>
          </button>
        </div>
        
        <!-- 主要操作 -->
        <div class="main-actions">
          <button class="action-button secondary" @click="goBack">
            <Icon name="lucide:arrow-left" />
            <span>返回</span>
          </button>
          
          <button 
            class="action-button primary" 
            :disabled="!selectedActivityId"
            @click="startMeetup"
          >
            <Icon name="lucide:play" />
            <span>开始约会</span>
          </button>
        </div>
      </div>
    </div>
    
    <!-- 位置选择器弹窗 -->
    <Transition name="fade">
      <div v-if="showLocationModal" class="location-modal-overlay" @click="hideLocationPicker">
        <div class="location-modal" @click.stop>
          <div class="modal-header">
            <h3>选择约会地点</h3>
            <button class="close-btn" @click="hideLocationPicker">
              <Icon name="lucide:x" />
            </button>
          </div>
          
          <div class="modal-content">
            <div class="location-list">
              <div 
                v-for="location in availableLocations" 
                :key="location.id"
                class="location-item"
                :class="{ selected: selectedLocationId === location.id }"
                @click="selectLocation(location)"
              >
                <div class="location-icon">
                  <Icon :name="location.icon" />
                </div>
                <div class="location-details">
                  <div class="location-name">{{ location.name }}</div>
                  <div class="location-address">{{ location.address }}</div>
                  <div class="location-attributes">
                    <span v-if="location.romantic" class="attr romantic">浪漫</span>
                    <span v-if="location.quiet" class="attr quiet">安静</span>
                    <span v-if="location.popular" class="attr popular">热门</span>
                  </div>
                </div>
                <div class="location-rating">
                  <Icon name="lucide:star" />
                  <span>{{ location.rating }}</span>
                </div>
              </div>
            </div>
            
            <div class="modal-actions">
              <button class="modal-btn secondary" @click="hideLocationPicker">
                取消
              </button>
              <button class="modal-btn primary" @click="confirmLocation">
                确认选择
              </button>
            </div>
          </div>
        </div>
      </div>
    </Transition>
    
    <!-- 时间选择器弹窗 -->
    <Transition name="fade">
      <div v-if="showTimeModal" class="time-modal-overlay" @click="hideTimePicker">
        <div class="time-modal" @click.stop>
          <div class="modal-header">
            <h3>选择约会时间</h3>
            <button class="close-btn" @click="hideTimePicker">
              <Icon name="lucide:x" />
            </button>
          </div>
          
          <div class="modal-content">
            <div class="time-options">
              <div class="time-section">
                <h4>时段</h4>
                <div class="time-periods">
                  <button 
                    v-for="period in timePeriods" 
                    :key="period.id"
                    class="time-period-btn"
                    :class="{ active: selectedTimePeriod === period.id }"
                    @click="selectTimePeriod(period.id)"
                  >
                    <Icon :name="period.icon" />
                    <span>{{ period.name }}</span>
                    <div class="period-time">{{ period.time }}</div>
                  </button>
                </div>
              </div>
            </div>
            
            <div class="modal-actions">
              <button class="modal-btn secondary" @click="hideTimePicker">
                取消
              </button>
              <button class="modal-btn primary" @click="confirmTime">
                确认时间
              </button>
            </div>
          </div>
        </div>
      </div>
    </Transition>
    
    <!-- 约会开始确认弹窗 -->
    <Transition name="fade">
      <div v-if="showConfirmModal" class="confirm-modal-overlay" @click="hideConfirmModal">
        <div class="confirm-modal" @click.stop>
          <div class="modal-header">
            <h3>确认开始约会</h3>
          </div>
          
          <div class="modal-content">
            <div class="meetup-summary">
              <div class="summary-item">
                <Icon name="lucide:map-pin" />
                <span>地点: {{ currentLocation?.name }}</span>
              </div>
              <div class="summary-item">
                <Icon name="lucide:clock" />
                <span>时间: {{ meetupTime }}</span>
              </div>
              <div class="summary-item">
                <Icon name="lucide:activity" />
                <span>活动: {{ selectedActivity?.name }}</span>
              </div>
            </div>
            
            <div class="expectation-tips">
              <h4>约会小贴士</h4>
              <ul>
                <li>保持轻松愉快的心情</li>
                <li>展现你的绅士风度</li>
                <li>倾听对方的想法</li>
                <li>享受美好的时光</li>
              </ul>
            </div>
          </div>
          
          <div class="modal-actions">
            <button class="modal-btn secondary" @click="hideConfirmModal">
              再想想
            </button>
            <button class="modal-btn primary" @click="confirmStartMeetup">
              开始约会
            </button>
          </div>
        </div>
      </div>
    </Transition>
    
    <!-- 状态提示 -->
    <Transition name="fade">
      <div v-if="showStatusTip" class="status-tip-overlay">
        <div class="status-tip">
          <Icon :name="statusTipIcon" />
          <span>{{ statusTipText }}</span>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
// 显式导入组件
import EnhancedBackground from '~/components/common/EnhancedBackground.vue'
import HeartParticles from '~/components/Chat4/ui/HeartParticles.vue'
import { useChat4State } from '~/composables/chat4/useChat4State'
import { useChat4Events } from '~/composables/chat4/useChat4Events'

// 约会活动类型
interface Activity {
  id: string
  name: string
  description: string
  icon: string
  romance: boolean
  fun: boolean
  intimate: boolean
  recommended: boolean
  locked: boolean
  requiredAffection: number
}

// 地点类型
interface Location {
  id: string
  name: string
  address: string
  icon: string
  rating: number
  romantic: boolean
  quiet: boolean
  popular: boolean
}

// 快捷互动类型
interface QuickInteraction {
  id: string
  name: string
  icon: string
  affectionGain: number
}

// 时间段类型
interface TimePeriod {
  id: string
  name: string
  time: string
  icon: string
}

// Chat4 状态管理
const { state, sceneNavigation } = useChat4State()
const { currentActor } = storeToRefs(state)

// 事件管理
const { emit: emitEvent, interactionEvents } = useChat4Events()

// 组件引用
const heartParticlesRef = ref()

// 界面状态
const showLocationModal = ref(false)
const showTimeModal = ref(false)
const showConfirmModal = ref(false)
const showStatusTip = ref(false)
const statusTipText = ref('')
const statusTipIcon = ref('lucide:info')

// 约会状态
const selectedActivityId = ref('')
const selectedLocationId = ref('')
const selectedTimePeriod = ref('afternoon')
const affectionLevel = ref(65)
const currentMood = ref('happy')

// 角色状态
const isInteracting = ref(false)
const showInteractionHint = ref(true)
const showInteractionEffect = ref(false)
const characterStatus = ref('期待与你约会')

// 背景状态
const backgroundVideoUrl = ref('')
const backgroundImageUrl = ref('https://picsum.photos/1920/1080?random=400')

// 约会活动数据
const activities: Activity[] = [
  {
    id: 'coffee-date',
    name: '咖啡约会',
    description: '在温馨的咖啡厅里享受悠闲时光',
    icon: 'lucide:coffee',
    romance: true,
    fun: false,
    intimate: true,
    recommended: true,
    locked: false,
    requiredAffection: 0
  },
  {
    id: 'movie-date',
    name: '电影约会',
    description: '一起观看浪漫电影，分享爆米花',
    icon: 'lucide:film',
    romance: true,
    fun: true,
    intimate: false,
    recommended: false,
    locked: false,
    requiredAffection: 20
  },
  {
    id: 'park-walk',
    name: '公园漫步',
    description: '在美丽的公园里悠闲散步聊天',
    icon: 'lucide:trees',
    romance: true,
    fun: false,
    intimate: true,
    recommended: false,
    locked: false,
    requiredAffection: 10
  },
  {
    id: 'dinner-date',
    name: '浪漫晚餐',
    description: '在高档餐厅享受烛光晚餐',
    icon: 'lucide:utensils',
    romance: true,
    fun: false,
    intimate: true,
    recommended: false,
    locked: affectionLevel.value < 50,
    requiredAffection: 50
  },
  {
    id: 'shopping',
    name: '购物约会',
    description: '一起逛街购物，挑选心仪的物品',
    icon: 'lucide:shopping-bag',
    romance: false,
    fun: true,
    intimate: false,
    recommended: false,
    locked: false,
    requiredAffection: 30
  },
  {
    id: 'home-date',
    name: '居家约会',
    description: '在温馨的家里共度私密时光',
    icon: 'lucide:home',
    romance: true,
    fun: false,
    intimate: true,
    recommended: false,
    locked: affectionLevel.value < 80,
    requiredAffection: 80
  }
]

// 可用地点
const availableLocations: Location[] = [
  {
    id: 'cafe-1',
    name: '浪漫咖啡厅',
    address: '市中心文艺街123号',
    icon: 'lucide:coffee',
    rating: 4.8,
    romantic: true,
    quiet: true,
    popular: false
  },
  {
    id: 'restaurant-1',
    name: '法式餐厅',
    address: '商业区高端餐饮楼3F',
    icon: 'lucide:utensils',
    rating: 4.9,
    romantic: true,
    quiet: true,
    popular: true
  },
  {
    id: 'park-1',
    name: '城市公园',
    address: '市中心绿化带',
    icon: 'lucide:trees',
    rating: 4.6,
    romantic: true,
    quiet: true,
    popular: false
  },
  {
    id: 'cinema-1',
    name: '豪华影院',
    address: '购物中心5F',
    icon: 'lucide:film',
    rating: 4.7,
    romantic: false,
    quiet: false,
    popular: true
  }
]

// 快捷互动选项
const quickInteractions: QuickInteraction[] = [
  { id: 'compliment', name: '称赞', icon: 'lucide:heart', affectionGain: 2 },
  { id: 'gift', name: '送礼', icon: 'lucide:gift', affectionGain: 5 },
  { id: 'hug', name: '拥抱', icon: 'lucide:users', affectionGain: 3 },
  { id: 'chat', name: '聊天', icon: 'lucide:message-circle', affectionGain: 1 }
]

// 时间段选项
const timePeriods: TimePeriod[] = [
  { id: 'morning', name: '上午', time: '10:00-12:00', icon: 'lucide:sunrise' },
  { id: 'afternoon', name: '下午', time: '14:00-17:00', icon: 'lucide:sun' },
  { id: 'evening', name: '傍晚', time: '18:00-20:00', icon: 'lucide:sunset' },
  { id: 'night', name: '夜晚', time: '20:00-22:00', icon: 'lucide:moon' }
]

// 计算属性
const selectedActivity = computed(() => 
  activities.find(activity => activity.id === selectedActivityId.value)
)

const currentLocation = computed(() => 
  availableLocations.find(location => location.id === selectedLocationId.value)
)

const meetupTime = computed(() => {
  const period = timePeriods.find(p => p.id === selectedTimePeriod.value)
  return period ? period.time : '未选择'
})

const moodIcon = computed(() => {
  switch (currentMood.value) {
    case 'happy': return 'lucide:smile'
    case 'excited': return 'lucide:zap'
    case 'shy': return 'lucide:eye-off'
    case 'romantic': return 'lucide:heart'
    default: return 'lucide:smile'
  }
})

const showMeetupModal = computed(() => 
  showLocationModal.value || showTimeModal.value || showConfirmModal.value
)

// 角色互动
const interactWithCharacter = async () => {
  if (isInteracting.value) return
  
  isInteracting.value = true
  showInteractionEffect.value = true
  showInteractionHint.value = false
  
  // 触发心形粒子
  if (heartParticlesRef.value) {
    heartParticlesRef.value.emitParticles()
  }
  
  // 增加好感度
  affectionLevel.value = Math.min(100, affectionLevel.value + 1)
  
  // 更新角色状态
  const responses = [
    '很开心见到你~',
    '你今天看起来很帅呢',
    '期待我们的约会',
    '想和你一起度过美好时光'
  ]
  
  characterStatus.value = responses[Math.floor(Math.random() * responses.length)]
  
  // 触发事件
  interactionEvents.emitCharacterInteraction({
    type: 'touch',
    affectionGain: 1,
    response: characterStatus.value
  })
  
  setTimeout(() => {
    isInteracting.value = false
    showInteractionEffect.value = false
  }, 1500)
}

const performInteraction = async (interaction: QuickInteraction) => {
  // 增加好感度
  affectionLevel.value = Math.min(100, affectionLevel.value + interaction.affectionGain)
  
  // 更新角色状态
  const responses = {
    compliment: '谢谢你的夸奖，我很开心~',
    gift: '哇，这个礼物我很喜欢！',
    hug: '你的拥抱让我感到很温暖',
    chat: '和你聊天总是很愉快'
  }
  
  characterStatus.value = responses[interaction.id as keyof typeof responses] || '谢谢你~'
  
  // 显示提示
  showStatusTip(`好感度 +${interaction.affectionGain}`)
  setTimeout(hideStatusTip, 1500)
  
  // 触发特效
  if (heartParticlesRef.value && interaction.affectionGain > 2) {
    heartParticlesRef.value.emitParticles()
  }
  
  // 触发事件
  interactionEvents.emitCharacterInteraction({
    type: interaction.id,
    affectionGain: interaction.affectionGain,
    response: characterStatus.value
  })
}

// 活动选择
const selectActivity = (activity: Activity) => {
  if (activity.locked) {
    showStatusTip(`需要好感度达到 ${activity.requiredAffection}% 才能解锁`)
    setTimeout(hideStatusTip, 2000)
    return
  }
  
  selectedActivityId.value = activity.id
  showStatusTip(`已选择: ${activity.name}`)
  setTimeout(hideStatusTip, 1500)
}

// 地点选择
const showLocationPicker = () => {
  showLocationModal.value = true
}

const hideLocationPicker = () => {
  showLocationModal.value = false
}

const selectLocation = (location: Location) => {
  selectedLocationId.value = location.id
}

const confirmLocation = () => {
  if (currentLocation.value) {
    showStatusTip(`已选择地点: ${currentLocation.value.name}`)
    setTimeout(hideStatusTip, 1500)
  }
  hideLocationPicker()
}

// 时间选择
const showTimePicker = () => {
  showTimeModal.value = true
}

const hideTimePicker = () => {
  showTimeModal.value = false
}

const selectTimePeriod = (periodId: string) => {
  selectedTimePeriod.value = periodId
}

const confirmTime = () => {
  const period = timePeriods.find(p => p.id === selectedTimePeriod.value)
  if (period) {
    showStatusTip(`已选择时间: ${period.name}`)
    setTimeout(hideStatusTip, 1500)
  }
  hideTimePicker()
}

// 约会开始
const startMeetup = () => {
  if (!selectedActivityId.value) {
    showStatusTip('请先选择约会活动')
    setTimeout(hideStatusTip, 2000)
    return
  }
  
  showConfirmModal.value = true
}

const hideConfirmModal = () => {
  showConfirmModal.value = false
}

const confirmStartMeetup = () => {
  showStatusTip('约会即将开始...')
  
  // 触发约会开始事件
  interactionEvents.emitSceneNavigation('dating', {
    activity: selectedActivity.value,
    location: currentLocation.value,
    time: meetupTime.value,
    affectionLevel: affectionLevel.value
  })
  
  setTimeout(() => {
    hideStatusTip()
    hideConfirmModal()
    
    // 根据活动类型跳转到不同场景
    if (selectedActivity.value?.id === 'home-date') {
      sceneNavigation.goToChat()
    } else {
      sceneNavigation.goToLiving()
    }
  }, 2000)
}

// 导航控制
const goBack = () => {
  sceneNavigation.goToMap()
}

// 工具方法
const showStatusTip = (text: string, icon: string = 'lucide:info') => {
  statusTipText.value = text
  statusTipIcon.value = icon
  showStatusTip.value = true
}

const hideStatusTip = () => {
  showStatusTip.value = false
}

// 生命周期
onMounted(() => {
  // 默认选择推荐活动
  const recommendedActivity = activities.find(activity => activity.recommended && !activity.locked)
  if (recommendedActivity) {
    selectedActivityId.value = recommendedActivity.id
  }
  
  // 默认选择第一个地点
  if (availableLocations.length > 0) {
    selectedLocationId.value = availableLocations[0].id
  }
  
  // 显示互动提示
  setTimeout(() => {
    showInteractionHint.value = true
  }, 2000)
})
</script>

<style scoped>
.meetup-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

/* 约会界面 */
.meetup-interface {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  z-index: 2;
}

/* 状态栏 */
.status-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, transparent 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  z-index: 10;
}

.meetup-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.location-badge,
.time-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 12px;
  color: white;
  backdrop-filter: blur(10px);
}

.status-actions {
  display: flex;
  gap: 8px;
}

.status-btn {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.status-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 角色展示区 */
.character-display {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px 20px 200px;
  position: relative;
}

.character-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  transition: transform 0.3s ease;
}

.character-container.interacting {
  transform: scale(1.05);
}

.character-avatar {
  position: relative;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.character-avatar:hover {
  border-color: rgba(255, 255, 255, 0.6);
  transform: scale(1.05);
}

.character-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-fallback {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 80px;
  color: rgba(255, 255, 255, 0.6);
}

.interaction-hint {
  position: absolute;
  top: -60px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 12px;
  padding: 8px 12px;
  font-size: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  animation: bounce 2s infinite;
}

.hint-icon {
  font-size: 20px;
}

.mood-indicator {
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.mood-indicator.happy {
  color: #10b981;
}

.mood-indicator.excited {
  color: #f59e0b;
}

.mood-indicator.shy {
  color: #8b5cf6;
}

.mood-indicator.romantic {
  color: #ef4444;
}

.character-info {
  text-align: center;
  color: white;
}

.character-name {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.character-status {
  margin-bottom: 12px;
  font-size: 16px;
  opacity: 0.9;
}

.affection-level {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: 14px;
  color: #ff69b4;
}

/* 特效层 */
.effects-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 5;
}

.interaction-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.effect-ripple {
  width: 100px;
  height: 100px;
  border: 3px solid rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: ripple 1.5s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(3);
    opacity: 0;
  }
}

/* 活动选择 */
.activity-selection {
  position: absolute;
  bottom: 120px;
  left: 0;
  right: 0;
  padding: 0 20px;
  z-index: 8;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: white;
  text-align: center;
}

.activity-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.activity-card {
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  backdrop-filter: blur(10px);
}

.activity-card:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.activity-card.selected {
  background: rgba(59, 130, 246, 0.3);
  border-color: rgba(59, 130, 246, 0.6);
}

.activity-card.locked {
  opacity: 0.5;
  cursor: not-allowed;
}

.activity-card.recommended::after {
  content: '推荐';
  position: absolute;
  top: 8px;
  right: 8px;
  background: #10b981;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
}

.activity-icon {
  position: relative;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  margin-bottom: 12px;
}

.lock-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ef4444;
}

.activity-info {
  color: white;
}

.activity-name {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
}

.activity-description {
  font-size: 12px;
  opacity: 0.8;
  margin-bottom: 8px;
}

.activity-attributes {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.attribute {
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 4px;
}

.attribute.romance {
  background: rgba(239, 68, 68, 0.3);
  color: #fca5a5;
}

.attribute.fun {
  background: rgba(245, 158, 11, 0.3);
  color: #fcd34d;
}

.attribute.intimate {
  background: rgba(139, 92, 246, 0.3);
  color: #c4b5fd;
}

/* 操作栏 */
.action-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.9) 0%, transparent 100%);
  padding: 20px;
  z-index: 10;
}

.quick-interactions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 16px;
}

.interaction-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 12px 16px;
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 60px;
}

.interaction-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.main-actions {
  display: flex;
  gap: 12px;
}

.action-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-button.secondary {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.action-button.secondary:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
}

.action-button.primary {
  background: #3b82f6;
  border: 1px solid #3b82f6;
  color: white;
}

.action-button.primary:hover:not(:disabled) {
  background: #2563eb;
}

/* 弹窗样式 */
.location-modal-overlay,
.time-modal-overlay,
.confirm-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

.location-modal,
.time-modal,
.confirm-modal {
  background: white;
  border-radius: 16px;
  width: 400px;
  max-width: 90vw;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
}

.modal-content {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.location-list {
  margin-bottom: 20px;
}

.location-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 10px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.location-item:hover {
  background: #f9fafb;
}

.location-item.selected {
  background: #eff6ff;
  border-color: #3b82f6;
}

.location-icon {
  width: 40px;
  height: 40px;
  background: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
}

.location-details {
  flex: 1;
}

.location-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.location-address {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 6px;
}

.location-attributes {
  display: flex;
  gap: 4px;
}

.attr {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
}

.attr.romantic {
  background: #fee2e2;
  color: #ef4444;
}

.attr.quiet {
  background: #dbeafe;
  color: #3b82f6;
}

.attr.popular {
  background: #fef3c7;
  color: #f59e0b;
}

.location-rating {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #f59e0b;
  font-size: 14px;
}

.time-options {
  margin-bottom: 20px;
}

.time-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #1f2937;
}

.time-periods {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.time-period-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 16px 12px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.time-period-btn:hover {
  background: #f3f4f6;
}

.time-period-btn.active {
  background: #eff6ff;
  border-color: #3b82f6;
  color: #3b82f6;
}

.period-time {
  font-size: 12px;
  color: #6b7280;
}

.meetup-summary {
  margin-bottom: 20px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  color: #4b5563;
}

.expectation-tips {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
}

.expectation-tips h4 {
  margin: 0 0 12px 0;
  color: #1f2937;
}

.expectation-tips ul {
  margin: 0;
  padding-left: 20px;
  color: #6b7280;
}

.expectation-tips li {
  margin-bottom: 4px;
}

.modal-actions {
  display: flex;
  gap: 12px;
}

.modal-btn {
  flex: 1;
  height: 44px;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modal-btn.secondary {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  color: #6b7280;
}

.modal-btn.primary {
  background: #3b82f6;
  border: 1px solid #3b82f6;
  color: white;
}

/* 状态提示 */
.status-tip-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 15;
}

.status-tip {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  backdrop-filter: blur(10px);
}

/* 动画 */
@keyframes bounce {
  0%, 100% { transform: translateX(-50%) translateY(0); }
  50% { transform: translateX(-50%) translateY(-10px); }
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-bar {
    padding: 0 16px;
  }
  
  .character-display {
    padding: 70px 16px 180px;
  }
  
  .character-avatar {
    width: 150px;
    height: 150px;
  }
  
  .character-name {
    font-size: 20px;
  }
  
  .activity-selection {
    padding: 0 16px;
  }
  
  .activity-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .activity-card {
    padding: 12px;
  }
  
  .action-bar {
    padding: 16px;
  }
  
  .quick-interactions {
    gap: 8px;
  }
  
  .interaction-btn {
    padding: 8px 12px;
    min-width: 50px;
    font-size: 10px;
  }
  
  .location-modal,
  .time-modal,
  .confirm-modal {
    width: 350px;
  }
  
  .time-periods {
    grid-template-columns: 1fr;
  }
}
</style>