<template>
  <div class="map-container">
    <!-- 增强背景 -->
    <EnhancedBackground
      :video-url="backgroundVideoUrl"
      :image-url="backgroundImageUrl"
      transition-mode="zoom"
      :transition-duration="800"
    />
    
    <!-- 地图界面 -->
    <div class="map-interface">
      <!-- 顶部导航栏 -->
      <div class="top-navigation">
        <div class="nav-left">
          <button class="nav-btn" @click="goBack">
            <Icon name="lucide:arrow-left" />
          </button>
          <h1 class="page-title">选择地点</h1>
        </div>
        
        <div class="nav-right">
          <button class="nav-btn" @click="toggleSearch">
            <Icon name="lucide:search" />
          </button>
          <button class="nav-btn" @click="showLocationServices">
            <Icon name="lucide:map-pin" />
          </button>
        </div>
      </div>
      
      <!-- 搜索栏 -->
      <Transition name="slide-down">
        <div v-if="showSearchBar" class="search-bar">
          <div class="search-input-wrapper">
            <Icon name="lucide:search" class="search-icon" />
            <input 
              v-model="searchQuery" 
              type="text" 
              placeholder="搜索地点..." 
              class="search-input"
              @input="handleSearch"
            />
            <button v-if="searchQuery" class="clear-btn" @click="clearSearch">
              <Icon name="lucide:x" />
            </button>
          </div>
          
          <!-- 搜索建议 -->
          <div v-if="searchSuggestions.length" class="search-suggestions">
            <div 
              v-for="suggestion in searchSuggestions" 
              :key="suggestion.id"
              class="suggestion-item"
              @click="selectSuggestion(suggestion)"
            >
              <Icon :name="suggestion.icon" />
              <div class="suggestion-content">
                <div class="suggestion-name">{{ suggestion.name }}</div>
                <div class="suggestion-address">{{ suggestion.address }}</div>
              </div>
              <div class="suggestion-distance">{{ suggestion.distance }}km</div>
            </div>
          </div>
        </div>
      </Transition>
      
      <!-- 地图显示区域 -->
      <div class="map-display">
        <!-- 模拟地图 -->
        <div class="map-canvas" ref="mapCanvasRef">
          <!-- 地点标记 -->
          <div 
            v-for="location in visibleLocations" 
            :key="location.id"
            class="location-marker"
            :class="{ 
              'active': selectedLocationId === location.id,
              'recommended': location.recommended 
            }"
            :style="{ 
              left: location.position.x + '%', 
              top: location.position.y + '%' 
            }"
            @click="selectLocation(location)"
          >
            <div class="marker-icon">
              <Icon :name="location.icon" />
            </div>
            <div class="marker-label">{{ location.name }}</div>
            
            <!-- 推荐标签 -->
            <div v-if="location.recommended" class="recommended-badge">推荐</div>
          </div>
          
          <!-- 当前位置标记 -->
          <div 
            class="current-location-marker"
            :style="{ left: currentPosition.x + '%', top: currentPosition.y + '%' }"
          >
            <div class="current-marker-pulse"></div>
            <Icon name="lucide:navigation" />
          </div>
        </div>
        
        <!-- 地图控制按钮 -->
        <div class="map-controls">
          <button class="control-btn" @click="zoomIn">
            <Icon name="lucide:zoom-in" />
          </button>
          <button class="control-btn" @click="zoomOut">
            <Icon name="lucide:zoom-out" />
          </button>
          <button class="control-btn" @click="centerToCurrentLocation">
            <Icon name="lucide:crosshair" />
          </button>
        </div>
      </div>
      
      <!-- 地点分类标签 -->
      <div class="category-tabs">
        <button 
          v-for="category in categories" 
          :key="category.id"
          class="category-tab"
          :class="{ active: activeCategory === category.id }"
          @click="switchCategory(category.id)"
        >
          <Icon :name="category.icon" />
          <span>{{ category.name }}</span>
        </button>
      </div>
      
      <!-- 底部地点信息 -->
      <div class="location-info-panel" :class="{ expanded: selectedLocation }">
        <div v-if="selectedLocation" class="location-details">
          <!-- 地点基本信息 -->
          <div class="location-header">
            <div class="location-main-info">
              <h3 class="location-name">{{ selectedLocation.name }}</h3>
              <div class="location-meta">
                <span class="location-category">
                  <Icon :name="selectedLocation.categoryIcon" />
                  {{ selectedLocation.category }}
                </span>
                <span class="location-rating">
                  <Icon name="lucide:star" />
                  {{ selectedLocation.rating }}
                </span>
                <span class="location-distance">
                  <Icon name="lucide:map-pin" />
                  {{ selectedLocation.distanceFromUser }}km
                </span>
              </div>
            </div>
            
            <div class="location-actions">
              <button class="action-btn favorite" @click="toggleFavorite">
                <Icon :name="selectedLocation.isFavorite ? 'lucide:heart' : 'lucide:heart'" 
                      :class="{ 'filled': selectedLocation.isFavorite }" />
              </button>
              <button class="action-btn share" @click="shareLocation">
                <Icon name="lucide:share" />
              </button>
            </div>
          </div>
          
          <!-- 地点描述 -->
          <div class="location-description">
            <p>{{ selectedLocation.description }}</p>
          </div>
          
          <!-- 地点特色标签 -->
          <div class="location-tags">
            <span 
              v-for="tag in selectedLocation.tags" 
              :key="tag"
              class="location-tag"
            >
              {{ tag }}
            </span>
          </div>
          
          <!-- 营业时间和价格信息 -->
          <div class="location-info-grid">
            <div class="info-item">
              <Icon name="lucide:clock" />
              <div class="info-content">
                <div class="info-label">营业时间</div>
                <div class="info-value">{{ selectedLocation.openHours }}</div>
              </div>
            </div>
            
            <div class="info-item">
              <Icon name="lucide:dollar-sign" />
              <div class="info-content">
                <div class="info-label">价格区间</div>
                <div class="info-value">{{ selectedLocation.priceRange }}</div>
              </div>
            </div>
            
            <div class="info-item">
              <Icon name="lucide:phone" />
              <div class="info-content">
                <div class="info-label">联系电话</div>
                <div class="info-value">{{ selectedLocation.phone || '暂无' }}</div>
              </div>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="location-actions-panel">
            <button class="action-button secondary" @click="showDirections">
              <Icon name="lucide:navigation" />
              <span>查看路线</span>
            </button>
            
            <button class="action-button primary" @click="confirmSelection">
              <Icon name="lucide:check" />
              <span>选择此地点</span>
            </button>
          </div>
        </div>
        
        <!-- 默认提示 -->
        <div v-else class="location-placeholder">
          <Icon name="lucide:map-pin" />
          <p>点击地图上的标记查看地点详情</p>
        </div>
      </div>
    </div>
    
    <!-- 位置服务弹窗 -->
    <Transition name="fade">
      <div v-if="showLocationModal" class="location-modal-overlay" @click="hideLocationServices">
        <div class="location-modal" @click.stop>
          <div class="modal-header">
            <h3>位置服务</h3>
            <button class="close-btn" @click="hideLocationServices">
              <Icon name="lucide:x" />
            </button>
          </div>
          
          <div class="modal-content">
            <div class="location-permission">
              <Icon name="lucide:map-pin" class="permission-icon" />
              <h4>获取您的位置</h4>
              <p>允许访问位置信息，为您推荐附近的地点</p>
              
              <div class="permission-actions">
                <button class="permission-btn secondary" @click="hideLocationServices">
                  暂不允许
                </button>
                <button class="permission-btn primary" @click="requestLocation">
                  允许访问
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
    
    <!-- 路线弹窗 -->
    <Transition name="fade">
      <div v-if="showDirectionsModal" class="directions-modal-overlay" @click="hideDirections">
        <div class="directions-modal" @click.stop>
          <div class="modal-header">
            <h3>前往路线</h3>
            <button class="close-btn" @click="hideDirections">
              <Icon name="lucide:x" />
            </button>
          </div>
          
          <div class="modal-content">
            <div class="route-info">
              <div class="route-summary">
                <div class="route-distance">
                  <Icon name="lucide:map" />
                  <span>{{ selectedLocation?.distanceFromUser || '0' }}km</span>
                </div>
                <div class="route-time">
                  <Icon name="lucide:clock" />
                  <span>约 {{ estimatedTime }} 分钟</span>
                </div>
              </div>
              
              <div class="transport-options">
                <button 
                  v-for="transport in transportOptions" 
                  :key="transport.id"
                  class="transport-btn"
                  :class="{ active: selectedTransport === transport.id }"
                  @click="selectTransport(transport.id)"
                >
                  <Icon :name="transport.icon" />
                  <span>{{ transport.name }}</span>
                  <div class="transport-time">{{ transport.time }}分钟</div>
                </button>
              </div>
              
              <div class="route-actions">
                <button class="route-action-btn secondary" @click="hideDirections">
                  取消
                </button>
                <button class="route-action-btn primary" @click="startNavigation">
                  开始导航
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
    
    <!-- 状态提示 -->
    <Transition name="fade">
      <div v-if="showStatusTip" class="status-tip-overlay">
        <div class="status-tip">
          <Icon :name="statusTipIcon" />
          <span>{{ statusTipText }}</span>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
// 显式导入组件
import EnhancedBackground from '~/components/common/EnhancedBackground.vue'
import { useChat4State } from '~/composables/chat4/useChat4State'
import { useChat4Events } from '~/composables/chat4/useChat4Events'

// 地点类型定义
interface Location {
  id: string
  name: string
  category: string
  categoryIcon: string
  icon: string
  position: { x: number; y: number }
  address: string
  description: string
  rating: number
  distanceFromUser: number
  openHours: string
  priceRange: string
  phone?: string
  tags: string[]
  recommended: boolean
  isFavorite: boolean
}

// 搜索建议类型
interface SearchSuggestion {
  id: string
  name: string
  address: string
  icon: string
  distance: number
}

// 分类类型
interface Category {
  id: string
  name: string
  icon: string
}

// 交通方式类型
interface TransportOption {
  id: string
  name: string
  icon: string
  time: number
}

// Chat4 状态管理
const { state, sceneNavigation } = useChat4State()
const { currentActor } = storeToRefs(state)

// 事件管理
const { emit: emitEvent, interactionEvents } = useChat4Events()

// 组件引用
const mapCanvasRef = ref<HTMLElement>()

// 界面状态
const showSearchBar = ref(false)
const showLocationModal = ref(false)
const showDirectionsModal = ref(false)
const showStatusTip = ref(false)
const statusTipText = ref('')
const statusTipIcon = ref('lucide:info')

// 搜索状态
const searchQuery = ref('')
const searchSuggestions = ref<SearchSuggestion[]>([])

// 地图状态
const selectedLocationId = ref<string>('')
const activeCategory = ref('all')
const currentPosition = ref({ x: 50, y: 50 })
const mapZoom = ref(1)

// 交通选择
const selectedTransport = ref('car')
const estimatedTime = ref(15)

// 背景状态
const backgroundVideoUrl = ref('')
const backgroundImageUrl = ref('https://picsum.photos/1920/1080?random=300')

// 地点分类
const categories: Category[] = [
  { id: 'all', name: '全部', icon: 'lucide:map' },
  { id: 'restaurant', name: '餐厅', icon: 'lucide:utensils' },
  { id: 'cafe', name: '咖啡厅', icon: 'lucide:coffee' },
  { id: 'shopping', name: '购物', icon: 'lucide:shopping-bag' },
  { id: 'entertainment', name: '娱乐', icon: 'lucide:gamepad-2' },
  { id: 'scenic', name: '景点', icon: 'lucide:camera' }
]

// 交通方式选项
const transportOptions: TransportOption[] = [
  { id: 'walk', name: '步行', icon: 'lucide:footprints', time: 25 },
  { id: 'bike', name: '骑行', icon: 'lucide:bike', time: 12 },
  { id: 'car', name: '驾车', icon: 'lucide:car', time: 8 },
  { id: 'transit', name: '公交', icon: 'lucide:bus', time: 18 }
]

// 模拟地点数据
const allLocations: Location[] = [
  {
    id: 'restaurant-1',
    name: '浪漫法式餐厅',
    category: '餐厅',
    categoryIcon: 'lucide:utensils',
    icon: 'lucide:utensils',
    position: { x: 30, y: 25 },
    address: '市中心商业区A座3楼',
    description: '精致的法式料理，浪漫的用餐环境，适合情侣约会。',
    rating: 4.8,
    distanceFromUser: 2.3,
    openHours: '11:00-22:00',
    priceRange: '¥200-500',
    phone: '010-12345678',
    tags: ['浪漫', '法式料理', '情侣约会', '环境优雅'],
    recommended: true,
    isFavorite: false
  },
  {
    id: 'cafe-1',
    name: '静谧咖啡屋',
    category: '咖啡厅',
    categoryIcon: 'lucide:coffee',
    icon: 'lucide:coffee',
    position: { x: 45, y: 35 },
    address: '文艺街123号',
    description: '安静舒适的咖啡厅，提供精品咖啡和轻食。',
    rating: 4.6,
    distanceFromUser: 1.8,
    openHours: '08:00-22:00',
    priceRange: '¥30-80',
    tags: ['安静', '咖啡', '轻食', 'WiFi'],
    recommended: false,
    isFavorite: true
  },
  {
    id: 'shopping-1',
    name: '时尚购物中心',
    category: '购物',
    categoryIcon: 'lucide:shopping-bag',
    icon: 'lucide:shopping-bag',
    position: { x: 65, y: 40 },
    address: '购物大道888号',
    description: '大型购物中心，汇集各种品牌店铺。',
    rating: 4.5,
    distanceFromUser: 3.2,
    openHours: '10:00-22:00',
    priceRange: '¥50-2000',
    tags: ['购物', '品牌', '餐饮', '停车场'],
    recommended: true,
    isFavorite: false
  },
  {
    id: 'entertainment-1',
    name: 'KTV欢唱世界',
    category: '娱乐',
    categoryIcon: 'lucide:gamepad-2',
    icon: 'lucide:mic',
    position: { x: 25, y: 60 },
    address: '娱乐街66号',
    description: '高端KTV，音响设备一流，包间环境舒适。',
    rating: 4.4,
    distanceFromUser: 2.7,
    openHours: '14:00-02:00',
    priceRange: '¥100-300',
    tags: ['KTV', '聚会', '音响', '包间'],
    recommended: false,
    isFavorite: false
  },
  {
    id: 'scenic-1',
    name: '城市观景台',
    category: '景点',
    categoryIcon: 'lucide:camera',
    icon: 'lucide:mountain',
    position: { x: 70, y: 20 },
    address: '观景山顶',
    description: '俯瞰整个城市的最佳观景点，夜景尤其美丽。',
    rating: 4.9,
    distanceFromUser: 5.1,
    openHours: '06:00-23:00',
    priceRange: '免费',
    tags: ['观景', '拍照', '夜景', '免费'],
    recommended: true,
    isFavorite: true
  }
]

// 计算属性
const selectedLocation = computed(() => 
  allLocations.find(loc => loc.id === selectedLocationId.value)
)

const visibleLocations = computed(() => {
  if (activeCategory.value === 'all') {
    return allLocations
  }
  return allLocations.filter(loc => 
    loc.category === categories.find(cat => cat.id === activeCategory.value)?.name
  )
})

// 搜索功能
const handleSearch = () => {
  if (!searchQuery.value.trim()) {
    searchSuggestions.value = []
    return
  }
  
  // 模拟搜索建议
  const suggestions = allLocations
    .filter(loc => 
      loc.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      loc.address.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
    .slice(0, 5)
    .map(loc => ({
      id: loc.id,
      name: loc.name,
      address: loc.address,
      icon: loc.icon,
      distance: loc.distanceFromUser
    }))
  
  searchSuggestions.value = suggestions
}

const clearSearch = () => {
  searchQuery.value = ''
  searchSuggestions.value = []
}

const selectSuggestion = (suggestion: SearchSuggestion) => {
  searchQuery.value = suggestion.name
  searchSuggestions.value = []
  selectedLocationId.value = suggestion.id
  showSearchBar.value = false
}

// 界面控制
const toggleSearch = () => {
  showSearchBar.value = !showSearchBar.value
  if (showSearchBar.value) {
    // 自动聚焦搜索框
    nextTick(() => {
      const searchInput = document.querySelector('.search-input') as HTMLInputElement
      searchInput?.focus()
    })
  }
}

const showLocationServices = () => {
  showLocationModal.value = true
}

const hideLocationServices = () => {
  showLocationModal.value = false
}

const requestLocation = () => {
  showStatusTip('正在获取位置信息...')
  
  // 模拟位置获取
  setTimeout(() => {
    currentPosition.value = { x: 50, y: 50 }
    hideStatusTip()
    hideLocationServices()
    showStatusTip('位置获取成功')
    setTimeout(hideStatusTip, 2000)
  }, 2000)
}

// 地图控制
const zoomIn = () => {
  if (mapZoom.value < 3) {
    mapZoom.value += 0.5
    showStatusTip('地图已放大')
    setTimeout(hideStatusTip, 1000)
  }
}

const zoomOut = () => {
  if (mapZoom.value > 0.5) {
    mapZoom.value -= 0.5
    showStatusTip('地图已缩小')
    setTimeout(hideStatusTip, 1000)
  }
}

const centerToCurrentLocation = () => {
  showStatusTip('定位到当前位置')
  setTimeout(hideStatusTip, 1500)
}

// 地点选择
const selectLocation = (location: Location) => {
  selectedLocationId.value = location.id
  
  // 更新预计时间
  estimatedTime.value = Math.round(location.distanceFromUser * 5) // 简单估算
}

const switchCategory = (categoryId: string) => {
  activeCategory.value = categoryId
  selectedLocationId.value = '' // 清除选择
}

// 地点操作
const toggleFavorite = () => {
  if (selectedLocation.value) {
    selectedLocation.value.isFavorite = !selectedLocation.value.isFavorite
    showStatusTip(selectedLocation.value.isFavorite ? '已添加到收藏' : '已取消收藏')
    setTimeout(hideStatusTip, 1500)
  }
}

const shareLocation = () => {
  showStatusTip('位置信息已分享')
  setTimeout(hideStatusTip, 1500)
}

const showDirections = () => {
  showDirectionsModal.value = true
}

const hideDirections = () => {
  showDirectionsModal.value = false
}

const selectTransport = (transport: string) => {
  selectedTransport.value = transport
  const option = transportOptions.find(t => t.id === transport)
  if (option) {
    estimatedTime.value = option.time
  }
}

const startNavigation = () => {
  showStatusTip('正在启动导航...')
  setTimeout(() => {
    hideStatusTip()
    hideDirections()
    showStatusTip('导航已开始')
    setTimeout(hideStatusTip, 2000)
  }, 1500)
}

const confirmSelection = () => {
  if (selectedLocation.value) {
    showStatusTip(`已选择 ${selectedLocation.value.name}`)
    
    // 触发场景切换事件
    interactionEvents.emitSceneNavigation('meetup', {
      location: selectedLocation.value
    })
    
    setTimeout(() => {
      hideStatusTip()
      sceneNavigation.goToMeetup()
    }, 2000)
  }
}

// 导航控制
const goBack = () => {
  sceneNavigation.goToChat()
}

// 工具方法
const showStatusTip = (text: string, icon: string = 'lucide:info') => {
  statusTipText.value = text
  statusTipIcon.value = icon
  showStatusTip.value = true
}

const hideStatusTip = () => {
  showStatusTip.value = false
}

// 生命周期
onMounted(() => {
  // 默认选择推荐地点
  const recommendedLocation = allLocations.find(loc => loc.recommended)
  if (recommendedLocation) {
    setTimeout(() => {
      selectedLocationId.value = recommendedLocation.id
    }, 1000)
  }
})
</script>

<style scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background: #f8fafc;
  overflow: hidden;
}

/* 地图界面 */
.map-interface {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  z-index: 2;
}

/* 顶部导航栏 */
.top-navigation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  z-index: 10;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-btn {
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.05);
  border: none;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  background: rgba(0, 0, 0, 0.1);
}

.page-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

/* 搜索栏 */
.search-bar {
  position: absolute;
  top: 60px;
  left: 0;
  right: 0;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 16px 20px;
  z-index: 9;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  color: #9ca3af;
  z-index: 1;
}

.search-input {
  width: 100%;
  height: 44px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 0 40px 0 40px;
  font-size: 16px;
  outline: none;
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: #3b82f6;
  background: white;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.clear-btn {
  position: absolute;
  right: 8px;
  width: 28px;
  height: 28px;
  background: #e5e7eb;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

/* 搜索建议 */
.search-suggestions {
  margin-top: 12px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.suggestion-item:hover {
  background: #f9fafb;
}

.suggestion-item:not(:last-child) {
  border-bottom: 1px solid #f3f4f6;
}

.suggestion-content {
  flex: 1;
}

.suggestion-name {
  font-weight: 500;
  color: #1f2937;
}

.suggestion-address {
  font-size: 14px;
  color: #6b7280;
}

.suggestion-distance {
  font-size: 12px;
  color: #9ca3af;
}

/* 地图显示区域 */
.map-display {
  flex: 1;
  position: relative;
  margin-top: 60px;
}

.map-canvas {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

/* 地点标记 */
.location-marker {
  position: absolute;
  transform: translate(-50%, -100%);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 5;
}

.location-marker:hover {
  transform: translate(-50%, -100%) scale(1.1);
}

.location-marker.active {
  z-index: 6;
}

.marker-icon {
  width: 40px;
  height: 40px;
  background: white;
  border: 3px solid #3b82f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3b82f6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
}

.location-marker.active .marker-icon {
  border-color: #ef4444;
  color: #ef4444;
  animation: pulse 2s infinite;
}

.location-marker.recommended .marker-icon {
  border-color: #10b981;
  color: #10b981;
}

.marker-icon::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #3b82f6;
}

.location-marker.active .marker-icon::after {
  border-top-color: #ef4444;
}

.location-marker.recommended .marker-icon::after {
  border-top-color: #10b981;
}

.marker-label {
  position: absolute;
  top: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.location-marker:hover .marker-label,
.location-marker.active .marker-label {
  opacity: 1;
}

.recommended-badge {
  position: absolute;
  top: -45px;
  right: -30px;
  background: #10b981;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
}

/* 当前位置标记 */
.current-location-marker {
  position: absolute;
  transform: translate(-50%, -50%);
  z-index: 4;
}

.current-marker-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid #3b82f6;
  border-radius: 50%;
  animation: currentLocationPulse 2s infinite;
}

@keyframes currentLocationPulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

/* 地图控制按钮 */
.map-controls {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-btn {
  width: 44px;
  height: 44px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.control-btn:hover {
  background: #f9fafb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 分类标签 */
.category-tabs {
  position: absolute;
  bottom: 260px;
  left: 0;
  right: 0;
  display: flex;
  gap: 8px;
  padding: 0 20px;
  overflow-x: auto;
  z-index: 8;
}

.category-tab {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e5e7eb;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  backdrop-filter: blur(10px);
}

.category-tab:hover {
  background: rgba(255, 255, 255, 0.95);
}

.category-tab.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

/* 地点信息面板 */
.location-info-panel {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  max-height: 240px;
  transition: max-height 0.3s ease;
  z-index: 9;
  overflow: hidden;
}

.location-info-panel.expanded {
  max-height: 60vh;
}

.location-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 40px 20px;
  color: #9ca3af;
  text-align: center;
}

.location-details {
  padding: 20px;
}

.location-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 16px;
}

.location-main-info {
  flex: 1;
}

.location-name {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.location-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  color: #6b7280;
}

.location-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.location-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 40px;
  height: 40px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #f3f4f6;
}

.action-btn.favorite .filled {
  color: #ef4444;
}

.location-description {
  margin-bottom: 16px;
  color: #4b5563;
  line-height: 1.5;
}

.location-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.location-tag {
  background: #f3f4f6;
  color: #6b7280;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
}

.location-info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.info-content {
  flex: 1;
}

.info-label {
  font-size: 12px;
  color: #9ca3af;
  margin-bottom: 2px;
}

.info-value {
  font-size: 14px;
  color: #1f2937;
  font-weight: 500;
}

.location-actions-panel {
  display: flex;
  gap: 12px;
}

.action-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-button.secondary {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  color: #6b7280;
}

.action-button.secondary:hover {
  background: #f3f4f6;
}

.action-button.primary {
  background: #3b82f6;
  border: 1px solid #3b82f6;
  color: white;
}

.action-button.primary:hover {
  background: #2563eb;
}

/* 弹窗样式 */
.location-modal-overlay,
.directions-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

.location-modal,
.directions-modal {
  background: white;
  border-radius: 16px;
  width: 400px;
  max-width: 90vw;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
}

.modal-content {
  padding: 20px;
}

.location-permission {
  text-align: center;
}

.permission-icon {
  width: 64px;
  height: 64px;
  color: #3b82f6;
  margin-bottom: 16px;
}

.location-permission h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #1f2937;
}

.location-permission p {
  margin: 0 0 24px 0;
  color: #6b7280;
  line-height: 1.5;
}

.permission-actions {
  display: flex;
  gap: 12px;
}

.permission-btn {
  flex: 1;
  height: 44px;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.permission-btn.secondary {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  color: #6b7280;
}

.permission-btn.primary {
  background: #3b82f6;
  border: 1px solid #3b82f6;
  color: white;
}

/* 路线信息 */
.route-summary {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 12px;
}

.route-distance,
.route-time {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.transport-options {
  margin-bottom: 20px;
}

.transport-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  margin-bottom: 8px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.transport-btn:hover {
  background: #f3f4f6;
}

.transport-btn.active {
  background: #eff6ff;
  border-color: #3b82f6;
  color: #3b82f6;
}

.transport-btn span {
  display: flex;
  align-items: center;
  gap: 8px;
}

.transport-time {
  font-size: 14px;
  color: #6b7280;
}

.route-actions {
  display: flex;
  gap: 12px;
}

.route-action-btn {
  flex: 1;
  height: 44px;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.route-action-btn.secondary {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  color: #6b7280;
}

.route-action-btn.primary {
  background: #3b82f6;
  border: 1px solid #3b82f6;
  color: white;
}

/* 状态提示 */
.status-tip-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 15;
}

.status-tip {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  backdrop-filter: blur(10px);
}

/* 动画 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.slide-down-enter-active {
  transition: all 0.3s ease;
}

.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from {
  transform: translateY(-100%);
  opacity: 0;
}

.slide-down-leave-to {
  transform: translateY(-100%);
  opacity: 0;
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .top-navigation {
    padding: 0 16px;
  }
  
  .search-bar {
    padding: 12px 16px;
  }
  
  .category-tabs {
    padding: 0 16px;
  }
  
  .location-details {
    padding: 16px;
  }
  
  .location-info-grid {
    grid-template-columns: 1fr;
  }
  
  .map-controls {
    right: 16px;
  }
  
  .control-btn {
    width: 40px;
    height: 40px;
  }
  
  .location-modal,
  .directions-modal {
    width: 350px;
  }
}
</style>