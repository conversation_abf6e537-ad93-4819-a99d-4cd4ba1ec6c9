<template>
  <div class="live-stream-container">
    <!-- 直播视频区域 -->
    <div class="video-area">
      <div class="video-player">
        <!-- 模拟视频播放器 -->
        <div class="video-placeholder">
          <div class="avatar-container">
            <img
              :src="currentActor?.avatar_url || '/default-avatar.png'"
              :alt="currentActor?.name || 'Character'"
              class="character-avatar"
            />
          </div>
          <div class="live-indicator">
            <span class="live-dot"></span>
            LIVE
          </div>
        </div>

        <!-- 直播覆盖层 -->
        <div class="stream-overlay">
          <!-- 观众数量 -->
          <div class="viewer-count">
            <Icon name="lucide:eye" />
            {{ viewerCount }}
          </div>

          <!-- 心形点击效果 -->
          <div class="heart-area" @click="handleHeartClick">
            <HeartParticles
              v-if="showHeartParticles"
              :count="heartClickCount"
              @animation-end="showHeartParticles = false"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 直播评论流 -->
    <div class="comment-section">
      <div class="comment-flow-container">
        <CommentFlow :comments="liveComments" :auto-scroll="true" />
      </div>

      <!-- 评论输入区 -->
      <div class="comment-input-area">
        <div class="input-group">
          <input
            v-model="commentText"
            type="text"
            placeholder="说点什么..."
            class="comment-input"
            @keyup.enter="sendComment"
            maxlength="100"
          />
          <button
            class="send-button"
            @click="sendComment"
            :disabled="!commentText.trim()"
          >
            <Icon name="lucide:send" />
          </button>
        </div>
      </div>
    </div>

    <!-- 底部控制栏 -->
    <div class="control-bar">
      <div class="action-buttons">
        <!-- 心形按钮 -->
        <button
          class="action-btn heart-btn"
          @click="handleHeartClick"
          :class="{ active: heartClickCount > 0 }"
        >
          <Icon name="lucide:heart" />
          <span v-if="heartClickCount > 0">{{ heartClickCount }}</span>
        </button>

        <!-- 礼物按钮 -->
        <button class="action-btn gift-btn" @click="openGiftModal">
          <Icon name="lucide:gift" />
          礼物
        </button>

        <!-- 分享按钮 -->
        <button class="action-btn share-btn" @click="handleShare">
          <Icon name="lucide:share-2" />
          分享
        </button>

        <!-- 场景切换按钮 -->
        <button class="action-btn scene-btn" @click="handleSceneSwitch">
          <Icon name="lucide:message-circle" />
          私聊
        </button>
      </div>
    </div>

    <!-- 弹幕层 -->
    <DanmakuFlow
      v-if="showDanmaku"
      :messages="danmakuMessages"
      class="danmaku-layer"
    />
  </div>
</template>

<script setup lang="ts">
import type { LiveComment } from '~/types/chat4'

// 显式导入UI组件
import HeartParticles from '~/components/Chat4/ui/HeartParticles.vue'
import CommentFlow from '~/components/Chat4/ui/CommentFlow.vue'
import DanmakuFlow from '~/components/Chat4/ui/DanmakuFlow.vue'

// 使用现代化架构
import { useChat4 } from '~/composables/useChat4'

// Props
interface Props {
  characterId?: string
  storyId?: string
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'scene-change', scene: string): void
}

const emit = defineEmits<Emits>()

// 使用现代化Chat4架构
const { state, sendMessage, toggleModal } = useChat4({
  config: {
    api: {
      baseUrl:
        process.env.NUXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api/v1',
      timeout: 30000,
      retryAttempts: 3,
    },
    features: {
      enableSound: true,
      enableHistory: true,
      enableOffline: false,
    },
  },
})

// 响应式数据
const commentText = ref('')
const showHeartParticles = ref(false)
const showDanmaku = ref(true)
const heartClickCount = ref(0)
const viewerCount = ref(128)

// 模拟直播评论数据
const liveComments = ref<LiveComment[]>([
  {
    id: '1',
    username: '观众1',
    content: '主播好美！',
    timestamp: Date.now(),
  },
  {
    id: '2',
    username: '观众2',
    content: '❤️❤️❤️',
    timestamp: Date.now(),
  },
])

// 弹幕消息（简化版本）
const danmakuMessages = computed(() => liveComments.value.slice(-5))

// 心形点击处理
const handleHeartClick = () => {
  heartClickCount.value++
  showHeartParticles.value = true

  // 添加心形评论
  liveComments.value.push({
    id: Date.now().toString(),
    username: '观众',
    content: '❤️',
    timestamp: Date.now(),
  })

  console.log('💖 心形点击:', heartClickCount.value)
}

// 发送评论
const sendComment = () => {
  const text = commentText.value.trim()
  if (!text) return

  // 添加用户评论
  liveComments.value.push({
    id: Date.now().toString(),
    username: '我',
    content: text,
    timestamp: Date.now(),
  })

  // 清空输入
  commentText.value = ''

  console.log('💬 发送评论:', text)
}

// 打开礼物模态框
const openGiftModal = () => {
  toggleModal('gift')
  console.log('🎁 打开礼物选择')
}

// 分享功能
const handleShare = () => {
  if (navigator.share) {
    navigator
      .share({
        title: '精彩的直播',
        text: '快来看看这个精彩的直播！',
        url: window.location.href,
      })
      .catch((err) => console.log('分享失败:', err))
  } else {
    // 降级到复制链接
    navigator.clipboard.writeText(window.location.href).then(() => {
      console.log('📋 链接已复制到剪贴板')
    })
  }
}

// 场景切换处理
const handleSceneSwitch = () => {
  emit('scene-change', 'Phone')
  console.log('🔄 切换到聊天场景')
}

// 模拟实时评论
const simulateRealTimeComments = () => {
  const mockComments = [
    '主播好美！',
    '这个故事太有趣了',
    '❤️❤️❤️',
    '继续继续！',
    '哇哦！',
    '太精彩了！',
  ]

  const mockUsernames = ['观众A', '观众B', '观众C', '观众D']

  // 每隔3-8秒随机添加一条评论
  const addRandomComment = () => {
    const comment =
      mockComments[Math.floor(Math.random() * mockComments.length)]
    const username =
      mockUsernames[Math.floor(Math.random() * mockUsernames.length)]

    // 添加到评论列表
    liveComments.value.push({
      id: Date.now().toString(),
      username,
      content: comment,
      timestamp: Date.now(),
    })

    // 随机间隔
    const nextDelay = 3000 + Math.random() * 5000
    setTimeout(addRandomComment, nextDelay)
  }

  // 启动模拟评论
  setTimeout(addRandomComment, 2000)
}

// 组件挂载时初始化
onMounted(() => {
  console.log('📺 直播场景初始化')

  // 模拟一些初始评论
  nextTick(() => {
    // 添加入场消息
    liveComments.value.push(
      {
        id: 'enter-1',
        username: '观众A',
        content: '观众A 进入了直播间',
        timestamp: Date.now(),
      },
      {
        id: 'enter-2',
        username: '观众B',
        content: '观众B 进入了直播间',
        timestamp: Date.now() + 1000,
      },
      {
        id: 'comment-1',
        username: '观众A',
        content: '哇，开始了！',
        timestamp: Date.now() + 2000,
      },
    )

    // 开始模拟实时评论
    simulateRealTimeComments()
  })
})

// 组件卸载时清理
onUnmounted(() => {
  console.log('📺 直播场景卸载')
})
</script>

<style scoped>
.live-stream-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--chat4-bg-gradient);
  color: white;
  overflow: hidden;
}

/* 视频区域 */
.video-area {
  flex: 1;
  position: relative;
  min-height: 50%;
}

.video-player {
  width: 100%;
  height: 100%;
  position: relative;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 8px;
  overflow: hidden;
}

.video-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.avatar-container {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid var(--chat4-secondary-color);
  box-shadow: 0 8px 32px rgba(218, 255, 150, 0.3);
  animation: pulse 2s ease-in-out infinite;
}

.character-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.live-indicator {
  position: absolute;
  top: 20px;
  left: 20px;
  background: var(--chat4-accent-color);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.live-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: white;
  animation: blink 1s ease-in-out infinite;
}

@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0.3;
  }
}

/* 直播覆盖层 */
.stream-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.viewer-count {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  backdrop-filter: blur(10px);
}

.heart-area {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 80px;
  height: 80px;
  pointer-events: all;
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
}

.heart-area:hover {
  transform: scale(1.1);
}

.heart-area:active {
  transform: scale(0.95);
}

/* 评论区域 */
.comment-section {
  height: 200px;
  display: flex;
  flex-direction: column;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.comment-flow-container {
  flex: 1;
  overflow: hidden;
  padding: 12px;
}

.comment-input-area {
  padding: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.input-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.comment-input {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 10px 16px;
  color: white;
  font-size: 14px;
  outline: none;
  transition: all 0.3s ease;
}

.comment-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.comment-input:focus {
  border-color: var(--chat4-secondary-color);
  box-shadow: 0 0 0 2px rgba(218, 255, 150, 0.2);
}

.send-button {
  background: var(--chat4-secondary-color);
  color: var(--chat4-primary-color);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.send-button:hover:not(:disabled) {
  background: #b8ff56;
  transform: scale(1.05);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 控制栏 */
.control-bar {
  padding: 16px;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.action-buttons {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.action-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 10px 16px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 60px;
  justify-content: center;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.heart-btn.active {
  background: var(--chat4-accent-color);
  border-color: var(--chat4-accent-color);
}

.gift-btn:hover {
  background: linear-gradient(45deg, #ff6b6b, #feca57);
  border-color: transparent;
}

.scene-btn {
  background: var(--chat4-secondary-color);
  color: var(--chat4-primary-color);
  border-color: var(--chat4-secondary-color);
  font-weight: 600;
}

.scene-btn:hover {
  background: #b8ff56;
}

/* 弹幕层 */
.danmaku-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 200px; /* 避开评论区域 */
  pointer-events: none;
  z-index: 10;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .avatar-container {
    width: 80px;
    height: 80px;
  }

  .comment-section {
    height: 160px;
  }

  .action-buttons {
    gap: 8px;
  }

  .action-btn {
    padding: 8px 12px;
    font-size: 12px;
    min-width: 50px;
  }
}
</style>
