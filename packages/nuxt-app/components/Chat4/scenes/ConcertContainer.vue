<template>
  <div class="concert-container">
    <!-- 增强背景 -->
    <EnhancedBackground
      :video-url="backgroundVideoUrl"
      :image-url="backgroundImageUrl"
      :blur="false"
      transition-mode="zoom"
      :transition-duration="1200"
    />
    
    <!-- 演唱会界面 -->
    <div class="concert-interface">
      <!-- 顶部演出信息栏 -->
      <div class="concert-info-bar">
        <div class="performance-info">
          <div class="artist-info">
            <div class="artist-avatar">
              <img v-if="currentActor?.avatar" :src="currentActor.avatar" :alt="currentActor.name" />
              <div v-else class="avatar-fallback">
                <Icon name="lucide:music" />
              </div>
            </div>
            <div class="artist-details">
              <h2 class="artist-name">{{ currentActor?.name || 'Artist' }}</h2>
              <div class="concert-title">{{ currentConcert.title }}</div>
            </div>
          </div>
          
          <div class="concert-status">
            <div class="live-indicator" :class="{ 'live': isLive }">
              <div class="live-dot"></div>
              <span>{{ isLive ? 'LIVE' : 'OFFLINE' }}</span>
            </div>
            <div class="audience-count">
              <Icon name="lucide:users" />
              <span>{{ formatNumber(audienceCount) }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 主舞台区域 -->
      <div class="main-stage">
        <!-- 舞台灯光效果 -->
        <div class="stage-lights" :class="{ 'active': isPerforming }">
          <div class="spotlight spotlight-1"></div>
          <div class="spotlight spotlight-2"></div>
          <div class="spotlight spotlight-3"></div>
          <div class="light-beam" v-for="i in 6" :key="i" :class="`beam-${i}`"></div>
        </div>
        
        <!-- 表演者区域 -->
        <div class="performer-area">
          <div class="performer" :class="{ 'performing': isPerforming, 'spotlight': showSpotlight }">
            <div class="performer-avatar">
              <img v-if="currentActor?.avatar" :src="currentActor.avatar" :alt="currentActor.name" />
              <div v-else class="avatar-fallback">
                <Icon name="lucide:user" />
              </div>
              
              <!-- 表演特效 -->
              <div v-if="isPerforming" class="performance-effects">
                <div class="energy-aura"></div>
                <div class="rhythm-pulse"></div>
              </div>
            </div>
            
            <!-- 当前歌曲信息 -->
            <div v-if="currentSong && isPerforming" class="current-song">
              <div class="song-title">{{ currentSong.title }}</div>
              <div class="song-progress">
                <div class="progress-bar">
                  <div class="progress-fill" :style="{ width: songProgress + '%' }"></div>
                </div>
                <div class="song-time">
                  {{ formatTime(currentTime) }} / {{ formatTime(currentSong.duration) }}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 舞台装饰 -->
        <div class="stage-decorations">
          <div class="stage-smoke" v-if="showEffects">
            <div class="smoke-layer"></div>
          </div>
          <div class="confetti-area" v-if="showConfetti">
            <!-- 五彩纸屑效果会通过JS动态添加 -->
          </div>
        </div>
      </div>
      
      <!-- 观众互动区 -->
      <div class="audience-interaction">
        <!-- 实时弹幕 -->
        <div class="concert-danmaku" v-if="showDanmaku">
          <DanmakuFlow 
            :messages="concertComments"
            :speed="danmakuSpeed"
            :opacity="0.8"
            container-class="concert-danmaku-container"
          />
        </div>
        
        <!-- 观众反应 -->
        <div class="audience-reactions">
          <div 
            v-for="reaction in activeReactions" 
            :key="reaction.id"
            class="reaction-bubble"
            :style="reaction.style"
          >
            {{ reaction.emoji }}
          </div>
        </div>
        
        <!-- 粒子效果 -->
        <div class="particle-effects">
          <HeartParticles 
            ref="heartParticlesRef"
            :auto-emit="false"
            :particle-count="15"
            color="#ff69b4"
          />
        </div>
      </div>
      
      <!-- 演出控制面板 -->
      <div class="concert-controls">
        <!-- 快捷互动 -->
        <div class="quick-interactions">
          <button 
            v-for="interaction in quickInteractions" 
            :key="interaction.id"
            class="interaction-btn"
            @click="performInteraction(interaction)"
          >
            <Icon :name="interaction.icon" />
            <span>{{ interaction.name }}</span>
          </button>
        </div>
        
        <!-- 主要控制 -->
        <div class="main-controls">
          <button class="control-button secondary" @click="showSonglist">
            <Icon name="lucide:music" />
            <span>歌单</span>
          </button>
          
          <button 
            class="control-button primary"
            :class="{ 'stop': isPerforming }"
            @click="togglePerformance"
          >
            <Icon :name="isPerforming ? 'lucide:square' : 'lucide:play'" />
            <span>{{ isPerforming ? '结束演出' : '开始演出' }}</span>
          </button>
          
          <button class="control-button secondary" @click="showConcertSettings">
            <Icon name="lucide:settings" />
            <span>设置</span>
          </button>
        </div>
      </div>
    </div>
    
    <!-- 歌单选择弹窗 -->
    <Transition name="fade">
      <div v-if="showSonglistModal" class="songlist-modal-overlay" @click="hideSonglist">
        <div class="songlist-modal" @click.stop>
          <div class="modal-header">
            <h3>演出歌单</h3>
            <button class="close-btn" @click="hideSonglist">
              <Icon name="lucide:x" />
            </button>
          </div>
          
          <div class="modal-content">
            <div class="songlist-tabs">
              <button 
                v-for="category in songCategories" 
                :key="category.id"
                class="tab-btn"
                :class="{ active: activeCategory === category.id }"
                @click="switchCategory(category.id)"
              >
                {{ category.name }}
              </button>
            </div>
            
            <div class="songs-list">
              <div 
                v-for="song in filteredSongs" 
                :key="song.id"
                class="song-item"
                :class="{ 
                  selected: selectedSongId === song.id,
                  locked: song.locked 
                }"
                @click="selectSong(song)"
              >
                <div class="song-cover">
                  <img v-if="song.cover" :src="song.cover" :alt="song.title" />
                  <div v-else class="cover-fallback">
                    <Icon name="lucide:music" />
                  </div>
                  <div v-if="song.locked" class="lock-overlay">
                    <Icon name="lucide:lock" />
                  </div>
                </div>
                
                <div class="song-details">
                  <div class="song-title">{{ song.title }}</div>
                  <div class="song-artist">{{ song.artist }}</div>
                  <div class="song-info">
                    <span class="song-duration">{{ formatTime(song.duration) }}</span>
                    <span class="song-mood" :class="song.mood.toLowerCase()">
                      {{ getMoodText(song.mood) }}
                    </span>
                  </div>
                </div>
                
                <div class="song-actions">
                  <button 
                    v-if="!song.locked" 
                    class="preview-btn"
                    @click.stop="previewSong(song)"
                  >
                    <Icon name="lucide:play" />
                  </button>
                  
                  <div class="song-stats">
                    <Icon name="lucide:heart" />
                    <span>{{ formatNumber(song.likes) }}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="modal-actions">
              <button class="modal-btn secondary" @click="hideSonglist">
                取消
              </button>
              <button 
                class="modal-btn primary" 
                :disabled="!selectedSongId || getSongById(selectedSongId)?.locked"
                @click="confirmSongSelection"
              >
                选择歌曲
              </button>
            </div>
          </div>
        </div>
      </div>
    </Transition>
    
    <!-- 演出设置弹窗 -->
    <Transition name="fade">
      <div v-if="showSettingsModal" class="settings-modal-overlay" @click="hideConcertSettings">
        <div class="settings-modal" @click.stop>
          <div class="modal-header">
            <h3>演出设置</h3>
            <button class="close-btn" @click="hideConcertSettings">
              <Icon name="lucide:x" />
            </button>
          </div>
          
          <div class="modal-content">
            <div class="setting-group">
              <h4>舞台效果</h4>
              <div class="effect-toggles">
                <label class="toggle-item">
                  <input type="checkbox" v-model="showEffects" />
                  <span>舞台烟雾</span>
                </label>
                <label class="toggle-item">
                  <input type="checkbox" v-model="showSpotlight" />
                  <span>聚光灯</span>
                </label>
                <label class="toggle-item">
                  <input type="checkbox" v-model="showConfetti" />
                  <span>五彩纸屑</span>
                </label>
                <label class="toggle-item">
                  <input type="checkbox" v-model="showDanmaku" />
                  <span>观众弹幕</span>
                </label>
              </div>
            </div>
            
            <div class="setting-group">
              <h4>音效设置</h4>
              <div class="volume-controls">
                <div class="volume-item">
                  <label>音乐音量</label>
                  <input 
                    type="range" 
                    min="0" 
                    max="100" 
                    v-model="musicVolume"
                    class="volume-slider"
                  />
                  <span>{{ musicVolume }}%</span>
                </div>
                <div class="volume-item">
                  <label>观众音效</label>
                  <input 
                    type="range" 
                    min="0" 
                    max="100" 
                    v-model="audienceVolume"
                    class="volume-slider"
                  />
                  <span>{{ audienceVolume }}%</span>
                </div>
              </div>
            </div>
            
            <div class="setting-group">
              <h4>观众互动</h4>
              <div class="interaction-settings">
                <div class="setting-item">
                  <label>弹幕速度</label>
                  <select v-model="danmakuSpeed" class="setting-select">
                    <option value="slow">慢速</option>
                    <option value="normal">正常</option>
                    <option value="fast">快速</option>
                  </select>
                </div>
                <div class="setting-item">
                  <label>观众反应频率</label>
                  <select v-model="reactionFrequency" class="setting-select">
                    <option value="low">低</option>
                    <option value="medium">中</option>
                    <option value="high">高</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          
          <div class="modal-actions">
            <button class="modal-btn secondary" @click="resetSettings">
              重置
            </button>
            <button class="modal-btn primary" @click="hideConcertSettings">
              确定
            </button>
          </div>
        </div>
      </div>
    </Transition>
    
    <!-- 状态提示 -->
    <Transition name="fade">
      <div v-if="showStatusTip" class="status-tip-overlay">
        <div class="status-tip">
          <Icon :name="statusTipIcon" />
          <span>{{ statusTipText }}</span>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
// 显式导入组件
import EnhancedBackground from '~/components/common/EnhancedBackground.vue'
import HeartParticles from '~/components/Chat4/ui/HeartParticles.vue'
import DanmakuFlow from '~/components/Chat4/ui/DanmakuFlow.vue'
import { useChat4State } from '~/composables/chat4/useChat4State'
import { useChat4Events } from '~/composables/chat4/useChat4Events'

// 歌曲类型
interface Song {
  id: string
  title: string
  artist: string
  duration: number
  cover?: string
  mood: 'ENERGETIC' | 'ROMANTIC' | 'MELANCHOLY' | 'UPBEAT'
  category: string
  locked: boolean
  likes: number
}

// 观众反应类型
interface AudienceReaction {
  id: string
  emoji: string
  style: {
    left: string
    bottom: string
    animationDelay: string
  }
}

// 快捷互动类型
interface QuickInteraction {
  id: string
  name: string
  icon: string
  effect: string
}

// Chat4 状态管理
const { state, sceneNavigation } = useChat4State()
const { currentActor } = storeToRefs(state)

// 事件管理
const { emit: emitEvent, interactionEvents } = useChat4Events()

// 组件引用
const heartParticlesRef = ref()

// 界面状态
const showSonglistModal = ref(false)
const showSettingsModal = ref(false)
const showStatusTip = ref(false)
const statusTipText = ref('')
const statusTipIcon = ref('lucide:info')

// 演出状态
const isPerforming = ref(false)
const isLive = ref(false)
const currentSong = ref<Song | null>(null)
const selectedSongId = ref('')
const activeCategory = ref('popular')

// 进度状态
const currentTime = ref(0)
const songProgress = ref(0)
const audienceCount = ref(12540)

// 视觉效果状态
const showEffects = ref(true)
const showSpotlight = ref(true)
const showConfetti = ref(false)
const showDanmaku = ref(true)

// 设置状态
const musicVolume = ref(85)
const audienceVolume = ref(70)
const danmakuSpeed = ref('normal')
const reactionFrequency = ref('medium')

// 互动状态
const activeReactions = ref<AudienceReaction[]>([])
const concertComments = ref<any[]>([])

// 背景状态
const backgroundVideoUrl = ref('')
const backgroundImageUrl = ref('https://picsum.photos/1920/1080?random=600')

// 定时器
let performanceTimer: ReturnType<typeof setInterval> | null = null
let reactionTimer: ReturnType<typeof setInterval> | null = null
let commentTimer: ReturnType<typeof setInterval> | null = null

// 演出数据
const currentConcert = {
  title: '浪漫夜晚演唱会',
  venue: '星光剧院',
  date: '今晚 20:00'
}

// 歌曲分类
const songCategories = [
  { id: 'popular', name: '热门' },
  { id: 'romantic', name: '浪漫' },
  { id: 'energetic', name: '激情' },
  { id: 'classic', name: '经典' }
]

// 可用歌曲
const availableSongs: Song[] = [
  {
    id: 'song-1',
    title: 'Starlight Serenade',
    artist: 'Digital Hearts',
    duration: 245,
    mood: 'ROMANTIC',
    category: 'romantic',
    locked: false,
    likes: 15420
  },
  {
    id: 'song-2',
    title: 'Electric Nights',
    artist: 'Neon Dreams',
    duration: 198,
    mood: 'ENERGETIC',
    category: 'popular',
    locked: false,
    likes: 28350
  },
  {
    id: 'song-3',
    title: 'Moonlight Dance',
    artist: 'Velvet Voice',
    duration: 267,
    mood: 'ROMANTIC',
    category: 'romantic',
    locked: false,
    likes: 19870
  },
  {
    id: 'song-4',
    title: 'Thunder Beat',
    artist: 'Storm Riders',
    duration: 223,
    mood: 'ENERGETIC',
    category: 'energetic',
    locked: false,
    likes: 34560
  },
  {
    id: 'song-5',
    title: 'Eternal Love',
    artist: 'Angel Voice',
    duration: 301,
    mood: 'MELANCHOLY',
    category: 'classic',
    locked: true,
    likes: 45670
  }
]

// 快捷互动
const quickInteractions: QuickInteraction[] = [
  { id: 'cheer', name: '欢呼', icon: 'lucide:volume-2', effect: 'cheer' },
  { id: 'heart', name: '爱心', icon: 'lucide:heart', effect: 'hearts' },
  { id: 'applause', name: '鼓掌', icon: 'lucide:hand', effect: 'applause' },
  { id: 'flower', name: '鲜花', icon: 'lucide:flower', effect: 'flowers' }
]

// 计算属性
const filteredSongs = computed(() => 
  availableSongs.filter(song => song.category === activeCategory.value)
)

// 工具方法
const getSongById = (id: string) => availableSongs.find(song => song.id === id)

const formatNumber = (num: number) => {
  if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M'
  if (num >= 1000) return (num / 1000).toFixed(1) + 'K'
  return num.toString()
}

const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const getMoodText = (mood: string) => {
  switch (mood) {
    case 'ENERGETIC': return '激情'
    case 'ROMANTIC': return '浪漫'
    case 'MELANCHOLY': return '忧伤'
    case 'UPBEAT': return '欢快'
    default: return '未知'
  }
}

// 演出控制
const togglePerformance = () => {
  if (isPerforming.value) {
    stopPerformance()
  } else {
    startPerformance()
  }
}

const startPerformance = async () => {
  if (!currentSong.value) {
    // 如果没有选择歌曲，使用默认歌曲
    currentSong.value = availableSongs.find(song => !song.locked) || availableSongs[0]
  }
  
  isPerforming.value = true
  isLive.value = true
  currentTime.value = 0
  songProgress.value = 0
  
  showStatusTip('演出开始!')
  setTimeout(hideStatusTip, 2000)
  
  // 启动性能计时器
  startPerformanceTimer()
  
  // 启动观众互动
  startAudienceInteraction()
  
  // 触发特效
  triggerConfetti()
  
  // 触发事件
  interactionEvents.emitSceneEvent('concert_start', {
    song: currentSong.value,
    audience: audienceCount.value
  })
}

const stopPerformance = () => {
  isPerforming.value = false
  isLive.value = false
  
  // 停止所有定时器
  stopPerformanceTimer()
  stopAudienceInteraction()
  
  showStatusTip(`演出结束! 观众: ${formatNumber(audienceCount.value)}`)
  setTimeout(hideStatusTip, 3000)
  
  // 触发事件
  interactionEvents.emitSceneEvent('concert_end', {
    duration: currentTime.value,
    audience: audienceCount.value
  })
}

const startPerformanceTimer = () => {
  if (!currentSong.value) return
  
  performanceTimer = setInterval(() => {
    if (currentSong.value) {
      currentTime.value++
      songProgress.value = (currentTime.value / currentSong.value.duration) * 100
      
      // 歌曲结束
      if (currentTime.value >= currentSong.value.duration) {
        stopPerformance()
      }
    }
  }, 1000)
}

const stopPerformanceTimer = () => {
  if (performanceTimer) {
    clearInterval(performanceTimer)
    performanceTimer = null
  }
}

// 观众互动系统
const startAudienceInteraction = () => {
  // 观众反应
  const reactionIntervals = {
    low: 3000,
    medium: 2000,
    high: 1000
  }
  
  const interval = reactionIntervals[reactionFrequency.value as keyof typeof reactionIntervals]
  
  reactionTimer = setInterval(() => {
    generateAudienceReaction()
  }, interval)
  
  // 弹幕评论
  commentTimer = setInterval(() => {
    generateConcertComment()
  }, 1500)
}

const stopAudienceInteraction = () => {
  if (reactionTimer) {
    clearInterval(reactionTimer)
    reactionTimer = null
  }
  
  if (commentTimer) {
    clearInterval(commentTimer)
    commentTimer = null
  }
  
  // 清理反应
  activeReactions.value = []
  concertComments.value = []
}

const generateAudienceReaction = () => {
  const emojis = ['👏', '🔥', '❤️', '🌟', '🎉', '💖', '🎶', '✨']
  const emoji = emojis[Math.floor(Math.random() * emojis.length)]
  
  const reaction: AudienceReaction = {
    id: Date.now().toString(),
    emoji,
    style: {
      left: Math.random() * 80 + 10 + '%',
      bottom: '0px',
      animationDelay: '0s'
    }
  }
  
  activeReactions.value.push(reaction)
  
  // 3秒后移除
  setTimeout(() => {
    const index = activeReactions.value.findIndex(r => r.id === reaction.id)
    if (index > -1) {
      activeReactions.value.splice(index, 1)
    }
  }, 3000)
}

const generateConcertComment = () => {
  const comments = [
    '太棒了！',
    '声音好美！',
    '我爱这首歌！',
    '简直是天籁之音',
    '感动到哭了',
    '再来一首！',
    '完美的演出',
    '太有才了！'
  ]
  
  const comment = {
    id: Date.now(),
    text: comments[Math.floor(Math.random() * comments.length)],
    user: `观众${Math.floor(Math.random() * 9999)}`,
    timestamp: Date.now()
  }
  
  concertComments.value.push(comment)
  
  // 保持最多50条评论
  if (concertComments.value.length > 50) {
    concertComments.value = concertComments.value.slice(-50)
  }
}

// 互动操作
const performInteraction = async (interaction: QuickInteraction) => {
  switch (interaction.effect) {
    case 'cheer':
      showStatusTip('观众欢呼声雷动!')
      audienceCount.value += Math.floor(Math.random() * 50) + 10
      break
      
    case 'hearts':
      if (heartParticlesRef.value) {
        heartParticlesRef.value.emitParticles()
      }
      showStatusTip('爱心满屏!')
      break
      
    case 'applause':
      showStatusTip('掌声如潮!')
      audienceCount.value += Math.floor(Math.random() * 30) + 5
      break
      
    case 'flowers':
      triggerFlowerEffect()
      showStatusTip('鲜花飞舞!')
      break
  }
  
  setTimeout(hideStatusTip, 2000)
  
  // 触发事件
  interactionEvents.emitCharacterInteraction({
    type: interaction.id,
    effect: interaction.effect,
    response: `感谢你的${interaction.name}!`
  })
}

// 特效方法
const triggerConfetti = async () => {
  showConfetti.value = true
  
  // 动态导入confetti
  if (process.client) {
    try {
      const { default: confetti } = await import('canvas-confetti')
      
      // 发射五彩纸屑
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      })
      
      setTimeout(() => {
        confetti({
          particleCount: 50,
          angle: 60,
          spread: 55,
          origin: { x: 0 }
        })
      }, 250)
      
      setTimeout(() => {
        confetti({
          particleCount: 50,
          angle: 120,
          spread: 55,
          origin: { x: 1 }
        })
      }, 400)
      
    } catch (error) {
      console.warn('Confetti not available:', error)
    }
  }
  
  setTimeout(() => {
    showConfetti.value = false
  }, 3000)
}

const triggerFlowerEffect = () => {
  // 简单的花朵效果模拟
  for (let i = 0; i < 5; i++) {
    setTimeout(() => {
      generateAudienceReaction()
    }, i * 200)
  }
}

// 歌单控制
const showSonglist = () => {
  showSonglistModal.value = true
}

const hideSonglist = () => {
  showSonglistModal.value = false
}

const switchCategory = (categoryId: string) => {
  activeCategory.value = categoryId
}

const selectSong = (song: Song) => {
  if (song.locked) {
    showStatusTip('此歌曲尚未解锁')
    setTimeout(hideStatusTip, 2000)
    return
  }
  
  selectedSongId.value = song.id
}

const previewSong = (song: Song) => {
  showStatusTip(`预览: ${song.title}`)
  setTimeout(hideStatusTip, 2000)
}

const confirmSongSelection = () => {
  const song = getSongById(selectedSongId.value)
  if (song) {
    currentSong.value = song
    showStatusTip(`已选择: ${song.title}`)
    setTimeout(hideStatusTip, 2000)
  }
  hideSonglist()
}

// 设置控制
const showConcertSettings = () => {
  showSettingsModal.value = true
}

const hideConcertSettings = () => {
  showSettingsModal.value = false
}

const resetSettings = () => {
  showEffects.value = true
  showSpotlight.value = true
  showConfetti.value = false
  showDanmaku.value = true
  musicVolume.value = 85
  audienceVolume.value = 70
  danmakuSpeed.value = 'normal'
  reactionFrequency.value = 'medium'
  
  showStatusTip('设置已重置')
  setTimeout(hideStatusTip, 1500)
}

// 导航控制
const goBack = () => {
  if (isPerforming.value) {
    stopPerformance()
  }
  sceneNavigation.goToDancing()
}

// 工具方法
const showStatusTip = (text: string, icon: string = 'lucide:info') => {
  statusTipText.value = text
  statusTipIcon.value = icon
  showStatusTip.value = true
}

const hideStatusTip = () => {
  showStatusTip.value = false
}

// 生命周期
onMounted(() => {
  // 设置初始歌曲
  const defaultSong = availableSongs.find(song => !song.locked)
  if (defaultSong) {
    currentSong.value = defaultSong
    selectedSongId.value = defaultSong.id
  }
  
  // 模拟观众增长
  const audienceGrowthTimer = setInterval(() => {
    if (isLive.value) {
      audienceCount.value += Math.floor(Math.random() * 10) + 1
    }
  }, 5000)
  
  // 清理定时器
  onBeforeUnmount(() => {
    clearInterval(audienceGrowthTimer)
  })
})

onBeforeUnmount(() => {
  stopPerformanceTimer()
  stopAudienceInteraction()
})
</script>

<style scoped>
.concert-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  overflow: hidden;
}

/* 演唱会界面 */
.concert-interface {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  z-index: 2;
}

/* 演出信息栏 */
.concert-info-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, transparent 100%);
  padding: 20px;
  z-index: 10;
}

.performance-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
}

.artist-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.artist-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.artist-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-fallback {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: rgba(255, 255, 255, 0.6);
}

.artist-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.artist-name {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.concert-title {
  font-size: 16px;
  opacity: 0.8;
}

.concert-status {
  display: flex;
  align-items: center;
  gap: 20px;
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.5);
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.live-indicator.live {
  background: rgba(239, 68, 68, 0.3);
  border-color: rgba(239, 68, 68, 0.8);
  animation: livePulse 2s infinite;
}

.live-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ef4444;
}

.live-indicator.live .live-dot {
  animation: blink 1s infinite;
}

.audience-count {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 500;
}

@keyframes livePulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* 主舞台区域 */
.main-stage {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100px 20px 160px;
}

/* 舞台灯光 */
.stage-lights {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.stage-lights.active {
  opacity: 1;
}

.spotlight {
  position: absolute;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  animation: spotlightMove 8s infinite;
}

.spotlight-1 {
  top: 10%;
  left: 20%;
  animation-delay: 0s;
}

.spotlight-2 {
  top: 15%;
  right: 20%;
  animation-delay: 2s;
}

.spotlight-3 {
  top: 20%;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: 4s;
}

.light-beam {
  position: absolute;
  top: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.4) 0%, transparent 100%);
  animation: beamSweep 6s infinite;
}

.beam-1 { left: 10%; animation-delay: 0s; }
.beam-2 { left: 25%; animation-delay: 1s; }
.beam-3 { left: 40%; animation-delay: 2s; }
.beam-4 { right: 40%; animation-delay: 3s; }
.beam-5 { right: 25%; animation-delay: 4s; }
.beam-6 { right: 10%; animation-delay: 5s; }

@keyframes spotlightMove {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(20px, 30px) scale(1.1); }
  66% { transform: translate(-15px, 20px) scale(0.9); }
}

@keyframes beamSweep {
  0%, 100% { opacity: 0.2; }
  50% { opacity: 0.6; }
}

/* 表演者区域 */
.performer-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.performer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
  position: relative;
}

.performer.performing {
  animation: performanceMove 3s ease-in-out infinite;
}

.performer.spotlight {
  filter: drop-shadow(0 0 30px rgba(255, 255, 255, 0.5));
}

@keyframes performanceMove {
  0%, 100% { transform: translateY(0) rotate(0deg) scale(1); }
  25% { transform: translateY(-5px) rotate(1deg) scale(1.02); }
  50% { transform: translateY(-10px) rotate(0deg) scale(1.05); }
  75% { transform: translateY(-5px) rotate(-1deg) scale(1.02); }
}

.performer-avatar {
  position: relative;
  width: 160px;
  height: 160px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid rgba(255, 215, 0, 0.3);
  box-shadow: 0 0 30px rgba(255, 215, 0, 0.2);
}

.performer-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.performer-avatar .avatar-fallback {
  font-size: 64px;
}

/* 表演特效 */
.performance-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.energy-aura {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 180px;
  height: 180px;
  border: 3px solid rgba(255, 215, 0, 0.4);
  border-radius: 50%;
  animation: energyPulse 2s ease-in-out infinite;
}

.rhythm-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 50%;
  animation: rhythmPulse 1.5s ease-out infinite;
}

@keyframes energyPulse {
  0%, 100% { 
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.4;
  }
  50% { 
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.8;
  }
}

@keyframes rhythmPulse {
  0% { 
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.6;
  }
  100% { 
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

/* 当前歌曲信息 */
.current-song {
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 16px 24px;
  color: white;
  text-align: center;
  min-width: 200px;
}

.song-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.song-progress {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ffd700, #ffed4e);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.song-time {
  font-size: 12px;
  opacity: 0.8;
}

/* 舞台装饰 */
.stage-decorations {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.stage-smoke {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100px;
  overflow: hidden;
}

.smoke-layer {
  width: 120%;
  height: 100%;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
  animation: smokeMove 8s infinite;
}

@keyframes smokeMove {
  0% { transform: translateX(-10%); }
  50% { transform: translateX(10%); }
  100% { transform: translateX(-10%); }
}

/* 观众互动区 */
.audience-interaction {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 5;
}

.concert-danmaku {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.audience-reactions {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 200px;
}

.reaction-bubble {
  position: absolute;
  font-size: 24px;
  animation: reactionFloat 3s ease-out forwards;
}

@keyframes reactionFloat {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(-150px);
    opacity: 0;
  }
}

/* 演出控制面板 */
.concert-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.9) 0%, transparent 100%);
  padding: 30px 20px;
  z-index: 10;
}

.quick-interactions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 20px;
}

.interaction-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 12px 16px;
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 70px;
}

.interaction-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
}

.main-controls {
  display: flex;
  gap: 12px;
}

.control-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.control-button.secondary {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.control-button.secondary:hover {
  background: rgba(255, 255, 255, 0.2);
}

.control-button.primary {
  background: #10b981;
  border: 1px solid #10b981;
  color: white;
}

.control-button.primary:hover {
  background: #059669;
}

.control-button.primary.stop {
  background: #ef4444;
  border-color: #ef4444;
}

.control-button.primary.stop:hover {
  background: #dc2626;
}

/* 弹窗样式 */
.songlist-modal-overlay,
.settings-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

.songlist-modal,
.settings-modal {
  background: white;
  border-radius: 16px;
  width: 500px;
  max-width: 90vw;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
}

.modal-content {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

/* 歌单标签 */
.songlist-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 16px;
}

.tab-btn {
  padding: 8px 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.tab-btn:hover {
  background: #f3f4f6;
}

.tab-btn.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

/* 歌曲列表 */
.songs-list {
  margin-bottom: 20px;
}

.song-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 10px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.song-item:hover:not(.locked) {
  background: #f9fafb;
}

.song-item.selected {
  background: #eff6ff;
  border-color: #3b82f6;
}

.song-item.locked {
  opacity: 0.5;
  cursor: not-allowed;
}

.song-cover {
  position: relative;
  width: 50px;
  height: 50px;
  border-radius: 8px;
  overflow: hidden;
}

.song-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-fallback {
  width: 100%;
  height: 100%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
}

.lock-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.song-details {
  flex: 1;
}

.song-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.song-artist {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 6px;
}

.song-info {
  display: flex;
  gap: 12px;
  font-size: 12px;
}

.song-duration {
  color: #9ca3af;
}

.song-mood {
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.song-mood.energetic {
  background: #fee2e2;
  color: #dc2626;
}

.song-mood.romantic {
  background: #fce7f3;
  color: #be185d;
}

.song-mood.melancholy {
  background: #ede9fe;
  color: #7c3aed;
}

.song-mood.upbeat {
  background: #fef3c7;
  color: #d97706;
}

.song-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.preview-btn {
  width: 32px;
  height: 32px;
  background: #f3f4f6;
  border: none;
  border-radius: 50%;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.preview-btn:hover {
  background: #e5e7eb;
}

.song-stats {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #ef4444;
}

/* 设置内容 */
.setting-group {
  margin-bottom: 24px;
}

.setting-group h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #374151;
  font-weight: 600;
}

.effect-toggles {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.toggle-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.volume-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.volume-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.volume-item label {
  min-width: 80px;
  font-size: 14px;
  color: #4b5563;
}

.volume-slider {
  flex: 1;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  outline: none;
  cursor: pointer;
}

.volume-slider::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  background: #3b82f6;
  border-radius: 50%;
  cursor: pointer;
}

.interaction-settings {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.setting-item label {
  font-size: 14px;
  color: #4b5563;
}

.setting-select {
  padding: 6px 10px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: white;
  cursor: pointer;
}

.modal-actions {
  display: flex;
  gap: 12px;
}

.modal-btn {
  flex: 1;
  height: 44px;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modal-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.modal-btn.secondary {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  color: #6b7280;
}

.modal-btn.primary {
  background: #3b82f6;
  border: 1px solid #3b82f6;
  color: white;
}

/* 状态提示 */
.status-tip-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 15;
}

.status-tip {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  backdrop-filter: blur(10px);
}

/* 动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .concert-info-bar {
    padding: 16px;
  }
  
  .performance-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .artist-info {
    gap: 12px;
  }
  
  .artist-avatar {
    width: 50px;
    height: 50px;
  }
  
  .artist-name {
    font-size: 20px;
  }
  
  .main-stage {
    padding: 80px 16px 140px;
  }
  
  .performer-avatar {
    width: 120px;
    height: 120px;
  }
  
  .current-song {
    padding: 12px 16px;
    min-width: 150px;
  }
  
  .song-title {
    font-size: 16px;
  }
  
  .quick-interactions {
    gap: 8px;
    margin-bottom: 16px;
  }
  
  .interaction-btn {
    padding: 8px 12px;
    min-width: 60px;
    font-size: 10px;
  }
  
  .concert-controls {
    padding: 20px 16px;
  }
  
  .songlist-modal,
  .settings-modal {
    width: 350px;
  }
  
  .songlist-tabs {
    flex-wrap: wrap;
  }
}
</style>