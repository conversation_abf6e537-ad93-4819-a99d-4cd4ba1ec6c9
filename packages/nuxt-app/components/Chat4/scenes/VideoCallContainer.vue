<template>
  <div class="video-call-container">
    <!-- 增强背景 -->
    <EnhancedBackground
      :video-url="backgroundVideoUrl"
      :image-url="backgroundImageUrl"
      :blur="isCallActive"
      transition-mode="fade"
      :transition-duration="600"
    />
    
    <!-- 视频通话主界面 -->
    <div class="call-interface">
      <!-- 通话状态显示 -->
      <div class="call-status-bar">
        <div class="status-indicator" :class="callStatusClass">
          <div class="status-dot"></div>
          <span class="status-text">{{ callStatusText }}</span>
        </div>
        <div class="call-timer" v-if="isCallActive">
          {{ formattedCallDuration }}
        </div>
      </div>
      
      <!-- 视频窗口区域 -->
      <div class="video-windows">
        <!-- 对方视频窗口（主窗口） -->
        <div class="remote-video-window" :class="{ 'fullscreen': !showLocalVideo }">
          <div class="video-placeholder">
            <div class="character-avatar">
              <img v-if="currentActor?.avatar" :src="currentActor.avatar" :alt="currentActor.name" />
              <div v-else class="avatar-fallback">
                <Icon name="lucide:user" />
              </div>
            </div>
            <div class="character-name">{{ currentActor?.name || 'Unknown' }}</div>
            
            <!-- 模拟视频效果 -->
            <div class="video-overlay" v-if="isCallActive">
              <div class="video-effects">
                <div class="breathing-effect"></div>
              </div>
            </div>
          </div>
          
          <!-- 视频控制浮层 -->
          <div class="video-controls-overlay" v-if="showVideoControls">
            <button class="control-btn" @click="toggleFullscreen">
              <Icon :name="isFullscreen ? 'lucide:minimize' : 'lucide:maximize'" />
            </button>
            <button class="control-btn" @click="takeScreenshot">
              <Icon name="lucide:camera" />
            </button>
          </div>
        </div>
        
        <!-- 本地视频窗口（小窗口） -->
        <div 
          class="local-video-window" 
          v-if="showLocalVideo"
          :class="{ 'dragging': isDragging }"
          @mousedown="startDrag"
          @touchstart="startDrag"
        >
          <div class="video-placeholder local">
            <div class="local-avatar">
              <Icon name="lucide:user" />
            </div>
            <div class="local-label">You</div>
            
            <!-- 本地视频状态 -->
            <div class="local-status">
              <Icon v-if="!isCameraOn" name="lucide:video-off" class="camera-off" />
              <Icon v-if="isMuted" name="lucide:mic-off" class="mic-off" />
            </div>
          </div>
        </div>
      </div>
      
      <!-- 通话控制栏 -->
      <div class="call-controls" :class="{ 'incoming': callState === 'incoming' }">
        <!-- 来电状态控制 -->
        <div v-if="callState === 'incoming'" class="incoming-controls">
          <button class="control-button decline" @click="declineCall">
            <Icon name="lucide:phone-off" />
            <span>拒绝</span>
          </button>
          <button class="control-button accept" @click="acceptCall">
            <Icon name="lucide:phone" />
            <span>接听</span>
          </button>
        </div>
        
        <!-- 通话中控制 -->
        <div v-else-if="callState === 'active'" class="active-controls">
          <button 
            class="control-button" 
            :class="{ active: isMuted }" 
            @click="toggleMute"
          >
            <Icon :name="isMuted ? 'lucide:mic-off' : 'lucide:mic'" />
            <span>{{ isMuted ? '取消静音' : '静音' }}</span>
          </button>
          
          <button 
            class="control-button" 
            :class="{ active: !isCameraOn }" 
            @click="toggleCamera"
          >
            <Icon :name="isCameraOn ? 'lucide:video' : 'lucide:video-off'" />
            <span>{{ isCameraOn ? '关闭摄像头' : '开启摄像头' }}</span>
          </button>
          
          <button class="control-button" @click="switchCamera" v-if="showCameraSwitcher">
            <Icon name="lucide:camera-rotate" />
            <span>翻转</span>
          </button>
          
          <button class="control-button end-call" @click="endCall">
            <Icon name="lucide:phone-off" />
            <span>挂断</span>
          </button>
        </div>
        
        <!-- 通话结束状态 -->
        <div v-else-if="callState === 'ended'" class="ended-controls">
          <button class="control-button callback" @click="startCall">
            <Icon name="lucide:phone" />
            <span>回拨</span>
          </button>
          <button class="control-button" @click="goToChat">
            <Icon name="lucide:message-circle" />
            <span>发消息</span>
          </button>
        </div>
      </div>
    </div>
    
    <!-- 互动元素 -->
    <div class="interaction-elements" v-if="isCallActive">
      <!-- 心形点击区域 -->
      <div class="heart-click-area" @click="sendHeart">
        <HeartParticles 
          ref="heartParticlesRef"
          :auto-emit="false"
          :particle-count="8"
          color="#ff69b4"
        />
      </div>
      
      <!-- 快捷表情 -->
      <div class="quick-emotions" v-if="showQuickEmotions">
        <button 
          v-for="emotion in quickEmotions" 
          :key="emotion.id"
          class="emotion-btn"
          @click="sendEmotion(emotion)"
        >
          {{ emotion.emoji }}
        </button>
      </div>
    </div>
    
    <!-- 通话提示浮层 -->
    <Transition name="fade">
      <div v-if="showCallTip" class="call-tip-overlay">
        <div class="call-tip">
          <Icon name="lucide:info" />
          <span>{{ callTipText }}</span>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
// 显式导入组件
import EnhancedBackground from '~/components/common/EnhancedBackground.vue'
import HeartParticles from '~/components/Chat4/ui/HeartParticles.vue'
import { useChat4State } from '~/composables/chat4/useChat4State'
import { useChat4Events } from '~/composables/chat4/useChat4Events'

// 通话状态类型
type CallState = 'idle' | 'incoming' | 'connecting' | 'active' | 'ended'

// 快捷表情类型
interface QuickEmotion {
  id: string
  emoji: string
  name: string
}

// Chat4 状态管理
const { state, sceneNavigation } = useChat4State()
const { currentActor } = storeToRefs(state)

// 事件管理
const { emit: emitEvent, interactionEvents } = useChat4Events()

// 组件引用
const heartParticlesRef = ref()

// 通话状态
const callState = ref<CallState>('idle')
const callStartTime = ref<number>(0)
const callDuration = ref<number>(0)
const isCallActive = computed(() => callState.value === 'active')

// 控制状态
const isMuted = ref(false)
const isCameraOn = ref(true)
const showLocalVideo = ref(true)
const showVideoControls = ref(false)
const isFullscreen = ref(false)
const showQuickEmotions = ref(false)
const showCameraSwitcher = ref(true)

// 拖拽状态
const isDragging = ref(false)
const dragStartPos = ref({ x: 0, y: 0 })

// 背景状态
const backgroundVideoUrl = ref('')
const backgroundImageUrl = ref('https://picsum.photos/1920/1080?random=100')

// 提示状态
const showCallTip = ref(false)
const callTipText = ref('')

// 计时器
let callTimer: ReturnType<typeof setInterval> | null = null

// 计算属性
const callStatusClass = computed(() => {
  return {
    'incoming': callState.value === 'incoming',
    'connecting': callState.value === 'connecting', 
    'active': callState.value === 'active',
    'ended': callState.value === 'ended'
  }
})

const callStatusText = computed(() => {
  switch (callState.value) {
    case 'incoming': return '来电中...'
    case 'connecting': return '连接中...'
    case 'active': return '通话中'
    case 'ended': return '通话结束'
    default: return '等待中'
  }
})

const formattedCallDuration = computed(() => {
  const minutes = Math.floor(callDuration.value / 60)
  const seconds = callDuration.value % 60
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
})

// 快捷表情数据
const quickEmotions: QuickEmotion[] = [
  { id: 'heart', emoji: '❤️', name: 'Heart' },
  { id: 'kiss', emoji: '😘', name: 'Kiss' },
  { id: 'smile', emoji: '😊', name: 'Smile' },
  { id: 'wink', emoji: '😉', name: 'Wink' },
  { id: 'love', emoji: '🥰', name: 'Love' }
]

// 通话控制方法
const startCall = async () => {
  callState.value = 'connecting'
  showCallTip('正在连接...')
  
  // 模拟连接延迟
  setTimeout(() => {
    callState.value = 'active'
    startCallTimer()
    hideCallTip()
  }, 2000)
}

const acceptCall = async () => {
  callState.value = 'connecting'
  showCallTip('正在接听...')
  
  setTimeout(() => {
    callState.value = 'active'
    startCallTimer()
    hideCallTip()
  }, 1500)
}

const declineCall = () => {
  callState.value = 'ended'
  showCallTip('已拒绝通话')
  setTimeout(hideCallTip, 2000)
}

const endCall = () => {
  callState.value = 'ended'
  stopCallTimer()
  showCallTip('通话已结束')
  setTimeout(hideCallTip, 2000)
}

const toggleMute = () => {
  isMuted.value = !isMuted.value
  showCallTip(isMuted.value ? '已静音' : '已取消静音')
  setTimeout(hideCallTip, 1500)
}

const toggleCamera = () => {
  isCameraOn.value = !isCameraOn.value
  showCallTip(isCameraOn.value ? '摄像头已开启' : '摄像头已关闭')
  setTimeout(hideCallTip, 1500)
}

const switchCamera = () => {
  showCallTip('摄像头已切换')
  setTimeout(hideCallTip, 1500)
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  showLocalVideo.value = !isFullscreen.value
}

const takeScreenshot = () => {
  showCallTip('截图已保存')
  setTimeout(hideCallTip, 2000)
}

// 互动方法
const sendHeart = () => {
  if (heartParticlesRef.value) {
    heartParticlesRef.value.emitParticles()
  }
  interactionEvents.emitHeartClick(1)
}

const sendEmotion = (emotion: QuickEmotion) => {
  showCallTip(`发送了 ${emotion.emoji}`)
  setTimeout(hideCallTip, 1500)
}

// 场景导航
const goToChat = () => {
  sceneNavigation.goToChat()
}

// 工具方法
const startCallTimer = () => {
  callStartTime.value = Date.now()
  callDuration.value = 0
  
  callTimer = setInterval(() => {
    callDuration.value = Math.floor((Date.now() - callStartTime.value) / 1000)
  }, 1000)
}

const stopCallTimer = () => {
  if (callTimer) {
    clearInterval(callTimer)
    callTimer = null
  }
}

const showCallTip = (text: string) => {
  callTipText.value = text
  showCallTip.value = true
}

const hideCallTip = () => {
  showCallTip.value = false
}

// 拖拽功能
const startDrag = (event: MouseEvent | TouchEvent) => {
  isDragging.value = true
  const clientX = 'touches' in event ? event.touches[0].clientX : event.clientX
  const clientY = 'touches' in event ? event.touches[0].clientY : event.clientY
  
  dragStartPos.value = { x: clientX, y: clientY }
  
  const handleMove = (e: MouseEvent | TouchEvent) => {
    // 拖拽逻辑（简化实现）
  }
  
  const handleEnd = () => {
    isDragging.value = false
    document.removeEventListener('mousemove', handleMove)
    document.removeEventListener('mouseup', handleEnd)
    document.removeEventListener('touchmove', handleMove)
    document.removeEventListener('touchend', handleEnd)
  }
  
  document.addEventListener('mousemove', handleMove)
  document.addEventListener('mouseup', handleEnd)
  document.addEventListener('touchmove', handleMove)
  document.addEventListener('touchend', handleEnd)
}

// 生命周期
onMounted(() => {
  // 模拟来电
  setTimeout(() => {
    if (callState.value === 'idle') {
      callState.value = 'incoming'
    }
  }, 2000)
  
  // 设置视频控制显示
  setTimeout(() => {
    showVideoControls.value = true
  }, 3000)
})

onBeforeUnmount(() => {
  stopCallTimer()
})
</script>

<style scoped>
.video-call-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background: #000;
  overflow: hidden;
  user-select: none;
}

/* 通话界面 */
.call-interface {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  z-index: 2;
}

/* 状态栏 */
.call-status-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, transparent 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  z-index: 10;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-size: 14px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10b981;
  animation: pulse 2s infinite;
}

.status-indicator.incoming .status-dot {
  background: #f59e0b;
}

.status-indicator.ended .status-dot {
  background: #ef4444;
  animation: none;
}

.call-timer {
  color: white;
  font-size: 16px;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

/* 视频窗口区域 */
.video-windows {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
}

.remote-video-window {
  flex: 1;
  position: relative;
  background: #1a1a1a;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.remote-video-window.fullscreen {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 5;
}

.video-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  color: white;
}

.character-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.character-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-fallback {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: rgba(255, 255, 255, 0.6);
}

.character-name {
  font-size: 18px;
  font-weight: 500;
  color: white;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.breathing-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 200px;
  height: 200px;
  margin: -100px 0 0 -100px;
  border: 2px solid rgba(16, 185, 129, 0.3);
  border-radius: 50%;
  animation: breathe 3s ease-in-out infinite;
}

@keyframes breathe {
  0%, 100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.6;
  }
}

/* 本地视频窗口 */
.local-video-window {
  position: absolute;
  bottom: 120px;
  right: 20px;
  width: 120px;
  height: 160px;
  background: #2d2d2d;
  border-radius: 12px;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.2);
  cursor: move;
  z-index: 8;
  transition: all 0.3s ease;
}

.local-video-window:hover {
  border-color: rgba(255, 255, 255, 0.4);
}

.local-video-window.dragging {
  transform: scale(1.05);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.video-placeholder.local {
  height: 100%;
  font-size: 12px;
}

.local-avatar {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: rgba(255, 255, 255, 0.6);
}

.local-status {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
}

.camera-off, .mic-off {
  width: 16px;
  height: 16px;
  color: #ef4444;
}

/* 视频控制浮层 */
.video-controls-overlay {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.remote-video-window:hover .video-controls-overlay {
  opacity: 1;
}

.control-btn {
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.6);
  border: none;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.05);
}

/* 通话控制栏 */
.call-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.9) 0%, transparent 100%);
  padding: 30px 20px;
  z-index: 10;
}

.incoming-controls, .active-controls, .ended-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
}

.control-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 16px 20px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
  min-width: 80px;
}

.control-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
}

.control-button.active {
  background: rgba(59, 130, 246, 0.3);
  border-color: rgba(59, 130, 246, 0.6);
}

.control-button.accept {
  background: rgba(16, 185, 129, 0.3);
  border-color: rgba(16, 185, 129, 0.6);
}

.control-button.decline, .control-button.end-call {
  background: rgba(239, 68, 68, 0.3);
  border-color: rgba(239, 68, 68, 0.6);
}

.control-button.callback {
  background: rgba(16, 185, 129, 0.3);
  border-color: rgba(16, 185, 129, 0.6);
}

/* 互动元素 */
.interaction-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 6;
}

.heart-click-area {
  position: absolute;
  top: 50%;
  right: 20%;
  width: 100px;
  height: 100px;
  transform: translateY(-50%);
  pointer-events: auto;
  cursor: pointer;
}

.quick-emotions {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 12px;
  pointer-events: auto;
}

.emotion-btn {
  width: 50px;
  height: 50px;
  background: rgba(0, 0, 0, 0.6);
  border: none;
  border-radius: 50%;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.emotion-btn:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
}

/* 提示浮层 */
.call-tip-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 15;
}

.call-tip {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  backdrop-filter: blur(10px);
}

/* 动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .call-status-bar {
    padding: 0 16px;
  }
  
  .character-avatar {
    width: 80px;
    height: 80px;
  }
  
  .character-name {
    font-size: 16px;
  }
  
  .local-video-window {
    width: 100px;
    height: 133px;
    bottom: 140px;
    right: 16px;
  }
  
  .control-button {
    padding: 12px 16px;
    min-width: 70px;
    font-size: 11px;
  }
  
  .active-controls {
    gap: 12px;
  }
  
  .quick-emotions {
    left: 16px;
  }
  
  .emotion-btn {
    width: 45px;
    height: 45px;
    font-size: 20px;
  }
}
</style>