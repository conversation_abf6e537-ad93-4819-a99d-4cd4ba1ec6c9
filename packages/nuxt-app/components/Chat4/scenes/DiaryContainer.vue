<template>
  <div class="diary-container">
    <!-- 增强背景 -->
    <EnhancedBackground
      :video-url="backgroundVideoUrl"
      :image-url="backgroundImageUrl"
      :blur="showModal"
      transition-mode="fade"
      :transition-duration="800"
    />
    
    <!-- 日记界面 -->
    <div class="diary-interface">
      <!-- 顶部导航栏 -->
      <div class="top-navigation">
        <div class="nav-left">
          <button class="nav-btn" @click="goBack">
            <Icon name="lucide:arrow-left" />
          </button>
          <h1 class="page-title">我的日记</h1>
        </div>
        
        <div class="nav-right">
          <button class="nav-btn" @click="createNewDiary">
            <Icon name="lucide:plus" />
          </button>
          <button class="nav-btn" @click="showCalendarView">
            <Icon name="lucide:calendar" />
          </button>
          <button class="nav-btn" @click="showSettings">
            <Icon name="lucide:settings" />
          </button>
        </div>
      </div>
      
      <!-- 日记统计卡片 -->
      <div class="diary-stats">
        <div class="stats-card">
          <div class="stat-item">
            <div class="stat-icon">
              <Icon name="lucide:book-open" />
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ totalEntries }}</div>
              <div class="stat-label">总篇数</div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon">
              <Icon name="lucide:calendar-days" />
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ consecutiveDays }}</div>
              <div class="stat-label">连续天数</div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon">
              <Icon name="lucide:heart" />
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ moodAverage }}/10</div>
              <div class="stat-label">平均心情</div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon">
              <Icon name="lucide:clock" />
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ totalWords }}</div>
              <div class="stat-label">总字数</div>
            </div>
          </div>
        </div>
        
        <!-- 心情趋势 -->
        <div class="mood-trend">
          <div class="trend-title">最近心情趋势</div>
          <div class="trend-chart">
            <div 
              v-for="(mood, index) in recentMoods" 
              :key="index"
              class="mood-bar"
              :style="{ height: (mood.value * 10) + '%' }"
              :title="`${mood.date}: ${mood.value}/10`"
            ></div>
          </div>
        </div>
      </div>
      
      <!-- 日记列表 -->
      <div class="diary-list">
        <!-- 搜索和筛选 -->
        <div class="list-controls">
          <div class="search-box">
            <Icon name="lucide:search" />
            <input 
              v-model="searchQuery"
              type="text"
              placeholder="搜索日记内容..."
              @input="handleSearch"
            />
          </div>
          
          <div class="filter-buttons">
            <button 
              v-for="filter in moodFilters"
              :key="filter.mood"
              class="filter-btn"
              :class="{ 'active': selectedMoodFilter === filter.mood }"
              @click="selectMoodFilter(filter.mood)"
            >
              <span class="filter-emoji">{{ filter.emoji }}</span>
              <span class="filter-label">{{ filter.label }}</span>
            </button>
          </div>
        </div>
        
        <!-- 日记条目 -->
        <div class="diary-entries">
          <div 
            v-for="entry in filteredEntries" 
            :key="entry.id"
            class="diary-entry"
            :class="{ 'private': entry.isPrivate }"
            @click="openDiaryEntry(entry)"
          >
            <div class="entry-header">
              <div class="entry-date">
                <div class="date-main">{{ formatDate(entry.date) }}</div>
                <div class="date-time">{{ formatTime(entry.date) }}</div>
              </div>
              
              <div class="entry-mood">
                <div class="mood-emoji">{{ getMoodEmoji(entry.mood) }}</div>
                <div class="mood-level">{{ entry.mood }}/10</div>
              </div>
              
              <div class="entry-actions">
                <button v-if="entry.isPrivate" class="action-btn private">
                  <Icon name="lucide:lock" />
                </button>
                <button class="action-btn" @click.stop="editEntry(entry)">
                  <Icon name="lucide:edit" />
                </button>
                <button class="action-btn" @click.stop="showEntryMenu(entry)">
                  <Icon name="lucide:more-horizontal" />
                </button>
              </div>
            </div>
            
            <div class="entry-content">
              <h3 v-if="entry.title" class="entry-title">{{ entry.title }}</h3>
              <p class="entry-preview">{{ getPreviewText(entry.content) }}</p>
              
              <!-- 标签 -->
              <div v-if="entry.tags.length > 0" class="entry-tags">
                <span 
                  v-for="tag in entry.tags"
                  :key="tag"
                  class="tag"
                >
                  #{{ tag }}
                </span>
              </div>
              
              <!-- 图片预览 -->
              <div v-if="entry.images.length > 0" class="entry-images">
                <img 
                  v-for="(image, index) in entry.images.slice(0, 3)"
                  :key="index"
                  :src="image"
                  :alt="`Entry image ${index + 1}`"
                  class="entry-image"
                />
                <div v-if="entry.images.length > 3" class="more-images">
                  +{{ entry.images.length - 3 }}
                </div>
              </div>
            </div>
            
            <div class="entry-footer">
              <div class="entry-stats">
                <span class="stat">
                  <Icon name="lucide:type" />
                  {{ entry.wordCount }} 字
                </span>
                <span class="stat">
                  <Icon name="lucide:clock" />
                  {{ getWritingTime(entry.wordCount) }}
                </span>
              </div>
              
              <div class="weather-info" v-if="entry.weather">
                <Icon :name="getWeatherIcon(entry.weather)" />
                <span>{{ entry.weather }}</span>
              </div>
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-if="filteredEntries.length === 0" class="empty-state">
            <div class="empty-icon">
              <Icon name="lucide:book-open" />
            </div>
            <div class="empty-title">暂无日记</div>
            <div class="empty-subtitle">
              {{ searchQuery ? '未找到匹配的日记' : '开始记录你的生活吧' }}
            </div>
            <button v-if="!searchQuery" class="create-btn" @click="createNewDiary">
              <Icon name="lucide:plus" />
              写第一篇日记
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 日记编辑弹窗 -->
    <Teleport to="body">
      <div v-if="showEditor" class="diary-editor-modal" @click.self="closeEditor">
        <div class="editor-container">
          <div class="editor-header">
            <h2>{{ isEditing ? '编辑日记' : '新建日记' }}</h2>
            <div class="editor-actions">
              <button class="action-btn" @click="togglePrivate">
                <Icon :name="currentEntry.isPrivate ? 'lucide:lock' : 'lucide:unlock'" />
              </button>
              <button class="action-btn" @click="closeEditor">
                <Icon name="lucide:x" />
              </button>
            </div>
          </div>
          
          <div class="editor-content">
            <!-- 日期选择 -->
            <div class="date-picker">
              <input 
                v-model="currentEntry.date"
                type="datetime-local"
                class="date-input"
              />
            </div>
            
            <!-- 标题 -->
            <input 
              v-model="currentEntry.title"
              type="text"
              placeholder="给这篇日记起个标题..."
              class="title-input"
            />
            
            <!-- 心情选择 -->
            <div class="mood-selector">
              <label class="mood-label">今天心情：</label>
              <div class="mood-scale">
                <div 
                  v-for="level in 10"
                  :key="level"
                  class="mood-point"
                  :class="{ 'selected': currentEntry.mood === level }"
                  @click="currentEntry.mood = level"
                >
                  <span class="mood-number">{{ level }}</span>
                  <span class="mood-emoji">{{ getMoodEmoji(level) }}</span>
                </div>
              </div>
            </div>
            
            <!-- 天气选择 -->
            <div class="weather-selector">
              <label class="weather-label">天气：</label>
              <div class="weather-options">
                <button 
                  v-for="weather in weatherOptions"
                  :key="weather.value"
                  class="weather-btn"
                  :class="{ 'selected': currentEntry.weather === weather.value }"
                  @click="currentEntry.weather = weather.value"
                >
                  <Icon :name="weather.icon" />
                  <span>{{ weather.label }}</span>
                </button>
              </div>
            </div>
            
            <!-- 内容编辑 -->
            <textarea 
              v-model="currentEntry.content"
              placeholder="记录今天发生的事情..."
              class="content-textarea"
              rows="10"
              @input="updateWordCount"
            ></textarea>
            
            <!-- 标签输入 -->
            <div class="tags-input">
              <label class="tags-label">标签：</label>
              <div class="tags-container">
                <span 
                  v-for="tag in currentEntry.tags"
                  :key="tag"
                  class="tag-item"
                >
                  #{{ tag }}
                  <button class="remove-tag" @click="removeTag(tag)">
                    <Icon name="lucide:x" />
                  </button>
                </span>
                <input 
                  v-model="newTag"
                  type="text"
                  placeholder="添加标签..."
                  class="tag-input"
                  @keydown.enter.prevent="addTag"
                  @keydown.space.prevent="addTag"
                />
              </div>
            </div>
            
            <!-- 图片上传 -->
            <div class="image-upload">
              <label class="upload-label">图片：</label>
              <div class="upload-area">
                <div v-if="currentEntry.images.length > 0" class="uploaded-images">
                  <div 
                    v-for="(image, index) in currentEntry.images"
                    :key="index"
                    class="uploaded-image"
                  >
                    <img :src="image" :alt="`Image ${index + 1}`" />
                    <button class="remove-image" @click="removeImage(index)">
                      <Icon name="lucide:x" />
                    </button>
                  </div>
                </div>
                <button class="upload-btn" @click="uploadImage">
                  <Icon name="lucide:image" />
                  <span>添加图片</span>
                </button>
              </div>
            </div>
          </div>
          
          <div class="editor-footer">
            <div class="word-count">{{ currentEntry.wordCount || 0 }} 字</div>
            <div class="footer-actions">
              <button class="cancel-btn" @click="closeEditor">取消</button>
              <button class="save-btn" @click="saveDiary">
                {{ isEditing ? '保存' : '创建' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </Teleport>
    
    <!-- 日记详情弹窗 -->
    <Teleport to="body">
      <div v-if="showDetail" class="diary-detail-modal" @click.self="closeDetail">
        <div class="detail-container">
          <div class="detail-header">
            <div class="detail-date">
              <div class="date-main">{{ formatDate(selectedEntry?.date) }}</div>
              <div class="date-time">{{ formatTime(selectedEntry?.date) }}</div>
            </div>
            
            <div class="detail-actions">
              <button class="action-btn" @click="editEntry(selectedEntry)">
                <Icon name="lucide:edit" />
              </button>
              <button class="action-btn" @click="closeDetail">
                <Icon name="lucide:x" />
              </button>
            </div>
          </div>
          
          <div class="detail-content">
            <div class="detail-mood">
              <span class="mood-emoji">{{ getMoodEmoji(selectedEntry?.mood) }}</span>
              <span class="mood-text">心情：{{ selectedEntry?.mood }}/10</span>
              <span v-if="selectedEntry?.weather" class="weather-info">
                <Icon :name="getWeatherIcon(selectedEntry.weather)" />
                {{ selectedEntry.weather }}
              </span>
            </div>
            
            <h1 v-if="selectedEntry?.title" class="detail-title">
              {{ selectedEntry.title }}
            </h1>
            
            <div class="detail-text">{{ selectedEntry?.content }}</div>
            
            <div v-if="selectedEntry?.tags.length > 0" class="detail-tags">
              <span 
                v-for="tag in selectedEntry.tags"
                :key="tag"
                class="tag"
              >
                #{{ tag }}
              </span>
            </div>
            
            <div v-if="selectedEntry?.images.length > 0" class="detail-images">
              <img 
                v-for="(image, index) in selectedEntry.images"
                :key="index"
                :src="image"
                :alt="`Image ${index + 1}`"
                class="detail-image"
                @click="showImagePreview(image, index)"
              />
            </div>
          </div>
          
          <div class="detail-footer">
            <div class="detail-stats">
              <span class="stat">
                <Icon name="lucide:type" />
                {{ selectedEntry?.wordCount }} 字
              </span>
              <span class="stat">
                <Icon name="lucide:clock" />
                预计阅读 {{ Math.ceil((selectedEntry?.wordCount || 0) / 300) }} 分钟
              </span>
            </div>
          </div>
        </div>
      </div>
    </Teleport>
    
    <!-- 日历视图弹窗 -->
    <Teleport to="body">
      <div v-if="showCalendar" class="calendar-modal" @click.self="closeCalendar">
        <div class="calendar-container">
          <div class="calendar-header">
            <h2>日记日历</h2>
            <button class="close-btn" @click="closeCalendar">
              <Icon name="lucide:x" />
            </button>
          </div>
          
          <div class="calendar-content">
            <div class="calendar-navigation">
              <button @click="previousMonth">
                <Icon name="lucide:chevron-left" />
              </button>
              <span class="current-month">{{ currentMonthYear }}</span>
              <button @click="nextMonth">
                <Icon name="lucide:chevron-right" />
              </button>
            </div>
            
            <div class="calendar-grid">
              <div class="calendar-weekdays">
                <div v-for="day in weekdays" :key="day" class="weekday">{{ day }}</div>
              </div>
              
              <div class="calendar-days">
                <div 
                  v-for="day in calendarDays"
                  :key="`${day.date}-${day.isCurrentMonth}`"
                  class="calendar-day"
                  :class="{
                    'other-month': !day.isCurrentMonth,
                    'has-entry': day.hasEntry,
                    'today': day.isToday
                  }"
                  @click="selectCalendarDay(day)"
                >
                  <span class="day-number">{{ day.day }}</span>
                  <div v-if="day.hasEntry" class="day-indicator">
                    <span class="entry-mood">{{ getMoodEmoji(day.mood) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Teleport>
    
    <!-- 图片预览弹窗 -->
    <Teleport to="body">
      <div v-if="showImagePreviewModal" class="image-preview-modal" @click.self="closeImagePreview">
        <div class="image-preview-container">
          <button class="close-btn" @click="closeImagePreview">
            <Icon name="lucide:x" />
          </button>
          
          <img :src="previewImage" alt="Preview" class="preview-image" />
          
          <div v-if="previewImages.length > 1" class="image-navigation">
            <button 
              class="nav-btn prev" 
              @click="previousImage"
              :disabled="previewIndex === 0"
            >
              <Icon name="lucide:chevron-left" />
            </button>
            
            <span class="image-counter">
              {{ previewIndex + 1 }} / {{ previewImages.length }}
            </span>
            
            <button 
              class="nav-btn next" 
              @click="nextImage"
              :disabled="previewIndex === previewImages.length - 1"
            >
              <Icon name="lucide:chevron-right" />
            </button>
          </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useChat4Store } from '~/stores/chat4'
import EnhancedBackground from '~/components/common/EnhancedBackground.vue'

// 导入组件
import EnhancedBackground from '~/components/common/EnhancedBackground.vue'

// 接口定义
interface DiaryEntry {
  id: string
  title: string
  content: string
  date: string
  mood: number // 1-10
  weather?: string
  tags: string[]
  images: string[]
  isPrivate: boolean
  wordCount: number
  createdAt: string
  updatedAt: string
}

interface CalendarDay {
  date: string
  day: number
  isCurrentMonth: boolean
  hasEntry: boolean
  mood?: number
  isToday: boolean
}

// Store
const chat4Store = useChat4Store()

// 响应式数据
const showEditor = ref(false)
const showDetail = ref(false)
const showCalendar = ref(false)
const showImagePreviewModal = ref(false)
const showModal = computed(() => showEditor.value || showDetail.value || showCalendar.value || showImagePreviewModal.value)

const isEditing = ref(false)
const searchQuery = ref('')
const selectedMoodFilter = ref<number | null>(null)
const newTag = ref('')

// 当前编辑的日记
const currentEntry = ref<DiaryEntry>({
  id: '',
  title: '',
  content: '',
  date: new Date().toISOString().slice(0, 16),
  mood: 5,
  weather: '晴天',
  tags: [],
  images: [],
  isPrivate: false,
  wordCount: 0,
  createdAt: '',
  updatedAt: ''
})

// 选中的日记
const selectedEntry = ref<DiaryEntry | null>(null)

// 图片预览相关
const previewImage = ref('')
const previewImages = ref<string[]>([])
const previewIndex = ref(0)

// 日历相关
const currentCalendarDate = ref(new Date())

// 模拟数据
const diaryEntries = ref<DiaryEntry[]>([
  {
    id: '1',
    title: '美好的一天',
    content: '今天天气很好，和朋友一起去公园散步，心情格外舒畅。看到了很多美丽的花朵，还遇到了一只可爱的小猫。生活中的小确幸总是让人感到温暖。',
    date: '2024-08-01T10:30:00',
    mood: 8,
    weather: '晴天',
    tags: ['散步', '朋友', '心情好'],
    images: [
      'https://picsum.photos/400/300?random=801',
      'https://picsum.photos/400/300?random=802',
    ],
    isPrivate: false,
    wordCount: 68,
    createdAt: '2024-08-01T10:30:00',
    updatedAt: '2024-08-01T10:30:00'
  },
  {
    id: '2',
    title: '工作日常',
    content: '今天工作很忙，但是完成了一个重要的项目，感觉很有成就感。虽然加班到很晚，但是看到成果还是很开心的。',
    date: '2024-07-31T22:15:00',
    mood: 6,
    weather: '多云',
    tags: ['工作', '项目', '成就感'],
    images: [],
    isPrivate: true,
    wordCount: 52,
    createdAt: '2024-07-31T22:15:00',
    updatedAt: '2024-07-31T22:15:00'
  },
  {
    id: '3',
    title: '下雨天的思考',
    content: '窗外下着小雨，坐在咖啡厅里写字，听着雨声，心情很平静。有时候慢下来的生活节奏让人更能感受到生活的美好。',
    date: '2024-07-30T16:20:00',
    mood: 7,
    weather: '雨天',
    tags: ['雨天', '咖啡厅', '思考'],
    images: ['https://picsum.photos/400/300?random=803'],
    isPrivate: false,
    wordCount: 56,
    createdAt: '2024-07-30T16:20:00',
    updatedAt: '2024-07-30T16:20:00'
  }
])

// 背景设置
const backgroundVideoUrl = ref('https://storage.googleapis.com/example-videos/diary-background.mp4')
const backgroundImageUrl = ref('https://picsum.photos/1920/1080?random=900')

// 计算属性
const totalEntries = computed(() => diaryEntries.value.length)
const consecutiveDays = computed(() => {
  // 计算连续写日记的天数
  if (diaryEntries.value.length === 0) return 0
  
  const sortedEntries = [...diaryEntries.value].sort((a, b) => 
    new Date(b.date).getTime() - new Date(a.date).getTime()
  )
  
  let consecutive = 1
  let currentDate = new Date(sortedEntries[0].date)
  
  for (let i = 1; i < sortedEntries.length; i++) {
    const entryDate = new Date(sortedEntries[i].date)
    const daysDiff = Math.floor((currentDate.getTime() - entryDate.getTime()) / (1000 * 60 * 60 * 24))
    
    if (daysDiff === 1) {
      consecutive++
      currentDate = entryDate
    } else {
      break
    }
  }
  
  return consecutive
})

const moodAverage = computed(() => {
  if (diaryEntries.value.length === 0) return 0
  const total = diaryEntries.value.reduce((sum, entry) => sum + entry.mood, 0)
  return Math.round((total / diaryEntries.value.length) * 10) / 10
})

const totalWords = computed(() => {
  return diaryEntries.value.reduce((sum, entry) => sum + entry.wordCount, 0)
})

const recentMoods = computed(() => {
  const recent = [...diaryEntries.value]
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 7)
    .reverse()
  
  return recent.map(entry => ({
    date: formatDate(entry.date),
    value: entry.mood
  }))
})

const filteredEntries = computed(() => {
  let filtered = [...diaryEntries.value]
  
  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(entry => 
      entry.title.toLowerCase().includes(query) ||
      entry.content.toLowerCase().includes(query) ||
      entry.tags.some(tag => tag.toLowerCase().includes(query))
    )
  }
  
  // 心情过滤
  if (selectedMoodFilter.value !== null) {
    filtered = filtered.filter(entry => entry.mood === selectedMoodFilter.value)
  }
  
  // 按日期排序
  return filtered.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
})

const currentMonthYear = computed(() => {
  return currentCalendarDate.value.toLocaleDateString('zh-CN', { 
    year: 'numeric', 
    month: 'long' 
  })
})

const calendarDays = computed(() => {
  const year = currentCalendarDate.value.getFullYear()
  const month = currentCalendarDate.value.getMonth()
  
  // 获取当月第一天和最后一天
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  
  // 获取日历开始日期（包含上月末尾几天）
  const startDate = new Date(firstDay)
  startDate.setDate(startDate.getDate() - firstDay.getDay())
  
  // 获取日历结束日期（包含下月开始几天）
  const endDate = new Date(lastDay)
  endDate.setDate(endDate.getDate() + (6 - lastDay.getDay()))
  
  const days: CalendarDay[] = []
  const currentDate = new Date(startDate)
  const today = new Date()
  
  while (currentDate <= endDate) {
    const dateStr = currentDate.toISOString().split('T')[0]
    const entry = diaryEntries.value.find(e => 
      e.date.split('T')[0] === dateStr
    )
    
    days.push({
      date: dateStr,
      day: currentDate.getDate(),
      isCurrentMonth: currentDate.getMonth() === month,
      hasEntry: !!entry,
      mood: entry?.mood,
      isToday: currentDate.toDateString() === today.toDateString()
    })
    
    currentDate.setDate(currentDate.getDate() + 1)
  }
  
  return days
})

// 常量数据
const weekdays = ['日', '一', '二', '三', '四', '五', '六']

const moodFilters = [
  { mood: null, emoji: '😊', label: '全部' },
  { mood: 9, emoji: '😍', label: '超开心' },
  { mood: 7, emoji: '😊', label: '开心' },
  { mood: 5, emoji: '😐', label: '一般' },
  { mood: 3, emoji: '😔', label: '不开心' },
  { mood: 1, emoji: '😢', label: '很难过' }
]

const weatherOptions = [
  { value: '晴天', label: '晴天', icon: 'lucide:sun' },
  { value: '多云', label: '多云', icon: 'lucide:cloud' },
  { value: '雨天', label: '雨天', icon: 'lucide:cloud-rain' },
  { value: '雪天', label: '雪天', icon: 'lucide:cloud-snow' },
  { value: '雾天', label: '雾天', icon: 'lucide:cloud-fog' }
]

// 方法
const goBack = () => {
  chat4Store.exitScene()
}

const createNewDiary = () => {
  currentEntry.value = {
    id: '',
    title: '',
    content: '',
    date: new Date().toISOString().slice(0, 16),
    mood: 5,
    weather: '晴天',
    tags: [],
    images: [],
    isPrivate: false,
    wordCount: 0,
    createdAt: '',
    updatedAt: ''
  }
  isEditing.value = false
  showEditor.value = true
}

const editEntry = (entry: DiaryEntry) => {
  currentEntry.value = { ...entry }
  isEditing.value = true
  showDetail.value = false
  showEditor.value = true
}

const closeEditor = () => {
  showEditor.value = false
  isEditing.value = false
}

const saveDiary = () => {
  if (!currentEntry.value.content.trim()) return
  
  const now = new Date().toISOString()
  
  if (isEditing.value) {
    // 更新现有日记
    const index = diaryEntries.value.findIndex(e => e.id === currentEntry.value.id)
    if (index !== -1) {
      diaryEntries.value[index] = {
        ...currentEntry.value,
        updatedAt: now
      }
    }
  } else {
    // 创建新日记
    const newEntry: DiaryEntry = {
      ...currentEntry.value,
      id: Date.now().toString(),
      createdAt: now,
      updatedAt: now
    }
    diaryEntries.value.unshift(newEntry)
  }
  
  closeEditor()
}

const openDiaryEntry = (entry: DiaryEntry) => {
  selectedEntry.value = entry
  showDetail.value = true
}

const closeDetail = () => {
  showDetail.value = false
  selectedEntry.value = null
}

const togglePrivate = () => {
  currentEntry.value.isPrivate = !currentEntry.value.isPrivate
}

const updateWordCount = () => {
  currentEntry.value.wordCount = currentEntry.value.content.length
}

const addTag = () => {
  const tag = newTag.value.trim()
  if (tag && !currentEntry.value.tags.includes(tag)) {
    currentEntry.value.tags.push(tag)
    newTag.value = ''
  }
}

const removeTag = (tag: string) => {
  const index = currentEntry.value.tags.indexOf(tag)
  if (index > -1) {
    currentEntry.value.tags.splice(index, 1)
  }
}

const uploadImage = () => {
  // 模拟图片上传
  const imageUrl = `https://picsum.photos/400/300?random=${Date.now()}`
  currentEntry.value.images.push(imageUrl)
}

const removeImage = (index: number) => {
  currentEntry.value.images.splice(index, 1)
}

const handleSearch = () => {
  // 搜索逻辑已在 computed 中实现
}

const selectMoodFilter = (mood: number | null) => {
  selectedMoodFilter.value = mood
}

const showCalendarView = () => {
  showCalendar.value = true
}

const closeCalendar = () => {
  showCalendar.value = false
}

const previousMonth = () => {
  const newDate = new Date(currentCalendarDate.value)
  newDate.setMonth(newDate.getMonth() - 1)
  currentCalendarDate.value = newDate
}

const nextMonth = () => {
  const newDate = new Date(currentCalendarDate.value)
  newDate.setMonth(newDate.getMonth() + 1)
  currentCalendarDate.value = newDate
}

const selectCalendarDay = (day: CalendarDay) => {
  if (day.hasEntry) {
    const entry = diaryEntries.value.find(e => 
      e.date.split('T')[0] === day.date
    )
    if (entry) {
      openDiaryEntry(entry)
      closeCalendar()
    }
  } else {
    // 创建新日记，设置为选中日期
    currentEntry.value = {
      id: '',
      title: '',
      content: '',
      date: `${day.date}T${new Date().toTimeString().slice(0, 5)}`,
      mood: 5,
      weather: '晴天',
      tags: [],
      images: [],
      isPrivate: false,
      wordCount: 0,
      createdAt: '',
      updatedAt: ''
    }
    isEditing.value = false
    closeCalendar()
    showEditor.value = true
  }
}

const showImagePreview = (image: string, index: number) => {
  previewImage.value = image
  previewImages.value = selectedEntry.value?.images || []
  previewIndex.value = index
  showImagePreviewModal.value = true
}

const closeImagePreview = () => {
  showImagePreviewModal.value = false
  previewImage.value = ''
  previewImages.value = []
  previewIndex.value = 0
}

const previousImage = () => {
  if (previewIndex.value > 0) {
    previewIndex.value--
    previewImage.value = previewImages.value[previewIndex.value]
  }
}

const nextImage = () => {
  if (previewIndex.value < previewImages.value.length - 1) {
    previewIndex.value++
    previewImage.value = previewImages.value[previewIndex.value]
  }
}

const showEntryMenu = (entry: DiaryEntry) => {
  // 显示日记菜单（复制、分享、删除等）
  console.log('Show menu for entry:', entry.id)
}

const showSettings = () => {
  // 显示设置弹窗
  console.log('Show settings')
}

const refreshMoments = () => {
  // 刷新日记列表
  console.log('Refresh diary entries')
}

// 辅助函数
const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  })
}

const formatTime = (dateStr: string) => {
  const date = new Date(dateStr)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getMoodEmoji = (mood: number) => {
  const emojis = ['😫', '😢', '😔', '😕', '😐', '🙂', '😊', '😄', '😁', '😍']
  return emojis[mood - 1] || '😐'
}

const getWeatherIcon = (weather: string) => {
  const icons: { [key: string]: string } = {
    '晴天': 'lucide:sun',
    '多云': 'lucide:cloud',
    '雨天': 'lucide:cloud-rain',
    '雪天': 'lucide:cloud-snow',
    '雾天': 'lucide:cloud-fog'
  }
  return icons[weather] || 'lucide:cloud'
}

const getPreviewText = (content: string) => {
  return content.length > 100 ? content.slice(0, 100) + '...' : content
}

const getWritingTime = (wordCount: number) => {
  const minutes = Math.ceil(wordCount / 100) // 假设每分钟写100字
  return minutes < 1 ? '< 1分钟' : `${minutes}分钟`
}

// 监听器
watch(() => currentEntry.value.content, () => {
  updateWordCount()
})

// 生命周期
onMounted(() => {
  console.log('DiaryContainer mounted')
})
</script>

<style scoped>
.diary-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.diary-interface {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

/* 导航栏 */
.top-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-left, .nav-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.page-title {
  color: white;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

/* 统计卡片 */
.diary-stats {
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stats-card {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
}

.stat-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: white;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 4px;
}

/* 心情趋势 */
.mood-trend {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 16px;
  backdrop-filter: blur(10px);
}

.trend-title {
  color: white;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
}

.trend-chart {
  display: flex;
  align-items: end;
  gap: 4px;
  height: 60px;
}

.mood-bar {
  flex: 1;
  min-height: 6px;
  background: linear-gradient(to top, #ff6b6b, #4ecdc4);
  border-radius: 2px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.mood-bar:hover {
  transform: scaleY(1.1);
  filter: brightness(1.2);
}

/* 日记列表 */
.diary-list {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.list-controls {
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.search-box {
  position: relative;
  margin-bottom: 16px;
}

.search-box input {
  width: 100%;
  padding: 12px 16px 12px 40px;
  border: none;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 14px;
  backdrop-filter: blur(10px);
}

.search-box input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.search-box .icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.6);
}

.filter-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateY(-1px);
}

.filter-emoji {
  font-size: 14px;
}

/* 日记条目 */
.diary-entries {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px 20px;
}

.diary-entry {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.diary-entry:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.diary-entry.private {
  border-left: 4px solid #ff6b6b;
}

.entry-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.entry-date {
  display: flex;
  flex-direction: column;
}

.date-main {
  color: white;
  font-size: 16px;
  font-weight: 600;
}

.date-time {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
}

.entry-mood {
  display: flex;
  align-items: center;
  gap: 6px;
}

.mood-emoji {
  font-size: 20px;
}

.mood-level {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

.entry-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: scale(1.1);
}

.action-btn.private {
  color: #ff6b6b;
}

.entry-content {
  margin-bottom: 12px;
}

.entry-title {
  color: white;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.entry-preview {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 12px 0;
}

.entry-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  margin-bottom: 12px;
}

.tag {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.entry-images {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.entry-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
}

.more-images {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

.entry-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.entry-stats {
  display: flex;
  gap: 16px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
}

.weather-info {
  display: flex;
  align-items: center;
  gap: 4px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 32px;
}

.empty-title {
  color: white;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.empty-subtitle {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  margin-bottom: 24px;
}

.create-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 24px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.create-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

/* 弹窗样式 */
.diary-editor-modal,
.diary-detail-modal,
.calendar-modal,
.image-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.editor-container,
.detail-container,
.calendar-container {
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  backdrop-filter: blur(20px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.editor-header,
.detail-header,
.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.editor-content,
.detail-content,
.calendar-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.editor-footer,
.detail-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.5);
}

/* 编辑器样式 */
.date-picker {
  margin-bottom: 16px;
}

.date-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
}

.title-input {
  width: 100%;
  padding: 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
}

.mood-selector,
.weather-selector {
  margin-bottom: 16px;
}

.mood-label,
.weather-label {
  display: block;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.mood-scale {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.mood-point {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border: 2px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mood-point:hover,
.mood-point.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.mood-number {
  font-size: 12px;
  font-weight: 600;
  color: #666;
}

.mood-emoji {
  font-size: 20px;
}

.weather-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.weather-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.weather-btn:hover,
.weather-btn.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.content-textarea {
  width: 100%;
  min-height: 200px;
  padding: 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.6;
  resize: vertical;
  margin-bottom: 16px;
}

.tags-input {
  margin-bottom: 16px;
}

.tags-label {
  display: block;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.tags-container {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  align-items: center;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 8px;
  min-height: 40px;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #667eea;
  color: white;
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 12px;
}

.remove-tag {
  width: 16px;
  height: 16px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 10px;
}

.tag-input {
  flex: 1;
  min-width: 120px;
  border: none;
  outline: none;
  padding: 4px;
  font-size: 12px;
}

.image-upload {
  margin-bottom: 16px;
}

.upload-label {
  display: block;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.upload-area {
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 16px;
}

.uploaded-images {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 12px;
}

.uploaded-image {
  position: relative;
  width: 80px;
  height: 80px;
}

.uploaded-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.remove-image {
  position: absolute;
  top: -6px;
  right: -6px;
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 50%;
  background: #ff4757;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
}

.upload-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 1px solid #667eea;
  border-radius: 8px;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-btn:hover {
  background: rgba(102, 126, 234, 0.2);
}

.word-count {
  color: #666;
  font-size: 12px;
}

.footer-actions {
  display: flex;
  gap: 12px;
}

.cancel-btn,
.save-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #f1f2f6;
  color: #666;
}

.cancel-btn:hover {
  background: #ddd;
}

.save-btn {
  background: #667eea;
  color: white;
}

.save-btn:hover {
  background: #5a67d8;
}

/* 详情页面样式 */
.detail-date {
  display: flex;
  flex-direction: column;
}

.detail-actions {
  display: flex;
  gap: 8px;
}

.detail-mood {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.mood-text {
  color: #666;
  font-size: 14px;
}

.detail-title {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin: 0 0 16px 0;
}

.detail-text {
  font-size: 16px;
  line-height: 1.8;
  color: #444;
  margin-bottom: 20px;
  white-space: pre-wrap;
}

.detail-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.detail-images {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

.detail-image {
  width: 100%;
  aspect-ratio: 1;
  object-fit: cover;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.detail-image:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.detail-stats {
  display: flex;
  gap: 20px;
}

/* 日历样式 */
.calendar-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.current-month {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.calendar-grid {
  width: 100%;
}

.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  margin-bottom: 8px;
}

.weekday {
  padding: 8px;
  text-align: center;
  font-size: 12px;
  font-weight: 600;
  color: #666;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
}

.calendar-day {
  position: relative;
  aspect-ratio: 1;
  padding: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
}

.calendar-day:hover {
  background: rgba(102, 126, 234, 0.1);
}

.calendar-day.other-month {
  color: #ccc;
}

.calendar-day.today {
  background: #667eea;
  color: white;
}

.calendar-day.has-entry {
  background: rgba(102, 126, 234, 0.2);
  border: 2px solid #667eea;
}

.day-number {
  font-size: 14px;
  font-weight: 500;
}

.day-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
}

.entry-mood {
  font-size: 12px;
}

/* 图片预览样式 */
.image-preview-container {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 12px;
}

.image-navigation {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-top: 20px;
  background: rgba(0, 0, 0, 0.5);
  padding: 12px 20px;
  border-radius: 24px;
}

.nav-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.image-counter {
  color: white;
  font-size: 14px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-card {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .editor-container,
  .detail-container,
  .calendar-container {
    width: 95%;
    margin: 20px;
  }
  
  .mood-scale {
    justify-content: center;
  }
  
  .weather-options {
    justify-content: center;
  }
  
  .detail-images {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .top-navigation {
    padding: 12px 16px;
  }
  
  .diary-stats,
  .list-controls,
  .diary-entries {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  .stats-card {
    grid-template-columns: 1fr;
  }
  
  .filter-buttons {
    justify-content: center;
  }
  
  .entry-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .entry-actions {
    align-self: flex-end;
  }
}
</style>