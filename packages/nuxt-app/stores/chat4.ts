/**
 * Chat4 状态管理
 * 使用Pinia + Composition API
 */

import type { 
  Chat4SceneState, 
  Chat4State, 
  ChatMessage, 
  LiveComment,
  GiftItem,
  SceneChangeEvent 
} from '~/types/chat4'

import { Chat4SceneUtils } from '~/types/chat4'

export const useChat4Store = defineStore('chat4', () => {
  // ==================== 状态定义 ====================
  
  // 场景状态
  const currentScene = ref<Chat4SceneState>('Living')
  const isTransitioning = ref(false)
  const isLoading = ref(false)
  
  // 游戏数据
  const currentActor = ref<any>(null)
  const currentStory = ref<any>(null)
  
  // 消息状态
  const messages = ref<ChatMessage[]>([])
  const liveComments = ref<LiveComment[]>([])
  
  // UI状态
  const showFriendRequestModal = ref(false)
  const showGiftModal = ref(false)
  const showFavorabilityDrawer = ref(false)
  const showPaymentModal = ref(false)
  const heartClickCount = ref(0)
  const viewerCount = ref(128) // 默认观众数
  
  // WebSocket状态
  const wsConnection = ref<WebSocket | null>(null)
  const isConnected = ref(false)
  
  // ==================== 场景切换方法 ====================
  
  /**
   * 设置当前场景
   */
  const setCurrentScene = (sceneId: Chat4SceneState) => {
    if (currentScene.value !== sceneId) {
      const oldScene = currentScene.value
      
      console.log('🎬 场景切换开始:', {
        from: oldScene,
        to: sceneId,
        timestamp: Date.now()
      })
      
      isTransitioning.value = true
      currentScene.value = sceneId
      
      // 清理旧场景状态
      clearSceneSpecificState(oldScene)
      
      // 触发场景变化事件
      const sceneChangeEvent: SceneChangeEvent = {
        from: oldScene,
        to: sceneId,
        timestamp: Date.now()
      }
      
      // 延迟结束转场状态
      nextTick(() => {
        setTimeout(() => {
          isTransitioning.value = false
          console.log('🎬 场景切换完成:', sceneChangeEvent)
        }, 300)
      })
    }
  }
  
  /**
   * 快捷场景切换方法
   */
  const goToChat = () => setCurrentScene('Phone')
  const goToVideo = () => setCurrentScene('Video')
  const goToLive = () => setCurrentScene('Living')
  const goToMap = () => setCurrentScene('Map')
  const goToMeetup = () => setCurrentScene('Meetup')
  const goToDancing = () => setCurrentScene('Dancing')
  const goToConcert = () => setCurrentScene('Concert')
  const goToMoment = () => setCurrentScene('Moment')
  const goToDiary = () => setCurrentScene('Diary')
  const goToMonitor = () => setCurrentScene('Monitor')
  
  // ==================== 游戏初始化 ====================
  
  /**
   * 初始化游戏
   */
  const initGame = async (characterId: string, storyId: string) => {
    console.log('🎮 Chat4Store: 开始初始化游戏', {
      characterId,
      storyId
    })
    
    isLoading.value = true
    
    try {
      // TODO: 加载角色和故事数据
      // const [actor, story] = await Promise.all([
      //   loadActor(characterId),
      //   loadStory(storyId)
      // ])
      
      // 暂时使用模拟数据
      currentActor.value = {
        id: characterId,
        name: 'Test Character',
        avatar_url: 'https://via.placeholder.com/100'
      }
      
      currentStory.value = {
        id: storyId,
        title: 'Test Story'
      }
      
      // TODO: 建立WebSocket连接
      // await initWebSocket()
      
      // 初始化场景为直播
      setCurrentScene('Living')
      
      console.log('🎮 Chat4Store: 游戏初始化成功')
    } catch (error) {
      console.error('❌ Chat4Store: 游戏初始化失败:', error)
    } finally {
      isLoading.value = false
    }
  }
  
  // ==================== 消息管理 ====================
  
  /**
   * 添加聊天消息
   */
  const addMessage = (message: Omit<ChatMessage, 'id' | 'timestamp'>) => {
    const newMessage: ChatMessage = {
      ...message,
      id: generateMessageId(),
      timestamp: Date.now()
    }
    
    messages.value.push(newMessage)
    
    // 限制消息数量
    if (messages.value.length > 100) {
      messages.value.shift()
    }
    
    console.log('💬 添加消息:', newMessage)
  }
  
  /**
   * 添加直播评论
   */
  const addLiveComment = (comment: Omit<LiveComment, 'id' | 'timestamp'>) => {
    const newComment: LiveComment = {
      ...comment,
      id: generateMessageId(),
      timestamp: Date.now()
    }
    
    liveComments.value.push(newComment)
    
    // 限制评论数量
    if (liveComments.value.length > 50) {
      liveComments.value.shift()
    }
    
    console.log('📺 添加直播评论:', newComment)
  }
  
  /**
   * 发送礼物消息
   */
  const sendGift = (gift: GiftItem, quantity: number = 1) => {
    addMessage({
      type: 'gift',
      content: `送出了 ${quantity} 个 ${gift.title}`,
      gift: { ...gift, quantity }
    })
    
    // 增加心形点击数
    heartClickCount.value += quantity
    
    console.log('🎁 发送礼物:', { gift, quantity })
  }
  
  // ==================== UI状态管理 ====================
  
  /**
   * 显示好友请求弹窗
   */
  const showFriendRequest = () => {
    showFriendRequestModal.value = true
  }
  
  /**
   * 显示礼物选择弹窗
   */
  const openGiftModal = () => {
    showGiftModal.value = true
  }
  
  /**
   * 增加心形点击数
   */
  const incrementHeartClick = () => {
    heartClickCount.value++
  }
  
  // ==================== 场景状态清理 ====================
  
  /**
   * 清理场景特定状态
   */
  const clearSceneSpecificState = (sceneId?: Chat4SceneState) => {
    console.log('🧹 清理场景状态:', sceneId)
    
    // 根据场景清理相应状态
    if (sceneId === 'Living') {
      // 清理直播相关状态
      liveComments.value = []
    } else if (sceneId === 'Phone') {
      // 清理聊天相关状态 (保留消息历史)
    }
    
    // 关闭所有模态框
    showFriendRequestModal.value = false
    showGiftModal.value = false
    showFavorabilityDrawer.value = false
    showPaymentModal.value = false
  }
  
  // ==================== WebSocket管理 ====================
  
  /**
   * 初始化WebSocket连接
   */
  const initWebSocket = async () => {
    if (process.client) {
      // TODO: 实现WebSocket连接
      console.log('🔌 WebSocket连接初始化 (待实现)')
    }
  }
  
  /**
   * 关闭WebSocket连接
   */
  const closeWebSocket = () => {
    if (wsConnection.value) {
      wsConnection.value.close()
      wsConnection.value = null
      isConnected.value = false
      console.log('🔌 WebSocket连接已关闭')
    }
  }
  
  // ==================== 重置方法 ====================
  
  /**
   * 重置所有状态
   */
  const resetAllState = () => {
    console.log('🔄 重置Chat4所有状态')
    
    // 重置场景状态
    currentScene.value = 'Living'
    isTransitioning.value = false
    isLoading.value = false
    
    // 重置游戏数据
    currentActor.value = null
    currentStory.value = null
    
    // 重置消息状态
    messages.value = []
    liveComments.value = []
    
    // 重置UI状态
    showFriendRequestModal.value = false
    showGiftModal.value = false
    showFavorabilityDrawer.value = false
    showPaymentModal.value = false
    heartClickCount.value = 0
    viewerCount.value = 128
    
    // 关闭WebSocket
    closeWebSocket()
  }
  
  // ==================== 工具方法 ====================
  
  /**
   * 生成消息ID
   */
  const generateMessageId = () => {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
  
  // ==================== 计算属性 ====================
  
  /**
   * 是否为直播场景
   */
  const isLivingScene = computed(() => currentScene.value === 'Living')
  
  /**
   * 当前场景显示名称
   */
  const currentSceneDisplayName = computed(() => {
    return Chat4SceneUtils.getSceneDisplayName(currentScene.value)
  })
  
  // ==================== 返回状态和方法 ====================
  
  return {
    // 状态 (只读)
    currentScene: readonly(currentScene),
    isTransitioning: readonly(isTransitioning),
    isLoading: readonly(isLoading),
    currentActor: readonly(currentActor),
    currentStory: readonly(currentStory),
    messages: readonly(messages),
    liveComments: readonly(liveComments),
    isConnected: readonly(isConnected),
    
    // UI状态 (可修改)
    showFriendRequestModal,
    showGiftModal,
    showFavorabilityDrawer,
    showPaymentModal,
    heartClickCount: readonly(heartClickCount),
    viewerCount: readonly(viewerCount),
    
    // 计算属性
    isLivingScene,
    currentSceneDisplayName,
    
    // 场景切换方法
    setCurrentScene,
    goToChat,
    goToVideo,
    goToLive,
    goToMap,
    goToMeetup,
    goToDancing,
    goToConcert,
    goToMoment,
    goToDiary,
    goToMonitor,
    
    // 游戏管理方法
    initGame,
    resetAllState,
    
    // 消息管理方法
    addMessage,
    addLiveComment,
    sendGift,
    
    // UI控制方法
    showFriendRequest,
    openGiftModal,
    incrementHeartClick,
    
    // WebSocket方法
    initWebSocket,
    closeWebSocket
  }
})